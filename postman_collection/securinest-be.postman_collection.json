{"info": {"_postman_id": "479d06a2-c9db-438f-b023-b9c98473c65c", "name": "securinest-be", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "12829168"}, "item": [{"name": "api_key", "item": [{"name": "addApiKey", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"label\": \"My Service Key\",\r\n  \"hashed_token\": \"aacaa\",\r\n  \"tenant_id\": 1,\r\n  \"user_id\": 1,\r\n  \"project_id\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8000/api_key", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["api_key"]}}, "response": []}, {"name": "updateApiKey", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"label\": \"My  Service Key\",\r\n  \"hashed_token\": \"aaaaa\",\r\n  \"tenant_id\": 1,\r\n  \"user_id\": 1,\r\n  \"project_id\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8000/api_key/4", "host": ["localhost"], "port": "8000", "path": ["api_key", "4"]}}, "response": []}, {"name": "getApiKeyById", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8000/api_keys/4", "host": ["localhost"], "port": "8000", "path": ["api_keys", "4"]}}, "response": []}, {"name": "getAllApiKeys", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8000/api_keys", "host": ["localhost"], "port": "8000", "path": ["api_keys"]}}, "response": []}, {"name": "deleteApiKeyById", "request": {"method": "DELETE", "header": [], "url": {"raw": "localhost:8000/api_keys/13", "host": ["localhost"], "port": "8000", "path": ["api_keys", "13"]}}, "response": []}]}, {"name": "log_entry", "item": [{"name": "getAllLogEntries", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "localhost:8000/log-entries", "host": ["localhost"], "port": "8000", "path": ["log-entries"]}}, "response": []}, {"name": "getLogEntryById", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "localhost:8000/log-entries/1", "host": ["localhost"], "port": "8000", "path": ["log-entries", "1"]}}, "response": []}, {"name": "createLogEntry", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"request_id\": \"id\",\r\n    \"api_key_id\": 1,\r\n    \"user_id\": 1,\r\n    \"tenant_id\": 1,\r\n    \"model\": \"open ai\",\r\n    \"outcome\": \"allowed\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8000/log-entries", "host": ["localhost"], "port": "8000", "path": ["log-entries"]}}, "response": []}, {"name": "updateLogEntry", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"request_id\": \"id\",\r\n    \"api_key_id\": 1,\r\n    \"user_id\": 1,\r\n    \"tenant_id\": 1,\r\n    \"model\": \"gemini\",\r\n    \"outcome\": \"allowed\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8000/log-entries/1", "host": ["localhost"], "port": "8000", "path": ["log-entries", "1"]}}, "response": []}, {"name": "deleteLogEntryById", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [], "url": {"raw": "localhost:8000/log-entries/4", "host": ["localhost"], "port": "8000", "path": ["log-entries", "4"]}}, "response": []}]}, {"name": "tenant", "item": [{"name": "getTenantById", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8000/tenants/1", "host": ["localhost"], "port": "8000", "path": ["tenants", "1"]}}, "response": []}, {"name": "getAllTenants", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8000/tenants", "host": ["localhost"], "port": "8000", "path": ["tenants"]}}, "response": []}, {"name": "add<PERSON><PERSON>t", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"Acme Corp\",\r\n  \"industry\": \"Finance\",\r\n  \"region_preference\": \"eu-west\",\r\n  \"subscription_level\": \"enterprise\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8000/tenants", "host": ["localhost"], "port": "8000", "path": ["tenants"]}}, "response": []}, {"name": "updateTenant", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"Acme Corp\",\r\n  \"industry\": \"Finance\",\r\n  \"region_preference\": \"eu-north\",\r\n  \"subscription_level\": \"enterprise\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8000/tenants/1", "host": ["localhost"], "port": "8000", "path": ["tenants", "1"]}}, "response": []}, {"name": "deleteTenantById", "request": {"method": "DELETE", "header": [], "url": {"raw": "localhost:8000/tenants/1", "host": ["localhost"], "port": "8000", "path": ["tenants", "1"]}}, "response": []}]}, {"name": "helloWorld", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "localhost:8000/", "host": ["localhost"], "port": "8000", "path": [""]}}, "response": []}]}