from typing import Optional, TYPE_CHECKING
from sqlmodel import SQLModel, Field, Relationship

if TYPE_CHECKING:
    from src.app.dao.entity.policy import Policy
    from src.app.dao.entity.tenant import Tenant


class Rule(SQLModel, table=True):
    __tablename__ = "rule"

    id: int = Field(primary_key=True)
    rule_id: str = Field(index=True, description="Rule identifier from YAML")
    name: str = Field(description="Human-readable rule name")
    policy_id: int = Field(foreign_key="policy.id", index=True)
    tenant_id: int = Field(foreign_key="tenant.id", nullable=False, index=True)

    # Relationships
    policy: Optional["Policy"] = Relationship()
    tenant: Optional["Tenant"] = Relationship()
