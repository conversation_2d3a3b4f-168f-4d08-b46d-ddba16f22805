from datetime import datetime, timezone
from typing import Optional, TYPE_CHECKING

from sqlalchemy import LargeBinary, Column, DateTime
from sqlmodel import SQLModel, Field, Relationship


if TYPE_CHECKING:
    from src.app.dao.entity.tenant import Tenant
    from src.app.dao.entity.user import User
    from src.app.dao.entity.project import Project


class Policy(SQLModel, table=True):
    __tablename__ = "policy"

    id: int | None = Field(default=None, primary_key=True)
    name: str = Field(nullable=False, index=True)
    version: int = Field(default=1, nullable=False, description="Version number for the policy")
    status: str = Field(default="DRAFT", max_length=50)
    yaml_blob: bytes = Field(sa_column=Column(LargeBinary), description="The policy yaml blob")
    tenant_id: int = Field(foreign_key="tenant.id", nullable=False, index=True)
    tenant: "Tenant" = Relationship(back_populates="policies")
    project_id: Optional[int] = Field(default=None, foreign_key="project.id", index=True)
    project: Optional["Project"] = Relationship(back_populates="policies")
    last_edited_by_id: int | None = Field(default=None, foreign_key="user.id", index=True)
    last_edited_by: Optional["User"] = Relationship(back_populates="policies")
    created_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    )
    updated_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    )
