from typing import Optional, TYPE_CHECKING

from sqlmodel import SQLModel, Field, Relationship

if TYPE_CHECKING:
    from src.app.dao.entity.tenant import Tenant


class ProviderConfig(SQLModel, table=True):
    __tablename__ = "provider_config"

    id: Optional[int] = Field(default=None, primary_key=True)
    provider_type: str = Field(max_length=50, nullable=False, description="Which AI provider this refers to")
    credentials: str = Field(nullable=False, description="Encrypted credentials or JSON blob")
    default_model: Optional[str] = Field(default=None, description="E.g., 'gpt-4', 'text-davinci-003'")
    tenant_id: int = Field(foreign_key="tenant.id", nullable=False, index=True)
    tenant: "Tenant" = Relationship(back_populates="provider_configs")
