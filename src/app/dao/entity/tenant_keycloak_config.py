from datetime import datetime, timezone
from typing import Optional, TYPE_CHECKING
from sqlalchemy import Column, DateTime
from sqlmodel import SQLModel, Field, Relationship

if TYPE_CHECKING:
    from src.app.dao.entity.tenant import Tenant


class TenantKeycloakConfig(SQLModel, table=True):
    __tablename__ = "tenant_keycloak_config"

    id: Optional[int] = Field(default=None, primary_key=True)
    tenant_id: int = Field(foreign_key="tenant.id", nullable=False, index=True, unique=True)
    realm_name: str = Field(nullable=False, unique=True, index=True)
    client_id: str = Field(nullable=False)
    client_secret_hash: str = Field(nullable=False, description="Hashed Keycloak client secret for this tenant")
    
    created_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    )
    updated_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    )

    tenant: "Tenant" = Relationship(back_populates="keycloak_config")
