from typing import List, Optional, TYPE_CHECKING
from decimal import Decimal

from sqlmodel import SQLModel, Field, Relationship

if TYPE_CHECKING:
    from src.app.dao.entity.subscription import Subscription


class Plan(SQLModel, table=True):
    __tablename__ = "plan"

    id: Optional[int] = Field(default=None, primary_key=True)
    code: str = Field(nullable=False, index=True, unique=True, max_length=50)
    monthly_price: Decimal = Field(nullable=False, decimal_places=2, max_digits=10)
    requests_per_day: int = Field(nullable=False)
    trial_days: int = Field(nullable=False, default=14)
    max_allowed_users: int = Field(nullable=False)
    audit_report: bool = Field(nullable=False, default=False)
    max_allowed_rules: int = Field(nullable=False)
    analytics_dashboard: bool = Field(nullable=False, default=False)
    advanced_api: bool = Field(nullable=False, default=False)

    # Relationships
    subscriptions: List["Subscription"] = Relationship(back_populates="plan")
