from datetime import datetime
from typing import Optional, List, TYPE_CHECKING

from sqlalchemy import <PERSON>SO<PERSON>, Column
from sqlmodel import Field, Relationship, SQLModel

if TYPE_CHECKING:
    from src.app.dao.entity.api_key import ApiKey
    from src.app.dao.entity.tenant import Tenant
    from src.app.dao.entity.user import User


class LogEntry(SQLModel, table=True):
    __tablename__ = "log_entry"

    id: int | None = Field(default=None, primary_key=True)
    timestamp: datetime = Field(default_factory=lambda: datetime.now(), nullable=False, index=True)
    request_id: str = Field(nullable=False, index=True, description="Unique request identifier")
    api_key_id: Optional[int] = Field(default=None, foreign_key="api_key.id", index=True)
    api_key: Optional["ApiKey"] = Relationship(back_populates="log_entries")
    user_id: Optional[int] = Field(default=None, foreign_key="user.id", index=True)
    user: Optional["User"] = Relationship(back_populates="log_entries")
    tenant_id: int = Field(foreign_key="tenant.id", nullable=False, index=True)
    tenant: "Tenant" = Relationship(back_populates="log_entries")
    model: str = Field(nullable=False, description="E.g., 'gpt-4-turbo'")
    prompt_text: str | None = Field(default=None, description="Truncated prompt or reference ID")
    outcome: str = Field(max_length=50, nullable=False, description="ALLOWED, BLOCKED, or REDACTED")
    rule_triggers: Optional[List[str]] = Field(
        sa_column=Column(JSON),
        default=None,
        description="E.g., ['rule_1', 'rule_42']"
    )
    provider_latency_ms: Optional[int] = Field(
        default=None,
        description="ms between request to AI provider and response"
    )
    prompt_tokens: Optional[int] = Field(default=None)
    completion_tokens: Optional[int] = Field(default=None)
    masked_response: Optional[str] = Field(default=None, description="Truncated or masked output")
