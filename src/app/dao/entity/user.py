from datetime import datetime, timezone
from typing import List, Optional, TYPE_CHECKING
from sqlalchemy import Column, DateTime
from sqlmodel import SQLModel, Field, Relationship

if TYPE_CHECKING:
    from src.app.dao.entity.api_key import A<PERSON><PERSON><PERSON>
    from src.app.dao.entity.log_entry import LogEntry
    from src.app.dao.entity.policy import Policy
    from src.app.dao.entity.tenant import Tenant


class User(SQLModel, table=True):
    __tablename__ = "user"

    id: Optional[int] = Field(default=None, primary_key=True)
    email: str = Field(nullable=False, unique=True, index=True)
    first_name: str
    last_name: str
    role: str = Field(
        default="PLATFORM_ADMIN",
        description="PLATFORM_ADMIN, TENANT_ADMIN, POLICY_EDITOR, DEVELOPER, AUDITOR, SUPPORT_AGENT"
    )
    is_active: bool = Field(default=True)
    tenant_id: int = Field(foreign_key="tenant.id", nullable=False, index=True)

    password_hash: str = Field(default=None, description="Hashed password for local authentication")
    email_verified: bool = Field(default=False, nullable=False, description="Whether user email is verified")
    email_verification_token: Optional[str] = Field(default=None, description="Email verification token")
    email_verification_expires: Optional[datetime] = Field(
        sa_column=Column(DateTime(timezone=True), nullable=True),
        default=None,
        description="Email verification token expiration"
    )

    tenant: "Tenant" = Relationship(back_populates="users")
    created_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    )
    updated_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    )
    api_keys: List["ApiKey"] = Relationship(back_populates="user")
    policies: List["Policy"] = Relationship(back_populates="last_edited_by")
    log_entries: List["LogEntry"] = Relationship(back_populates="user")