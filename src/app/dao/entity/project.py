from typing import List, TYPE_CHECKING

from sqlmodel import SQLModel, Field, Relationship

if TYPE_CHECKING:
    from src.app.dao.entity.api_key import ApiKey
    from src.app.dao.entity.policy import Policy
    from src.app.dao.entity.tenant import Tenant


class Project(SQLModel, table=True):
    __tablename__ = "project"

    id: int | None = Field(default=None, primary_key=True)
    name: str = Field(nullable=False, index=True)
    description: str | None = Field(default=None)
    tenant_id: int = Field(foreign_key="tenant.id", nullable=False, index=True)
    tenant: "Tenant" = Relationship(back_populates="projects")
    api_keys: List["ApiKey"] = Relationship(back_populates="project")
    policies: List["Policy"] = Relationship(back_populates="project")
