from typing import Optional, TYPE_CHECKING
from datetime import datetime, timezone

from sqlalchemy import Column, DateTime
from sqlmodel import SQLModel, Field, Relationship

if TYPE_CHECKING:
    from src.app.dao.entity.subscription import Subscription


class UsageRecord(SQLModel, table=True):
    __tablename__ = "usage_record"

    id: Optional[int] = Field(default=None, primary_key=True)
    subscription_id: int = Field(foreign_key="subscription.id", nullable=False, index=True)
    metric: str = Field(nullable=False, max_length=50, index=True)  # API_REQUESTS, TOKENS_PROCESSED, STORAGE_USED
    used: int = Field(nullable=False, description="Amount of the metric used")
    recorded_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), nullable=False),
        default_factory=lambda: datetime.now(timezone.utc)
    )

    # Relationships
    subscription: "Subscription" = Relationship(back_populates="usage_records")
