from typing import Optional, TYPE_CHECKING

from sqlmodel import SQLModel, Field, Relationship

if TYPE_CHECKING:
    from src.app.dao.entity.tenant import Tenant


class Payment(SQLModel, table=True):
    __tablename__ = "payment"

    id: Optional[int] = Field(default=None, primary_key=True)
    tenant_id: int = Field(foreign_key="tenant.id", nullable=False, index=True)
    is_default: bool = Field(nullable=False, default=False)
    stripe_payment_method_id: str = Field(nullable=False, max_length=255, index=True)

    # Relationships
    tenant: "Tenant" = Relationship(back_populates="payments")
