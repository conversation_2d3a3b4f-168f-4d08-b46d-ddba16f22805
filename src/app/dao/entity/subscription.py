from typing import List, Optional, TYPE_CHECKING
from datetime import datetime, timezone

from sqlalchemy import Column, DateTime
from sqlmodel import SQLModel, Field, Relationship

if TYPE_CHECKING:
    from src.app.dao.entity.tenant import Tenant
    from src.app.dao.entity.plan import Plan
    from src.app.dao.entity.usage_record import UsageRecord


class Subscription(SQLModel, table=True):
    __tablename__ = "subscription"

    id: Optional[int] = Field(default=None, primary_key=True)
    tenant_id: int = Field(foreign_key="tenant.id", nullable=False, index=True)
    plan_id: int = Field(foreign_key="plan.id", nullable=False, index=True)
    status: str = Field(nullable=False, max_length=20, index=True)
    started_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), nullable=False)
    )
    ends_at: Optional[datetime] = Field(
        sa_column=Column(DateTime(timezone=True), nullable=True)
    )
    trial_ends_at: Optional[datetime] = Field(
        sa_column=Column(DateTime(timezone=True), nullable=True)
    )
    stripe_subscription_id: Optional[str] = Field(nullable=True, max_length=255, index=True)

    # Relationships
    tenant: "Tenant" = Relationship(back_populates="subscriptions")
    plan: "Plan" = Relationship(back_populates="subscriptions")
    usage_records: List["UsageRecord"] = Relationship(back_populates="subscription")
