from datetime import datetime, timezone
from typing import List, Optional, TYPE_CHECKING

from sqlalchemy.sql.schema import Column
from sqlalchemy.sql.sqltypes import DateTime
from sqlmodel import SQLModel, Field, Relationship

if TYPE_CHECKING:
    from src.app.dao.entity.tenant import Tenant
    from src.app.dao.entity.user import User
    from src.app.dao.entity.log_entry import LogEntry
    from src.app.dao.entity.project import Project


class ApiKey(SQLModel, table=True):
    __tablename__ = "api_key"

    id: Optional[int] = Field(default=None, primary_key=True)
    hashed_token: str = Field(nullable=False, unique=True, index=True)
    label: Optional[str] = Field(default=None, description="Human‐friendly name/label")
    creation_date: datetime = Field(
        sa_column=Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    )
    last_used: Optional[datetime] = Field(default=None, description="Last time this key was used")
    status: str = Field(default="ACTIVE", max_length=50)
    tenant_id: int = Field(foreign_key="tenant.id", nullable=False, index=True)
    tenant: "Tenant" = Relationship(back_populates="api_keys")
    user_id: Optional[int] = Field(default=None, foreign_key="user.id", index=True)
    user: Optional["User"] = Relationship(back_populates="api_keys")
    project_id: Optional[int] = Field(default=None, foreign_key="project.id", index=True)
    project: Optional["Project"] = Relationship(back_populates="api_keys")
    updated_at: datetime = Field(
        sa_column=Column(
            DateTime(timezone=True),
            nullable=False,
            default=lambda: datetime.now(timezone.utc),
            onupdate=lambda: datetime.now(timezone.utc)
        )
    )
    log_entries: List["LogEntry"] = Relationship(back_populates="api_key")
