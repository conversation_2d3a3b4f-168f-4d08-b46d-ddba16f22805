import asyncio
import os
import sys
from logging.config import fileConfig

from alembic import context
from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    create_async_engine,
)
from sqlmodel import SQLModel

HERE = os.path.abspath(os.path.dirname(__file__))
PROJECT_ROOT = os.path.abspath(os.path.join(HERE, ".."))
sys.path.insert(0, PROJECT_ROOT)

from src.app.dao.entity import ApiKey, LogEntry, NotificationEvent, Policy, Project, ProviderConfig, Rule, Tenant, User
from src.app.core.constant.constants import DatabaseConfig

config = context.config

if config.config_file_name is not None:
    fileConfig(config.config_file_name)

target_metadata = SQLModel.metadata

naming_convention = {
    "ix": "ix_%(table_name)s_%(column_0_label)s",
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}


def run_migrations_offline() -> None:
    url = os.getenv(DatabaseConfig.DATABASE_URL.value) or config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        naming_convention=naming_convention
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    async def do_run_migrations() -> None:
        url = os.getenv(DatabaseConfig.DATABASE_URL.value) or config.get_main_option("sqlalchemy.url")
        async_engine: AsyncEngine = create_async_engine(
            url,
            echo=True,
            future=True,
        )

        async with async_engine.connect() as connection:
            await connection.run_sync(_run_migrations_sync)

        await async_engine.dispose()

    def _run_migrations_sync(connection):
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            compare_type=True,
            naming_convention=naming_convention,
            render_as_batch=True,
        )
        with context.begin_transaction():
            context.run_migrations()

    asyncio.run(do_run_migrations())


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
