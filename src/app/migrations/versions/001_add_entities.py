"""Initial database schema

Revision ID: 001_add_entities
Revises: 
Create Date: 2025-06-07 18:25:00.000000

"""
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = '001_add_entities'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table('tenant',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('organization_name', sa.String(), nullable=False),
        sa.Column('industry', sa.String(), nullable=True),
        sa.Column('region_preference', sa.String(50), nullable=False),
        sa.Column('subscription_level', sa.String(50), nullable=False),
        sa.Column('status', sa.String(20), nullable=False, server_default='PENDING'),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        schema='public'
    )
    op.create_index('ix_tenant_organization_name', 'tenant', ['organization_name'])


    op.create_table('user',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('email', sa.String(), nullable=False),
        sa.Column('first_name', sa.String(), nullable=False),
        sa.Column('last_name', sa.String(), nullable=False),
        sa.Column('role', sa.String(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('tenant_id', sa.Integer(), nullable=False),
        sa.Column('password_hash', sa.String(), nullable=True),
        sa.Column('email_verified', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('email_verification_token', sa.String(), nullable=True),
        sa.Column('email_verification_expires', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id']),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_user_email', 'user', ['email'], unique=True)
    op.create_index('ix_user_tenant_id', 'user', ['tenant_id'])


    op.create_table('project',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.String(), nullable=True),
        sa.Column('tenant_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id']),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_project_name', 'project', ['name'])
    op.create_index('ix_project_tenant_id', 'project', ['tenant_id'])

    
    op.create_table('provider_config',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('provider_type', sa.String(50), nullable=False),
        sa.Column('credentials', sa.String(), nullable=False),
        sa.Column('default_model', sa.String(), nullable=True),
        sa.Column('tenant_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id']),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_provider_config_tenant_id', 'provider_config', ['tenant_id'])

    
    op.create_table('api_key',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('hashed_token', sa.String(), nullable=False),
        sa.Column('label', sa.String(), nullable=True),
        sa.Column('creation_date', sa.DateTime(), nullable=False),
        sa.Column('last_used', sa.DateTime(), nullable=True),
        sa.Column('status', sa.String(50), nullable=False),
        sa.Column('tenant_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('project_id', sa.Integer(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['project_id'], ['project.id']),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id']),
        sa.ForeignKeyConstraint(['user_id'], ['user.id']),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_api_key_hashed_token', 'api_key', ['hashed_token'], unique=True)
    op.create_index('ix_api_key_project_id', 'api_key', ['project_id'])
    op.create_index('ix_api_key_tenant_id', 'api_key', ['tenant_id'])
    op.create_index('ix_api_key_user_id', 'api_key', ['user_id'])

    
    op.create_table('policy',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('version', sa.Integer(), nullable=False),
        sa.Column('status', sa.String(50), nullable=False),
        sa.Column('yaml_blob', sa.LargeBinary(), nullable=True),
        sa.Column('tenant_id', sa.Integer(), nullable=False),
        sa.Column('project_id', sa.Integer(), nullable=True),
        sa.Column('last_edited_by_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['last_edited_by_id'], ['user.id']),
        sa.ForeignKeyConstraint(['project_id'], ['project.id']),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id']),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_policy_last_edited_by_id', 'policy', ['last_edited_by_id'])
    op.create_index('ix_policy_name', 'policy', ['name'])
    op.create_index('ix_policy_project_id', 'policy', ['project_id'])
    op.create_index('ix_policy_tenant_id', 'policy', ['tenant_id'])

    
    op.create_table('rule',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('rule_id', sa.String(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('policy_id', sa.Integer(), nullable=False),
        sa.Column('tenant_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['policy_id'], ['policy.id']),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id']),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_rule_policy_id', 'rule', ['policy_id'])
    op.create_index('ix_rule_rule_id', 'rule', ['rule_id'])
    op.create_index('ix_rule_tenant_id', 'rule', ['tenant_id'])

    
    op.create_table('log_entry',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('timestamp', sa.DateTime(), nullable=False),
        sa.Column('request_id', sa.String(), nullable=False),
        sa.Column('api_key_id', sa.Integer(), nullable=True),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('tenant_id', sa.Integer(), nullable=False),
        sa.Column('model', sa.String(), nullable=False),
        sa.Column('prompt_text', sa.String(), nullable=True),
        sa.Column('outcome', sa.String(50), nullable=False),
        sa.Column('rule_triggers', sa.JSON(), nullable=True),
        sa.Column('provider_latency_ms', sa.Integer(), nullable=True),
        sa.Column('prompt_tokens', sa.Integer(), nullable=True),
        sa.Column('completion_tokens', sa.Integer(), nullable=True),
        sa.Column('masked_response', sa.String(), nullable=True),
        sa.ForeignKeyConstraint(['api_key_id'], ['api_key.id']),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id']),
        sa.ForeignKeyConstraint(['user_id'], ['user.id']),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_log_entry_api_key_id', 'log_entry', ['api_key_id'])
    op.create_index('ix_log_entry_request_id', 'log_entry', ['request_id'])
    op.create_index('ix_log_entry_tenant_id', 'log_entry', ['tenant_id'])
    op.create_index('ix_log_entry_timestamp', 'log_entry', ['timestamp'])
    op.create_index('ix_log_entry_user_id', 'log_entry', ['user_id'])

    
    op.create_table('notification_event',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('event_type', sa.String(50), nullable=False),
        sa.Column('details', sa.String(), nullable=True),
        sa.Column('sent_to', sa.String(), nullable=True),
        sa.Column('timestamp', sa.DateTime(), nullable=False),
        sa.Column('tenant_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('api_key_id', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['api_key_id'], ['api_key.id']),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id']),
        sa.ForeignKeyConstraint(['user_id'], ['user.id']),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_notification_event_api_key_id', 'notification_event', ['api_key_id'])
    op.create_index('ix_notification_event_tenant_id', 'notification_event', ['tenant_id'])
    op.create_index('ix_notification_event_user_id', 'notification_event', ['user_id'])


def downgrade() -> None:
    op.drop_table('notification_event')
    op.drop_table('log_entry')
    op.drop_table('rule')
    op.drop_table('policy')
    op.drop_table('api_key')
    op.drop_table('provider_config')
    op.drop_table('project')
    op.drop_table('user')
    op.drop_table('tenant')