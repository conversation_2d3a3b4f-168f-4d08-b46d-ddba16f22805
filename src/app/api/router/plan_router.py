from typing import List
from fastapi import APIRouter, Depends, HTTPException

from src.app.api.schema.plan.plan_create_request import PlanCreateRequest
from src.app.api.schema.plan.plan_create_response import PlanCreateResponse
from src.app.api.schema.plan.plan_read_response import PlanReadResponse
from src.app.api.schema.plan.plan_update_request import PlanUpdateRequest
from src.app.api.schema.plan.plan_update_response import PlanUpdateResponse
from src.app.api.service.plan_service import get_plan_service, PlanService
from src.app.core.routes.app_routes import ResourceRoute, ResourceTag, PathParam
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.core.exception.processing_exception import ProcessingException

router = APIRouter(
    prefix=ResourceRoute.PLAN.value,
    tags=[ResourceTag.PLAN.value]
)


@router.post("/", response_model=PlanCreateResponse)
async def create_plan(
    payload: PlanCreateRequest,
    service: PlanService = Depends(get_plan_service)
) -> PlanCreateResponse:
    """Create a new plan."""
    try:
        return await service.create_plan(payload)
    except ProcessingException as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get(PathParam.PLAN_ID, response_model=PlanReadResponse)
async def get_plan(
    plan_id: int,
    service: PlanService = Depends(get_plan_service)
) -> PlanReadResponse:
    """Get a plan by ID."""
    try:
        return await service.get_plan_by_id(plan_id)
    except NotFoundException as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/", response_model=List[PlanReadResponse])
async def get_all_plans(
    service: PlanService = Depends(get_plan_service)
) -> List[PlanReadResponse]:
    """Get all plans."""
    try:
        return await service.get_all_plans()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.put(PathParam.PLAN_ID, response_model=PlanUpdateResponse)
async def update_plan(
    plan_id: int,
    payload: PlanUpdateRequest,
    service: PlanService = Depends(get_plan_service)
) -> PlanUpdateResponse:
    """Update a plan."""
    try:
        return await service.update_plan(plan_id, payload)
    except NotFoundException as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ProcessingException as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.delete(PathParam.PLAN_ID)
async def delete_plan(
    plan_id: int,
    service: PlanService = Depends(get_plan_service)
) -> dict:
    """Delete a plan."""
    try:
        success = await service.delete_plan(plan_id)
        if success:
            return {"message": f"Plan {plan_id} deleted successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to delete plan")
    except NotFoundException as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
