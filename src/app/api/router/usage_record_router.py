from typing import List
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from src.app.api.schema.usage_record.usage_record_create_request import UsageRecordCreateRequest
from src.app.api.schema.usage_record.usage_record_create_response import UsageRecordCreateResponse
from src.app.api.schema.usage_record.usage_record_read_response import UsageRecordReadResponse
from src.app.api.schema.usage_record.usage_record_update_request import UsageRecordUpdateRequest
from src.app.api.schema.usage_record.usage_record_update_response import UsageRecordUpdateResponse
from src.app.api.service.usage_record_service import get_usage_record_service, UsageRecordService
from src.app.core.routes.app_routes import ResourceRoute, ResourceTag, PathParam
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.core.exception.processing_exception import ProcessingException

router = APIRouter(
    prefix=ResourceRoute.USAGE_RECORD.value,
    tags=[ResourceTag.USAGE_RECORD.value]
)


@router.post("/", response_model=UsageRecordCreateResponse)
async def create_usage_record(
    payload: UsageRecordCreateRequest,
    service: UsageRecordService = Depends(get_usage_record_service)
) -> UsageRecordCreateResponse:
    """Create a new usage record."""
    try:
        return await service.create_usage_record(payload)
    except ProcessingException as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get(PathParam.USAGE_RECORD_ID, response_model=UsageRecordReadResponse)
async def get_usage_record(
    usage_record_id: int,
    service: UsageRecordService = Depends(get_usage_record_service)
) -> UsageRecordReadResponse:
    """Get a usage record by ID."""
    try:
        return await service.get_usage_record_by_id(usage_record_id)
    except NotFoundException as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/subscription/{subscription_id}", response_model=List[UsageRecordReadResponse])
async def get_usage_records_by_subscription(
    subscription_id: int,
    service: UsageRecordService = Depends(get_usage_record_service)
) -> List[UsageRecordReadResponse]:
    """Get all usage records for a subscription."""
    try:
        return await service.get_usage_records_by_subscription(subscription_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/subscription/{subscription_id}/metric/{metric}", response_model=List[UsageRecordReadResponse])
async def get_usage_records_by_metric(
    subscription_id: int,
    metric: str,
    service: UsageRecordService = Depends(get_usage_record_service)
) -> List[UsageRecordReadResponse]:
    """Get usage records for a specific subscription and metric."""
    try:
        return await service.get_usage_records_by_metric(subscription_id, metric)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/", response_model=List[UsageRecordReadResponse])
async def get_all_usage_records(
    limit: int = Query(default=100, ge=1, le=1000),
    offset: int = Query(default=0, ge=0),
    service: UsageRecordService = Depends(get_usage_record_service)
) -> List[UsageRecordReadResponse]:
    """Get all usage records with pagination."""
    try:
        return await service.get_all_usage_records(limit=limit, offset=offset)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.put(PathParam.USAGE_RECORD_ID, response_model=UsageRecordUpdateResponse)
async def update_usage_record(
    usage_record_id: int,
    payload: UsageRecordUpdateRequest,
    service: UsageRecordService = Depends(get_usage_record_service)
) -> UsageRecordUpdateResponse:
    """Update a usage record."""
    try:
        return await service.update_usage_record(usage_record_id, payload)
    except NotFoundException as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ProcessingException as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.delete(PathParam.USAGE_RECORD_ID)
async def delete_usage_record(
    usage_record_id: int,
    service: UsageRecordService = Depends(get_usage_record_service)
) -> dict:
    """Delete a usage record."""
    try:
        success = await service.delete_usage_record(usage_record_id)
        if success:
            return {"message": f"Usage record {usage_record_id} deleted successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to delete usage record")
    except NotFoundException as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
