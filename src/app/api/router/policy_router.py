from typing import List

from fastapi import APIRouter, Depends, status

from src.app.api.schema.policy.policy_create_request import PolicyCreateRequest
from src.app.api.schema.policy.policy_create_response import PolicyCreateResponse
from src.app.api.schema.policy.policy_read_response import PolicyReadResponse
from src.app.api.schema.policy.policy_update_request import PolicyUpdateRequest
from src.app.api.schema.policy.policy_update_response import PolicyUpdateResponse
from src.app.api.service.policy_service import get_policy_service, PolicyService
from src.app.core.constant.constants import UserRole
from src.app.core.model.user_context import UserContext
from src.app.core.routes.app_routes import PathParam, ResourceRoute, ResourceTag
from src.app.core.security.keycloak_auth import require_roles
from src.app.core.service.user_context_service import get_current_user

router = APIRouter(prefix=ResourceRoute.POLICY, tags=[ResourceTag.POLICY])


@router.post(
    "",
    response_model=PolicyCreateResponse,
    status_code=status.HTTP_201_CREATED,
)
@require_roles(UserRole.POLICY_EDITOR, UserRole.TENANT_ADMIN)
async def create_policy(
        *,
        service: PolicyService = Depends(get_policy_service),
        payload: PolicyCreateRequest,
        current_user: UserContext = Depends(get_current_user)
):
    return await service.create_policy(payload, current_user)


@router.get(
    PathParam.POLICY_ID,
    response_model=PolicyReadResponse,
    status_code=status.HTTP_200_OK,
)
async def get_policy_by_id(
        *,
        service: PolicyService = Depends(get_policy_service),
        policy_id: int
):
    return await service.get_policy_by_id(policy_id)


@router.get(
    "",
    response_model=List[PolicyReadResponse],
    status_code=status.HTTP_200_OK,
)
async def get_policies(
    *,
    service: PolicyService = Depends(get_policy_service),
):
    return await service.get_policies()


@router.put(
    PathParam.POLICY_ID,
    response_model=PolicyUpdateResponse,
    status_code=status.HTTP_200_OK,
)
async def update_policy(
        *,
        service: PolicyService = Depends(get_policy_service),
        policy_id: int,
        payload: PolicyUpdateRequest
):
    return await service.update_policy(policy_id, payload)


@router.delete(
    PathParam.POLICY_ID,
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_policy(
        *,
        service: PolicyService = Depends(get_policy_service),
        policy_id: int
):
    return await service.delete_policy(policy_id)
