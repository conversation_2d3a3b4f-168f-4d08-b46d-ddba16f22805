from typing import List

from fastapi import APIRouter, Depends, status

from src.app.api.schema.api_key.create_api_key_request import CreateApiKeyRequest
from src.app.api.schema.api_key.create_api_key_response import CreateApiKeyResponse
from src.app.api.schema.api_key.read_api_key_response import ReadApiKeyResponse
from src.app.api.schema.api_key.update_api_key_request import UpdateApiKeyRequest
from src.app.api.schema.api_key.update_api_key_response import UpdateApiKeyResponse
from src.app.api.service.api_key_service import get_api_key_service, ApiKeyService
from src.app.core.constant.constants import UserRole
from src.app.core.model.user_context import UserContext
from src.app.core.routes.app_routes import PathParam, ResourceRoute, ResourceTag
from src.app.core.security.keycloak_auth import require_roles
from src.app.core.service.user_context_service import get_current_user

router = APIRouter(
    prefix=ResourceRoute.API_KEY,
    tags=[ResourceTag.API_KEY],
)


@router.post(
    "",
    response_model=CreateApiKeyResponse,
    status_code=status.HTTP_201_CREATED,
)
@require_roles(UserRole.DEVELOPER, UserRole.TENANT_ADMIN)
async def create_api_key(
        *,
        service: ApiKeyService = Depends(get_api_key_service),
        payload: CreateApiKeyRequest
):
    return await service.save_api_key(payload)

@router.get(
    PathParam.API_KEY_ID,
    response_model=ReadApiKeyResponse,
    status_code=status.HTTP_200_OK,
)
async def get_api_key_by_id(
        *,
        service: ApiKeyService = Depends(get_api_key_service),
        api_key_id: int
):
    return await service.get_api_key_by_id(api_key_id)

@router.get(
    "",
    response_model=List[ReadApiKeyResponse],
    status_code=status.HTTP_200_OK,
)
@require_roles(UserRole.DEVELOPER, UserRole.TENANT_ADMIN, UserRole.AUDITOR)
async def get_api_keys(
        *,
        service: ApiKeyService = Depends(get_api_key_service),
        current_user: UserContext = Depends(get_current_user)
):
    return await service.get_all_api_keys()

@router.put(
    PathParam.API_KEY_ID,
    response_model=UpdateApiKeyResponse,
    status_code=status.HTTP_200_OK,
)
async def update_api_key(
        *,
        service: ApiKeyService = Depends(get_api_key_service),
        api_key_id: int,
        payload: UpdateApiKeyRequest
):
    return await service.update_api_key(api_key_id, payload)

@router.delete(
    PathParam.API_KEY_ID,
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_api_key(
        *,
        service: ApiKeyService = Depends(get_api_key_service),
        api_key_id: int
):
    return await service.delete_api_key_by_id(api_key_id)
