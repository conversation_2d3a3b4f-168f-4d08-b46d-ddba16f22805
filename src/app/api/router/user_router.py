from typing import List

from fastapi import APIRouter, Depends, status

from src.app.api.schema.user.user_create_request import UserCreateRequest
from src.app.api.schema.user.user_create_response import UserCreateResponse
from src.app.api.schema.user.user_read_response import UserReadResponse
from src.app.api.schema.user.user_update_request import UserUpdateRequest
from src.app.api.schema.user.user_update_response import UserUpdateResponse
from src.app.api.service.user_service import get_user_service, UserService
from src.app.core.constant.constants import UserRole
from src.app.core.model.user_context import UserContext
from src.app.core.routes.app_routes import PathParam, ResourceRoute, ResourceTag
from src.app.core.security.keycloak_auth import require_roles
from src.app.core.service.user_context_service import get_current_user

router = APIRouter(prefix=ResourceRoute.USER, tags=[ResourceTag.USER])


@router.post(
    "",
    response_model=UserCreateResponse,
    status_code=status.HTTP_201_CREATED,
)
@require_roles(UserRole.TENANT_ADMIN)
async def create_user(
        *,
        service: UserService = Depends(get_user_service),
        payload: UserCreateRequest,
        current_user: UserContext = Depends(get_current_user)
):
    return await service.create_user(payload, current_user)


@router.get(
    PathParam.USER_ID,
    response_model=UserReadResponse,
    status_code=status.HTTP_200_OK,
)
async def get_user_by_id(
        *,
        service: UserService = Depends(get_user_service),
        user_id: int,
        current_user: UserContext = Depends(get_current_user)
):
    return await service.get_user_by_id(user_id, current_user)


@router.get(
    "",
    response_model=List[UserReadResponse],
    status_code=status.HTTP_200_OK,
)
async def get_users(
    *,
    service: UserService = Depends(get_user_service),
):
    return await service.get_users()


@router.patch(
    PathParam.USER_ID,
    response_model=UserUpdateResponse,
    status_code=status.HTTP_200_OK,
)
async def update_user(
        *,
        service: UserService = Depends(get_user_service),
        user_id: int,
        payload: UserUpdateRequest
):
    return await service.update_user(user_id, payload)


@router.delete(
    PathParam.USER_ID,
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_user(
        *,
        service: UserService = Depends(get_user_service),
        user_id: int
):
    return await service.delete_user(user_id)
