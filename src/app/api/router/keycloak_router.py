import logging
from typing import Dict, Any

from fastapi import APIRouter, Depends, status

from src.app.api.service.keycloak_service import get_keycloak_service, KeycloakService, get_health_check
from src.app.core.constant.constants import UserRole
from src.app.core.model.user_context import UserContext
from src.app.core.routes.app_routes import ResourceRoute, ResourceTag
from src.app.core.security.keycloak_auth import require_roles
from src.app.core.service.user_context_service import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix=ResourceRoute.KEYCLOAK,
    tags=[ResourceTag.KEYCLOAK],
)


@router.delete(
    "/tenant/{tenant_id}",
    status_code=status.HTTP_204_NO_CONTENT,
)
@require_roles(UserRole.TENANT_ADMIN)
async def delete_tenant_realm(
        tenant_id: str,
        *,
        service: KeycloakService = Depends(get_keycloak_service),
        current_user: UserContext = Depends(get_current_user)
):
    return await service.delete_tenant_realm(tenant_id, current_user)

@router.get(
    "/tenant/{tenant_id}/info",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
)
@require_roles(UserRole.TENANT_ADMIN)
async def get_tenant_realm_info(
    tenant_id: str,
    *,
    service: KeycloakService = Depends(get_keycloak_service),
    current_user: UserContext = Depends(get_current_user)
):
    return await service.get_realm_info(tenant_id, current_user)


@router.get(
    "/health",
    status_code=status.HTTP_200_OK,
)
async def keycloak_health_check(
    *,
    service: KeycloakService = Depends(get_keycloak_service)
):
    get_health_check()
