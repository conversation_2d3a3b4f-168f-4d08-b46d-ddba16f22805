from typing import List

from fastapi import APIRouter, Depends, HTTPException, status, Query

from src.app.api.schema.email.email_verification_response import EmailVerificationResponse
from src.app.api.schema.tenant.create_tenant_request import CreateTenantRequest
from src.app.api.schema.tenant.create_tenant_response import CreateTenantResponse
from src.app.api.schema.tenant.read_tenant_response import ReadTenantResponse
from src.app.api.schema.tenant.update_tenant_request import UpdateTenantRequest
from src.app.api.schema.tenant.update_tenant_response import UpdateTenantResponse
from src.app.api.service.tenant_service import get_tenant_service, TenantService
from src.app.core.constant.constants import Error, UserRole
from src.app.core.model.user_context import UserContext
from src.app.core.routes.app_routes import PathParam, ResourceRoute, ResourceTag
from src.app.core.security.keycloak_auth import require_roles
from src.app.core.service.email_service import get_email_service, EmailService
from src.app.core.service.user_context_service import get_current_user

router = APIRouter(
    prefix=ResourceRoute.TENANT,
    tags=[ResourceTag.TENANT],
)


@router.post(
    "",
    response_model=CreateTenantResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create Tenant",
    description="Secure tenant creation with email verification following auth-flow.md"
)
async def create_tenant(
        *,
        service: TenantService = Depends(get_tenant_service),
        payload: CreateTenantRequest
):
    try:
        result = await service.create_tenant(payload)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get(
    "/verify-email",
    response_model=EmailVerificationResponse,
    status_code=status.HTTP_200_OK,
    summary="Email Verification",
    description="Verify email and activate tenant following auth-flow.md"
)
async def verify_email(
        *,
        email_service: EmailService = Depends(get_email_service),
        token: str = Query(..., description="Email verification token from verification email")
):
    return await email_service.verify_email(token)


@router.get(
    PathParam.TENANT_ID,
    response_model=ReadTenantResponse,
    status_code=status.HTTP_200_OK,
)
async def get_tenant_by_id(
        *,
        service: TenantService = Depends(get_tenant_service),
        tenant_id: int,
        current_user: UserContext = Depends(get_current_user)
):
    try:
        if current_user.tenant_id != str(tenant_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied. You can only access your own tenant data."
            )

        result = await service.get_tenant_by_id(tenant_id)
        return result
    except HTTPException:
        raise
    except Exception as e:
        msg = str(e)
        if Error.NOT_FOUND in msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=msg
        )


@router.get(
    "",
    response_model=List[ReadTenantResponse],
    status_code=status.HTTP_200_OK,
)
@require_roles(UserRole.TENANT_ADMIN)
async def get_tenants(
    *,
    service: TenantService = Depends(get_tenant_service),
):
    return await service.get_all_tenants()


@router.put(
    PathParam.TENANT_ID,
    response_model=UpdateTenantResponse,
    status_code=status.HTTP_200_OK,
)
@require_roles(UserRole.TENANT_ADMIN)
async def update_tenant(
        *,
        service: TenantService = Depends(get_tenant_service),
        tenant_id: int,
        payload: UpdateTenantRequest,
        current_user: UserContext = Depends(get_current_user)
):
    try:
        if current_user.tenant_id != str(tenant_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied. You can only update your own tenant."
            )

        result = await service.update_tenant(tenant_id, payload)
        return result
    except HTTPException:
        raise
    except Exception as e:
        msg = str(e)
        if Error.NOT_FOUND in msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=msg
            )
        if Error.UNAUTHORIZED in msg.lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=msg
        )


@router.delete(
    PathParam.TENANT_ID,
    status_code=status.HTTP_204_NO_CONTENT,
)
@require_roles(UserRole.TENANT_ADMIN)
async def delete_tenant(
        *,
        service: TenantService = Depends(get_tenant_service),
        tenant_id: int,
        current_user: UserContext = Depends(get_current_user)
):
    try:
        if current_user.tenant_id != str(tenant_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied. You can only delete your own tenant."
            )

        await service.delete_tenant_by_id(tenant_id, current_user, cleanup_keycloak=True)
        return
    except HTTPException:
        raise
    except Exception as e:
        msg = str(e)
        if Error.NOT_FOUND in msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=msg
            )
        if Error.UNAUTHORIZED in msg.lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=msg
        )
