from typing import List

from fastapi import APIRouter, Depends, status

from src.app.api.schema.log_entry.create_log_entry_request import CreateLogEntryRequest
from src.app.api.schema.log_entry.create_log_entry_response import CreateLogEntryResponse
from src.app.api.schema.log_entry.read_log_entry_response import ReadLogEntryResponse
from src.app.api.schema.log_entry.update_log_entry_request import UpdateLogEntryRequest
from src.app.api.schema.log_entry.update_log_entry_response import UpdateLogEntryResponse
from src.app.api.service.log_entry_service import get_log_entry_service, LogEntryService
from src.app.core.routes.app_routes import PathParam, ResourceRoute, ResourceTag

router = APIRouter(prefix=ResourceRoute.LOG_ENTRY, tags=[ResourceTag.LOG_ENTRY])


@router.post(
    "",
    response_model=CreateLogEntryResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_log_entry(
        *,
        service: LogEntryService = Depends(get_log_entry_service),
        payload: CreateLogEntryRequest
):
    return await service.save_log_entry(payload)

@router.get(
    PathParam.LOG_ENTRY_ID,
    response_model=ReadLogEntryResponse,
    status_code=status.HTTP_200_OK,
)
async def get_log_entry_by_id(
        *,
        service: LogEntryService = Depends(get_log_entry_service),
        log_entry_id: int
):
    return await service.get_log_entry_by_id(log_entry_id)

@router.get(
    "",
    response_model=List[ReadLogEntryResponse],
    status_code=status.HTTP_200_OK,
)
async def get_log_entries(
    *,
    service: LogEntryService = Depends(get_log_entry_service),
):
    return await service.get_all_log_entries()


@router.put(
    PathParam.LOG_ENTRY_ID,
    response_model=UpdateLogEntryResponse,
    status_code=status.HTTP_200_OK,
)
async def update_log_entry(
        *,
        service: LogEntryService = Depends(get_log_entry_service),
        log_entry_id: int,
        payload: UpdateLogEntryRequest
):
    return await service.update_log_entry(log_entry_id, payload)

@router.delete(
    PathParam.LOG_ENTRY_ID,
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_log_entry(
        *,
        service: LogEntryService = Depends(get_log_entry_service),
        log_entry_id: int
):
        await service.delete_log_entry_by_id(log_entry_id)
