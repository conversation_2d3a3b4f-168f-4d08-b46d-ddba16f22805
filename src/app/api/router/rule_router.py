from typing import List

from fastapi import APIRouter, Depends, status

from src.app.api.schema.rule.rule_create_request import RuleCreateRequest
from src.app.api.schema.rule.rule_create_response import RuleCreateResponse
from src.app.api.schema.rule.rule_read_response import RuleReadResponse
from src.app.api.schema.rule.rule_update_request import RuleUpdateRequest
from src.app.api.schema.rule.rule_update_response import RuleUpdateResponse
from src.app.api.service.rule_service import get_rule_service, RuleService
from src.app.core.routes.app_routes import PathParam, ResourceRoute, ResourceTag

router = APIRouter(prefix=ResourceRoute.RULE, tags=[ResourceTag.RULE])


@router.post(
    "",
    response_model=RuleCreateResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_rule(
        *,
        service: RuleService = Depends(get_rule_service),
        payload: RuleCreateRequest
):
    return await service.create_rule(payload)


@router.get(
    PathParam.RULE_ID,
    response_model=RuleReadResponse,
    status_code=status.HTTP_200_OK,
)
async def get_rule_by_id(
        *,
        service: RuleService = Depends(get_rule_service),
        rule_id: int
):
    return await service.get_rule_by_id(rule_id)


@router.get(
    "",
    response_model=List[RuleReadResponse],
    status_code=status.HTTP_200_OK,
)
async def get_rules(
    *,
    service: RuleService = Depends(get_rule_service),
):
    return await service.get_rules()


@router.put(
    PathParam.RULE_ID,
    response_model=RuleUpdateResponse,
    status_code=status.HTTP_200_OK,
)
async def update_rule(
        *,
        service: RuleService = Depends(get_rule_service),
        rule_id: int,
        payload: RuleUpdateRequest
):
    return await service.update_rule(rule_id, payload)


@router.delete(
    PathParam.RULE_ID,
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_rule(
        *,
        service: RuleService = Depends(get_rule_service),
        rule_id: int
):
    return await service.delete_rule(rule_id)
