from typing import List

from fastapi import APIRouter, Depends, status

from src.app.api.schema.provider_config.provider_config_create_request import ProviderConfigCreateRequest
from src.app.api.schema.provider_config.provider_config_create_response import ProviderConfigCreateResponse
from src.app.api.schema.provider_config.provider_config_read_response import ProviderConfigReadResponse
from src.app.api.schema.provider_config.provider_config_update_request import ProviderConfigUpdateRequest
from src.app.api.schema.provider_config.provider_config_update_response import ProviderConfigUpdateResponse
from src.app.api.service.provider_config_service import get_provider_config_service, ProviderConfigService
from src.app.core.routes.app_routes import PathParam, ResourceRoute, ResourceTag

router = APIRouter(prefix=ResourceRoute.PROVIDER_CONFIG, tags=[ResourceTag.PROVIDER_CONFIG])


@router.post(
    "",
    response_model=ProviderConfigCreateResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_provider_config(
        *,
        service: ProviderConfigService = Depends(get_provider_config_service),
        payload: ProviderConfigCreateRequest
):
    return await service.create_provider_config(payload)


@router.get(
    PathParam.PROVIDER_CONFIG_ID,
    response_model=ProviderConfigReadResponse,
    status_code=status.HTTP_200_OK,
)
async def get_provider_config_by_id(
        *,
        service: ProviderConfigService = Depends(get_provider_config_service),
        provider_config_id: int
):
    return await service.get_provider_config_by_id(provider_config_id)


@router.get(
    "",
    response_model=List[ProviderConfigReadResponse],
    status_code=status.HTTP_200_OK,
)
async def get_provider_configs(
    *,
    service: ProviderConfigService = Depends(get_provider_config_service),
):
    return await service.get_provider_configs()


@router.put(
    PathParam.PROVIDER_CONFIG_ID,
    response_model=ProviderConfigUpdateResponse,
    status_code=status.HTTP_200_OK,
)
async def update_provider_config(
        *,
        service: ProviderConfigService = Depends(get_provider_config_service),
        provider_config_id: int,
        payload: ProviderConfigUpdateRequest
):
    return await service.update_provider_config(provider_config_id, payload)


@router.delete(
    PathParam.PROVIDER_CONFIG_ID,
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_provider_config(
        *,
        service: ProviderConfigService = Depends(get_provider_config_service),
        provider_config_id: int
):
        await service.delete_provider_config(provider_config_id)
