from typing import List

from fastapi import APIRouter, Depends, status

from src.app.api.schema.project.project_create_request import ProjectCreateRequest
from src.app.api.schema.project.project_create_response import ProjectCreateResponse
from src.app.api.schema.project.project_read_response import ProjectReadResponse
from src.app.api.schema.project.project_update_request import ProjectUpdateRequest
from src.app.api.schema.project.project_update_response import ProjectUpdateResponse
from src.app.api.service.project_service import get_project_service, ProjectService
from src.app.core.routes.app_routes import PathParam, ResourceRoute, ResourceTag

router = APIRouter(prefix=ResourceRoute.PROJECT, tags=[ResourceTag.PROJECT])


@router.post(
    "",
    response_model=ProjectCreateResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_project(
        *,
        service: ProjectService = Depends(get_project_service),
        payload: ProjectCreateRequest
):
    return await service.create_project(payload)


@router.get(
    PathParam.PROJECT_ID,
    response_model=ProjectReadResponse,
    status_code=status.HTTP_200_OK,
)
async def get_project_by_id(
        *,
        service: ProjectService = Depends(get_project_service),
        project_id: int
):
    return await service.get_project_by_id(project_id)


@router.get(
    "",
    response_model=List[ProjectReadResponse],
    status_code=status.HTTP_200_OK,
)
async def get_projects(
    *,
    service: ProjectService = Depends(get_project_service),
):
    return await service.get_projects()


@router.put(
    PathParam.PROJECT_ID,
    response_model=ProjectUpdateResponse,
    status_code=status.HTTP_200_OK,
)
async def update_project(
        *,
        service: ProjectService = Depends(get_project_service),
        project_id: int,
        payload: ProjectUpdateRequest
):
    return await service.update_project(project_id, payload)


@router.delete(
    PathParam.PROJECT_ID,
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_project(
        *,
        service: ProjectService = Depends(get_project_service),
        project_id: int
):
    await service.delete_project(project_id)
