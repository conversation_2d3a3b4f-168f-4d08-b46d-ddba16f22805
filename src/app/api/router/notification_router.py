from typing import List

from fastapi import APIRouter, Depends, status

from src.app.api.schema.notification.notification_create_request import NotificationCreateRequest
from src.app.api.schema.notification.notification_create_response import NotificationCreateResponse
from src.app.api.schema.notification.notification_read_response import NotificationReadResponse
from src.app.api.schema.notification.notification_update_request import NotificationUpdateRequest
from src.app.api.schema.notification.notification_update_response import NotificationUpdateResponse
from src.app.api.service.notification_service import get_notification_service, NotificationService
from src.app.core.routes.app_routes import PathParam, ResourceRoute, ResourceTag

router = APIRouter(prefix=ResourceRoute.NOTIFICATION, tags=[ResourceTag.NOTIFICATION])


@router.post(
    "",
    response_model=NotificationCreateResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_notification(
        *,
        service: NotificationService = Depends(get_notification_service),
        payload: NotificationCreateRequest
):
    return await service.create_notification(payload)


@router.get(
    PathParam.NOTIFICATION_ID,
    response_model=NotificationReadResponse,
    status_code=status.HTTP_200_OK,
)
async def get_notification_by_id(
        *,
        service: NotificationService = Depends(get_notification_service),
        notification_id: int
):
    return await service.get_notification_by_id(notification_id)


@router.get(
    "",
    response_model=List[NotificationReadResponse],
    status_code=status.HTTP_200_OK,
)
async def get_notifications(
    *,
    service: NotificationService = Depends(get_notification_service),
):
    return await service.get_notifications()


@router.put(
    PathParam.NOTIFICATION_ID,
    response_model=NotificationUpdateResponse,
    status_code=status.HTTP_200_OK,
)
async def update_notification(
        *,
        service: NotificationService = Depends(get_notification_service),
        notification_id: int,
        payload: NotificationUpdateRequest
):
    return await service.update_notification(notification_id, payload)


@router.delete(
    PathParam.NOTIFICATION_ID,
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_notification(
        *,
        service: NotificationService = Depends(get_notification_service),
        notification_id: int
):
    return await service.delete_notification(notification_id)
