import logging
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Request, Response, Query
from fastapi.responses import RedirectResponse

from src.app.api.service.auth_service import get_auth_service, AuthService
from src.app.api.schema.auth.login_request import LoginRequest
from src.app.api.schema.auth.login_response import LoginResponse
from src.app.api.schema.auth.callback_request import CallbackRequest
from src.app.api.schema.auth.callback_response import CallbackResponse
from src.app.api.schema.auth.refresh_token_request import RefreshTokenRequest
from src.app.api.schema.auth.refresh_token_response import RefreshTokenResponse
from src.app.api.schema.auth.logout_request import LogoutRequest
from src.app.api.schema.auth.logout_response import LogoutResponse
from src.app.core.routes.app_routes import ResourceRoute, ResourceTag
from src.app.core.exception.processing_exception import ProcessingException

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix=ResourceRoute.AUTH,
    tags=[ResourceTag.AUTH],
)


@router.post(
    "/login",
    response_model=LoginResponse,
    status_code=status.HTTP_200_OK,
    summary="Initiate OAuth2 Login",
    description="Initiate OAuth2 authorization code flow for tenant-specific authentication"
)
async def initiate_login(
    *,
    auth_service: AuthService = Depends(get_auth_service),
    payload: LoginRequest
) -> LoginResponse:
    try:
        logger.info(f"Login request for tenant: {payload.tenant_id}")
        result = await auth_service.initiate_login(payload)
        return result
    except ProcessingException as e:
        logger.error(f"Processing error in login: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message if e.message else str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error in login: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login initiation failed"
        )


@router.get(
    "/callback",
    response_model=CallbackResponse,
    status_code=status.HTTP_200_OK,
    summary="OAuth2 Callback Handler",
    description="Handle OAuth2 callback and exchange authorization code for tokens"
)
async def handle_callback(
    *,
    auth_service: AuthService = Depends(get_auth_service),
    response: Response,
    code: str = Query(..., description="Authorization code from Keycloak"),
    state: str = Query(..., description="OAuth2 state parameter"),
    error: str = Query(None, description="Error code if authentication failed"),
    error_description: str = Query(None, description="Error description")
) -> CallbackResponse:
    try:
        callback_request = CallbackRequest(
            code=code,
            state=state,
            error=error,
            error_description=error_description
        )
        
        logger.info("Processing OAuth2 callback")
        result = await auth_service.handle_callback(callback_request, response)
        return result
    except ProcessingException as e:
        logger.error(f"Processing error in callback: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message if e.message else str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error in callback: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Callback processing failed"
        )


@router.post(
    "/refresh",
    response_model=RefreshTokenResponse,
    status_code=status.HTTP_200_OK,
    summary="Refresh Access Token",
    description="Refresh access token using refresh token from httpOnly cookie"
)
async def refresh_token(
    *,
    auth_service: AuthService = Depends(get_auth_service),
    request: Request,
    response: Response,
    payload: RefreshTokenRequest
) -> RefreshTokenResponse:
    try:
        logger.info("Token refresh request")
        result = await auth_service.refresh_token(request, response)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in token refresh: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post(
    "/logout",
    response_model=LogoutResponse,
    status_code=status.HTTP_200_OK,
    summary="User Logout",
    description="Logout user and invalidate all tokens"
)
async def logout_user(
    *,
    auth_service: AuthService = Depends(get_auth_service),
    request: Request,
    response: Response,
    payload: LogoutRequest
) -> LogoutResponse:
    try:
        logger.info("Logout request")
        result = await auth_service.logout_user(request, response, payload)
        return result
    except Exception as e:
        logger.error(f"Unexpected error in logout: {e}")
        try:
            from src.app.core.util.auth_utils import AuthUtils
            from src.app.core.config.auth_config import auth_config
            
            cookie_settings = AuthUtils.get_cookie_settings()
            response.delete_cookie(
                key=auth_config.refresh_cookie_name,
                domain=cookie_settings.get('domain'),
                secure=cookie_settings.get('secure'),
                httponly=True,
                samesite=cookie_settings.get('samesite')
            )
        except Exception:
            pass
        
        return LogoutResponse(
            success=False,
            message="Logout completed with errors",
            logout_url=payload.redirect_uri if payload and payload.redirect_uri else "/"
        )


@router.get(
    "/health",
    status_code=status.HTTP_200_OK,
    summary="Authentication Health Check",
    description="Check authentication service health and session state manager status"
)
async def auth_health_check() -> Dict[str, Any]:
    try:
        from src.app.core.util.session_state_manager import session_state_manager
        
        cache_stats = await session_state_manager.get_cache_stats()
        
        return {
            "status": "healthy",
            "service": "authentication",
            "session_state_manager": cache_stats,
            "message": "Authentication service is operational"
        }
    except Exception as e:
        logger.error(f"Auth health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Authentication service health check failed: {str(e)}"
        )
