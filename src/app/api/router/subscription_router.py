from typing import List
from fastapi import APIRouter, Depends, HTTPException

from src.app.api.schema.subscription.subscription_create_request import SubscriptionCreateRequest
from src.app.api.schema.subscription.subscription_create_response import SubscriptionCreateResponse
from src.app.api.schema.subscription.subscription_read_response import SubscriptionReadResponse
from src.app.api.schema.subscription.subscription_update_request import SubscriptionUpdateRequest
from src.app.api.schema.subscription.subscription_update_response import SubscriptionUpdateResponse
from src.app.api.service.subscription_service import get_subscription_service, SubscriptionService
from src.app.core.routes.app_routes import ResourceRoute, ResourceTag, PathParam
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.core.exception.processing_exception import ProcessingException

router = APIRouter(
    prefix=ResourceRoute.SUBSCRIPTION.value,
    tags=[ResourceTag.SUBSCRIPTION.value]
)


@router.post("/", response_model=SubscriptionCreateResponse)
async def create_subscription(
    payload: SubscriptionCreateRequest,
    service: SubscriptionService = Depends(get_subscription_service)
) -> SubscriptionCreateResponse:
    """Create a new subscription."""
    try:
        return await service.create_subscription(payload)
    except ProcessingException as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get(PathParam.SUBSCRIPTION_ID, response_model=SubscriptionReadResponse)
async def get_subscription(
    subscription_id: int,
    service: SubscriptionService = Depends(get_subscription_service)
) -> SubscriptionReadResponse:
    """Get a subscription by ID."""
    try:
        return await service.get_subscription_by_id(subscription_id)
    except NotFoundException as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/tenant/{tenant_id}", response_model=List[SubscriptionReadResponse])
async def get_subscriptions_by_tenant(
    tenant_id: int,
    service: SubscriptionService = Depends(get_subscription_service)
) -> List[SubscriptionReadResponse]:
    """Get all subscriptions for a tenant."""
    try:
        return await service.get_subscriptions_by_tenant(tenant_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/", response_model=List[SubscriptionReadResponse])
async def get_all_subscriptions(
    service: SubscriptionService = Depends(get_subscription_service)
) -> List[SubscriptionReadResponse]:
    """Get all subscriptions."""
    try:
        return await service.get_all_subscriptions()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.put(PathParam.SUBSCRIPTION_ID, response_model=SubscriptionUpdateResponse)
async def update_subscription(
    subscription_id: int,
    payload: SubscriptionUpdateRequest,
    service: SubscriptionService = Depends(get_subscription_service)
) -> SubscriptionUpdateResponse:
    """Update a subscription."""
    try:
        return await service.update_subscription(subscription_id, payload)
    except NotFoundException as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ProcessingException as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.delete(PathParam.SUBSCRIPTION_ID)
async def delete_subscription(
    subscription_id: int,
    service: SubscriptionService = Depends(get_subscription_service)
) -> dict:
    """Delete a subscription."""
    try:
        success = await service.delete_subscription(subscription_id)
        if success:
            return {"message": f"Subscription {subscription_id} deleted successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to delete subscription")
    except NotFoundException as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
