from typing import List
from fastapi import APIRouter, Depends, HTTPException

from src.app.api.schema.payment.payment_create_request import PaymentCreateRequest
from src.app.api.schema.payment.payment_create_response import PaymentCreateResponse
from src.app.api.schema.payment.payment_read_response import PaymentReadResponse
from src.app.api.schema.payment.payment_update_request import PaymentUpdateRequest
from src.app.api.schema.payment.payment_update_response import PaymentUpdateResponse
from src.app.api.service.payment_service import get_payment_service, PaymentService
from src.app.core.routes.app_routes import ResourceRoute, ResourceTag, PathParam
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.core.exception.processing_exception import ProcessingException

router = APIRouter(
    prefix=ResourceRoute.PAYMENT.value,
    tags=[ResourceTag.PAYMENT.value]
)


@router.post("/", response_model=PaymentCreateResponse)
async def create_payment(
    payload: PaymentCreateRequest,
    service: PaymentService = Depends(get_payment_service)
) -> PaymentCreateResponse:
    """Create a new payment method."""
    try:
        return await service.create_payment(payload)
    except ProcessingException as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get(PathParam.PAYMENT_ID, response_model=PaymentReadResponse)
async def get_payment(
    payment_id: int,
    service: PaymentService = Depends(get_payment_service)
) -> PaymentReadResponse:
    """Get a payment method by ID."""
    try:
        return await service.get_payment_by_id(payment_id)
    except NotFoundException as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/tenant/{tenant_id}", response_model=List[PaymentReadResponse])
async def get_payments_by_tenant(
    tenant_id: int,
    service: PaymentService = Depends(get_payment_service)
) -> List[PaymentReadResponse]:
    """Get all payment methods for a tenant."""
    try:
        return await service.get_payments_by_tenant(tenant_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/", response_model=List[PaymentReadResponse])
async def get_all_payments(
    service: PaymentService = Depends(get_payment_service)
) -> List[PaymentReadResponse]:
    """Get all payment methods."""
    try:
        return await service.get_all_payments()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.put(PathParam.PAYMENT_ID, response_model=PaymentUpdateResponse)
async def update_payment(
    payment_id: int,
    payload: PaymentUpdateRequest,
    service: PaymentService = Depends(get_payment_service)
) -> PaymentUpdateResponse:
    """Update a payment method."""
    try:
        return await service.update_payment(payment_id, payload)
    except NotFoundException as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ProcessingException as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.delete(PathParam.PAYMENT_ID)
async def delete_payment(
    payment_id: int,
    service: PaymentService = Depends(get_payment_service)
) -> dict:
    """Delete a payment method."""
    try:
        success = await service.delete_payment(payment_id)
        if success:
            return {"message": f"Payment method {payment_id} deleted successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to delete payment method")
    except NotFoundException as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
