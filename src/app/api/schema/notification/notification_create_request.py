from __future__ import annotations

from typing import Literal, Optional

from pydantic import BaseModel, field_validator, ConfigDict

from src.app.dao.entity.notification_type import NotificationType


class NotificationCreateRequest(BaseModel):
    event_type: NotificationType
    details: Optional[str] = None
    sent_to: Optional[str] = None
    user_id: Optional[int] = None
    api_key_id: Optional[int] = None

    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )

    @field_validator("details", mode="before")
    def _validate_details(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return None
        v = v.strip()
        if not v:
            return None
        return v

    @field_validator("sent_to", mode="before")
    def _validate_sent_to(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return None
        v = v.strip()
        if not v:
            return None
        return v
