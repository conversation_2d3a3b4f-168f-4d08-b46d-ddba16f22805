from datetime import datetime
from typing import Literal, Optional

from pydantic import BaseModel, field_validator, ConfigDict

from src.app.dao.entity.notification_type import NotificationType
from src.app.core.util.schema_validators import ensure_utc


class NotificationReadResponse(BaseModel):
    id: int
    event_type: NotificationType
    details: Optional[str] = None
    sent_to: Optional[str] = None
    timestamp: datetime
    tenant_id: int
    user_id: Optional[int] = None
    api_key_id: Optional[int] = None

    model_config = ConfigDict(
        from_attributes=True,
        extra="forbid",
        populate_by_name=True,
    )

    @field_validator("timestamp")
    def _ensure_utc(cls, v: datetime) -> datetime:
        return ensure_utc(v)
