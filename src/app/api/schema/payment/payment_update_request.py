from __future__ import annotations

from typing import Optional
from pydantic import BaseModel, ConfigDict, field_validator


class PaymentUpdateRequest(BaseModel):
    is_default: Optional[bool] = None
    stripe_payment_method_id: Optional[str] = None

    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )

    @field_validator("stripe_payment_method_id")
    def _validate_stripe_payment_method_id(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return v
        v = v.strip()
        if not v:
            raise ValueError("Stripe payment method ID cannot be empty")
        if not v.startswith(("pm_", "card_")):
            raise ValueError("Invalid Stripe payment method ID format")
        return v
