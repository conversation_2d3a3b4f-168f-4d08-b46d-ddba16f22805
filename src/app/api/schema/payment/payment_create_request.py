from __future__ import annotations

from pydantic import BaseModel, ConfigDict, field_validator


class PaymentCreateRequest(BaseModel):
    tenant_id: int
    is_default: bool = False
    stripe_payment_method_id: str

    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )

    @field_validator("tenant_id")
    def _validate_tenant_id(cls, v: int) -> int:
        if v < 1:
            raise ValueError("Tenant ID must be positive")
        return v

    @field_validator("stripe_payment_method_id")
    def _validate_stripe_payment_method_id(cls, v: str) -> str:
        v = v.strip()
        if not v:
            raise ValueError("Stripe payment method ID cannot be empty")
        if not v.startswith(("pm_", "card_")):
            raise ValueError("Invalid Stripe payment method ID format")
        return v
