from typing import List, Optional

from pydantic import BaseModel, Field, field_validator

from src.app.dao.entity.outcome import Outcome


class LogEntryBase(BaseModel):
    request_id: str = Field(
        ...,
        min_length=1,
        description="Unique request identifier (non-empty string)"
    )
    api_key_id: Optional[int] = Field(
        None,
        ge=1,
        description="Foreign key to <PERSON><PERSON><PERSON><PERSON>; must be a positive integer if provided"
    )
    user_id: Optional[int] = Field(
        None,
        ge=1,
        description="Foreign key to User; must be a positive integer if provided"
    )
    tenant_id: int = Field(
        ...,
        ge=1,
        description="Foreign key to Tenant; must be a positive integer"
    )
    model: str = Field(
        ...,
        min_length=1,
        description="E.g., 'gpt-4-turbo' (non-empty string)"
    )
    prompt_text: Optional[str] = Field(
        None,
        min_length=1,
        description="Truncated prompt or reference ID (if provided, non-empty)"
    )
    outcome: Outcome = Field(
        ...,
        description="One of 'ALLOWED', 'BLOCKED', or 'REDACTED'"
    )
    rule_triggers: Optional[List[str]] = Field(
        None,
        description="List of rule identifiers (e.g., ['rule_1', 'rule_42']); if provided, each must be non-empty"
    )
    provider_latency_ms: Optional[int] = Field(
        None,
        ge=0,
        description="Milliseconds between request to AI provider and response; non-negative if provided"
    )
    prompt_tokens: Optional[int] = Field(
        None,
        ge=0,
        description="Number of prompt tokens; non-negative if provided"
    )
    completion_tokens: Optional[int] = Field(
        None,
        ge=0,
        description="Number of completion tokens; non-negative if provided"
    )
    masked_response: Optional[str] = Field(
        None,
        min_length=1,
        description="Truncated or masked output (if provided, non-empty)"
    )

    @field_validator("rule_triggers")
    def validate_rule_triggers(cls, value: Optional[List[str]]) -> Optional[List[str]]:
        if value is not None:
            for trigger in value:
                if not trigger or not trigger.strip():
                    raise ValueError("Each rule trigger must be a non-empty string")

        return value
