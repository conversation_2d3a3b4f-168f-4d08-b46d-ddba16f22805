from typing import Optional, List, Literal

from pydantic import Field, field_validator, BaseModel

from src.app.core.exception.processing_exception import ProcessingException
from src.app.dao.entity.outcome import Outcome


class UpdateLogEntryRequest(BaseModel):
    request_id: str = Field(
        min_length=1,
        description="Unique request identifier (if provided, non-empty string)"
    )
    api_key_id: Optional[int] = Field(
        None,
        ge=1,
        description="Foreign key to ApiKey; must be a positive integer if provided"
    )
    user_id: Optional[int] = Field(
        None,
        ge=1,
        description="Foreign key to User; must be a positive integer if provided"
    )
    tenant_id: int = Field(
        ge=1,
        description="Foreign key to Tenant; must be a positive integer if provided"
    )
    model: Optional[str] = Field(
        None,
        min_length=1,
        description="E.g., 'gpt-4-turbo' (if provided, non-empty string)"
    )
    prompt_text: Optional[str] = Field(
        None,
        min_length=1,
        description="Truncated prompt or reference ID (if provided, non-empty)"
    )
    outcome: Outcome = Field(
        description="One of 'ALLOWED', 'BLOCKED', or 'REDACTED' (if provided)"
    )
    rule_triggers: Optional[List[str]] = Field(
        None,
        description="List of rule identifiers (if provided, each must be non-empty)"
    )
    provider_latency_ms: Optional[int] = Field(
        None,
        ge=0,
        description="Milliseconds between request to AI provider and response; non-negative if provided"
    )
    prompt_tokens: Optional[int] = Field(
        None,
        ge=0,
        description="Number of prompt tokens; non-negative if provided"
    )
    completion_tokens: Optional[int] = Field(
        None,
        ge=0,
        description="Number of completion tokens; non-negative if provided"
    )
    masked_response: Optional[str] = Field(
        None,
        min_length=1,
        description="Truncated or masked output (if provided, non-empty)"
    )

    @field_validator("rule_triggers")
    def validate_rule_triggers(cls, value: str) -> str:
        if value is not None and not value.strip():
            raise ProcessingException("Each rule trigger must be a non-empty string")

        return value
