from typing import Optional

from pydantic import BaseModel, ConfigDict

from src.app.dao.entity.provider_type import ProviderType


class ProviderConfigReadResponse(BaseModel):
    id: int
    provider_type: ProviderType
    credentials: str
    default_model: Optional[str] = None
    tenant_id: int

    model_config = ConfigDict(
        from_attributes=True,
        extra="forbid",
        populate_by_name=True,
    )
