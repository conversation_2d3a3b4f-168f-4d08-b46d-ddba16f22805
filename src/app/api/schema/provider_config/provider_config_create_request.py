from __future__ import annotations

from typing import Optional

from pydantic import BaseModel, field_validator, ConfigDict

from src.app.dao.entity.provider_type import ProviderType


class ProviderConfigCreateRequest(BaseModel):
    provider_type: ProviderType
    credentials: str
    default_model: Optional[str] = None

    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )

    @field_validator("credentials", mode="before")
    def _validate_credentials(cls, v: str) -> str:
        if not isinstance(v, str):
            raise ValueError("Credentials must be a string")
        v = v.strip()
        if not v:
            raise ValueError("Credentials must not be blank")
        return v

    @field_validator("default_model", mode="before")
    def _validate_default_model(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return None
        v = v.strip()
        if not v:
            return None
        return v
