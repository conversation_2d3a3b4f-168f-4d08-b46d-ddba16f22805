from __future__ import annotations
from typing import Optional

from pydantic import BaseModel, field_validator, ConfigDict

from src.app.core.util.schema_validators import strip_and_validate_name, validate_positive_int


class RuleUpdateRequest(BaseModel):
    rule_id: Optional[str] = None
    name: Optional[str] = None
    policy_id: Optional[int] = None

    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )

    @field_validator("rule_id", mode="before")
    def _validate_rule_id(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return None
        return strip_and_validate_name(v)

    @field_validator("name", mode="before")
    def _validate_name(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return None
        return strip_and_validate_name(v)

    @field_validator("policy_id", mode="before")
    def _validate_policy_id(cls, v: Optional[int]) -> Optional[int]:
        if v is None:
            return None
        return validate_positive_int(v)
