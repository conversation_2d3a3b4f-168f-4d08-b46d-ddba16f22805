from __future__ import annotations

from pydantic import BaseModel, field_validator, ConfigDict

from src.app.core.util.schema_validators import strip_and_validate_name, validate_positive_int


class RuleCreateRequest(BaseModel):
    rule_id: str
    name: str
    policy_id: int

    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )

    @field_validator("rule_id", mode="before")
    def _validate_rule_id(cls, v: str) -> str:
        return strip_and_validate_name(v)

    @field_validator("name", mode="before")
    def _validate_name(cls, v: str) -> str:
        return strip_and_validate_name(v)

    @field_validator("policy_id", mode="before")
    def _validate_policy_id(cls, v: int) -> int:
        return validate_positive_int(v)
