from __future__ import annotations

from pydantic import BaseModel, field_validator, ConfigDict

from src.app.core.util.schema_validators import strip_and_validate_name


class RuleUpdateResponse(BaseModel):
    id: int
    rule_id: str
    name: str
    policy_id: int
    tenant_id: int

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
        extra="forbid",
    )

    @field_validator("rule_id", "name", mode="before")
    def _strip_strings(cls, v: str) -> str:
        return strip_and_validate_name(v)
