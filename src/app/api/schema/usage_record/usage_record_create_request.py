from __future__ import annotations

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, ConfigDict, field_validator

from src.app.core.constant.constants import UsageMetric


class UsageRecordCreateRequest(BaseModel):
    subscription_id: int
    metric: str
    used: int
    recorded_at: Optional[datetime] = None

    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )

    @field_validator("subscription_id")
    def _validate_subscription_id(cls, v: int) -> int:
        if v < 1:
            raise ValueError("Subscription ID must be positive")
        return v

    @field_validator("metric")
    def _validate_metric(cls, v: str) -> str:
        v = v.upper().strip()
        allowed_metrics = {metric.value for metric in UsageMetric}
        if v not in allowed_metrics:
            raise ValueError(f"Metric must be one of: {', '.join(allowed_metrics)}")
        return v

    @field_validator("used")
    def _validate_used(cls, v: int) -> int:
        if v < 0:
            raise ValueError("Used amount must be non-negative")
        return v
