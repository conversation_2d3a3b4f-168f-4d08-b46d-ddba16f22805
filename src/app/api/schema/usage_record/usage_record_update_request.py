from __future__ import annotations

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, ConfigDict, field_validator

from src.app.core.constant.constants import UsageMetric


class UsageRecordUpdateRequest(BaseModel):
    metric: Optional[str] = None
    used: Optional[int] = None
    recorded_at: Optional[datetime] = None

    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )

    @field_validator("metric")
    def _validate_metric(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return v
        v = v.upper().strip()
        allowed_metrics = {metric.value for metric in UsageMetric}
        if v not in allowed_metrics:
            raise ValueError(f"Metric must be one of: {', '.join(allowed_metrics)}")
        return v

    @field_validator("used")
    def _validate_used(cls, v: Optional[int]) -> Optional[int]:
        if v is not None and v < 0:
            raise ValueError("Used amount must be non-negative")
        return v
