from __future__ import annotations

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, ConfigDict


class SubscriptionUpdateResponse(BaseModel):
    id: int
    tenant_id: int
    plan_id: int
    status: str
    started_at: datetime
    ends_at: Optional[datetime]
    trial_ends_at: Optional[datetime]
    stripe_subscription_id: Optional[str]

    model_config = ConfigDict(
        populate_by_name=True,
        from_attributes=True,
    )
