from __future__ import annotations

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, ConfigDict, field_validator

from src.app.core.constant.constants import SubscriptionStatus


class SubscriptionUpdateRequest(BaseModel):
    plan_id: Optional[int] = None
    status: Optional[str] = None
    started_at: Optional[datetime] = None
    ends_at: Optional[datetime] = None
    trial_ends_at: Optional[datetime] = None
    stripe_subscription_id: Optional[str] = None

    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )

    @field_validator("plan_id")
    def _validate_plan_id(cls, v: Optional[int]) -> Optional[int]:
        if v is not None and v < 1:
            raise ValueError("Plan ID must be positive")
        return v

    @field_validator("status")
    def _validate_status(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return v
        v = v.upper().strip()
        allowed_statuses = {status.value for status in SubscriptionStatus}
        if v not in allowed_statuses:
            raise ValueError(f"Status must be one of: {', '.join(allowed_statuses)}")
        return v
