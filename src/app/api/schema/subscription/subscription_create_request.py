from __future__ import annotations

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, ConfigDict, field_validator

from src.app.core.constant.constants import SubscriptionStatus


class SubscriptionCreateRequest(BaseModel):
    tenant_id: int
    plan_id: int
    status: str = "ACTIVE"
    started_at: Optional[datetime] = None
    ends_at: Optional[datetime] = None
    trial_ends_at: Optional[datetime] = None
    stripe_subscription_id: Optional[str] = None

    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )

    @field_validator("tenant_id")
    def _validate_tenant_id(cls, v: int) -> int:
        if v < 1:
            raise ValueError("Tenant ID must be positive")
        return v

    @field_validator("plan_id")
    def _validate_plan_id(cls, v: int) -> int:
        if v < 1:
            raise ValueError("Plan ID must be positive")
        return v

    @field_validator("status")
    def _validate_status(cls, v: str) -> str:
        v = v.upper().strip()
        allowed_statuses = {status.value for status in SubscriptionStatus}
        if v not in allowed_statuses:
            raise ValueError(f"Status must be one of: {', '.join(allowed_statuses)}")
        return v

    @field_validator("ends_at")
    def _validate_ends_at(cls, v: Optional[datetime], values) -> Optional[datetime]:
        if v is not None and "started_at" in values and values["started_at"] is not None:
            if v <= values["started_at"]:
                raise ValueError("End date must be after start date")
        return v

    @field_validator("trial_ends_at")
    def _validate_trial_ends_at(cls, v: Optional[datetime], values) -> Optional[datetime]:
        if v is not None and "started_at" in values and values["started_at"] is not None:
            if v <= values["started_at"]:
                raise ValueError("Trial end date must be after start date")
        return v
