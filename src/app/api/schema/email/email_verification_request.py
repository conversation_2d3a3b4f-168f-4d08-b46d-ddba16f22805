"""
Email verification request schema for the new authentication flow.

This schema handles email verification tokens sent via verification links,
following the security requirements specified in auth-flow.md.
"""

from pydantic import BaseModel, ConfigDict, Field


class EmailVerificationRequest(BaseModel):
    """
    Request schema for email verification.
    
    Contains the verification token sent in the email verification link.
    """
    
    token: str = Field(
        ...,
        min_length=1,
        description="Email verification token from the verification link"
    )
    
    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )
