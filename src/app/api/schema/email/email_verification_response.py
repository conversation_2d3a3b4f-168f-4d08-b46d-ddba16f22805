"""
Email verification response schema for the new authentication flow.

This schema returns the result of email verification,
following the security requirements specified in auth-flow.md.
"""

from typing import Optional
from pydantic import BaseModel, ConfigDict, Field


class EmailVerificationResponse(BaseModel):
    """
    Response for email verification request.
    
    Returns confirmation of successful verification and next steps.
    """
    
    tenant_id: int = Field(..., description="ID of the verified tenant")
    organization_name: str = Field(..., description="Organization name")
    status: str = Field(..., description="Tenant status (should be 'ACTIVE' after verification)")
    email_verified: bool = Field(..., description="Email verification status")
    keycloak_provisioned: bool = Field(..., description="Whether Keycloak realm was provisioned")
    keycloak_realm_name: Optional[str] = Field(None, description="Keycloak realm name if provisioned")
    login_session_token: Optional[str] = Field(None, description="One-time login session token")
    message: str = Field(..., description="Success message with next steps")
    
    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
        extra="forbid",
    )
