from datetime import datetime
from typing import Literal, Optional

from pydantic import BaseModel, field_validator, ConfigDict

from src.app.dao.entity.policy_status import PolicyStatus
from src.app.core.util.schema_validators import strip_and_validate_name, ensure_utc


class PolicyReadResponse(BaseModel):
    id: int
    name: str
    version: int
    status: Literal[PolicyStatus.ACTIVE, PolicyStatus.DRAFT, PolicyStatus.ARCHIVED]
    yaml_blob: bytes
    tenant_id: int
    project_id: Optional[int] = None
    last_edited_by_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(
        from_attributes=True,
        extra="forbid",
        populate_by_name=True,
    )

    @field_validator("name", mode="before")
    def _strip_name(cls, v: str) -> str:
        return strip_and_validate_name(v)

    @field_validator("created_at", "updated_at")
    def _ensure_utc(cls, v: datetime) -> datetime:
        return ensure_utc(v)
