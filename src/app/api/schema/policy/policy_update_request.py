from __future__ import annotations
from typing import Literal, Optional

from pydantic import BaseModel, field_validator, ConfigDict

from src.app.dao.entity.policy_status import PolicyStatus
from src.app.core.util.schema_validators import strip_and_validate_name, validate_positive_int


class PolicyUpdateRequest(BaseModel):
    name: Optional[str] = None
    version: Optional[int] = None
    status: Optional[Literal[PolicyStatus.ACTIVE, PolicyStatus.DRAFT, PolicyStatus.ARCHIVED]] = None
    yaml_blob: Optional[bytes] = None
    project_id: Optional[int] = None
    last_edited_by_id: Optional[int] = None

    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )

    @field_validator("name", mode="before")
    def _validate_name(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return None
        return strip_and_validate_name(v)

    @field_validator("version", mode="before")
    def _validate_version(cls, v: Optional[int]) -> Optional[int]:
        if v is None:
            return None
        return validate_positive_int(v)

    @field_validator("project_id", mode="before")
    def _validate_project_id(cls, v: Optional[int]) -> Optional[int]:
        if v is None:
            return None
        return validate_positive_int(v)

    @field_validator("last_edited_by_id", mode="before")
    def _validate_last_edited_by_id(cls, v: Optional[int]) -> Optional[int]:
        if v is None:
            return None
        return validate_positive_int(v)
