from __future__ import annotations

from decimal import Decimal
from pydantic import BaseModel, ConfigDict, field_validator

from src.app.core.constant.constants import SubscriptionLevel


class PlanCreateRequest(BaseModel):
    code: str
    monthly_price: Decimal
    requests_per_day: int
    trial_days: int = 14
    max_allowed_users: int
    audit_report: bool = False
    max_allowed_rules: int
    analytics_dashboard: bool = False
    advanced_api: bool = False

    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )

    @field_validator("code")
    def _validate_code(cls, v: str) -> str:
        v = v.upper().strip()
        allowed_codes = {level.value for level in SubscriptionLevel}
        if v not in allowed_codes:
            raise ValueError(f"Code must be one of: {', '.join(allowed_codes)}")
        return v

    @field_validator("monthly_price")
    def _validate_monthly_price(cls, v: Decimal) -> Decimal:
        if v < 0:
            raise ValueError("Monthly price must be non-negative")
        return v

    @field_validator("requests_per_day")
    def _validate_requests_per_day(cls, v: int) -> int:
        if v < 1:
            raise ValueError("Requests per day must be positive")
        return v

    @field_validator("trial_days")
    def _validate_trial_days(cls, v: int) -> int:
        if v < 0:
            raise ValueError("Trial days must be non-negative")
        return v

    @field_validator("max_allowed_users")
    def _validate_max_allowed_users(cls, v: int) -> int:
        if v < 1:
            raise ValueError("Max allowed users must be positive")
        return v

    @field_validator("max_allowed_rules")
    def _validate_max_allowed_rules(cls, v: int) -> int:
        if v < 1:
            raise ValueError("Max allowed rules must be positive")
        return v
