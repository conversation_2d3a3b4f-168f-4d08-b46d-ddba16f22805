from __future__ import annotations

from decimal import Decimal
from typing import Optional
from pydantic import BaseModel, ConfigDict, field_validator

from src.app.core.constant.constants import SubscriptionLevel


class PlanUpdateRequest(BaseModel):
    code: Optional[str] = None
    monthly_price: Optional[Decimal] = None
    requests_per_day: Optional[int] = None
    trial_days: Optional[int] = None
    max_allowed_users: Optional[int] = None
    audit_report: Optional[bool] = None
    max_allowed_rules: Optional[int] = None
    analytics_dashboard: Optional[bool] = None
    advanced_api: Optional[bool] = None

    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )

    @field_validator("code")
    def _validate_code(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return v
        v = v.upper().strip()
        allowed_codes = {level.value for level in SubscriptionLevel}
        if v not in allowed_codes:
            raise ValueError(f"Code must be one of: {', '.join(allowed_codes)}")
        return v

    @field_validator("monthly_price")
    def _validate_monthly_price(cls, v: Optional[Decimal]) -> Optional[Decimal]:
        if v is not None and v < 0:
            raise ValueError("Monthly price must be non-negative")
        return v

    @field_validator("requests_per_day")
    def _validate_requests_per_day(cls, v: Optional[int]) -> Optional[int]:
        if v is not None and v < 1:
            raise ValueError("Requests per day must be positive")
        return v

    @field_validator("trial_days")
    def _validate_trial_days(cls, v: Optional[int]) -> Optional[int]:
        if v is not None and v < 0:
            raise ValueError("Trial days must be non-negative")
        return v

    @field_validator("max_allowed_users")
    def _validate_max_allowed_users(cls, v: Optional[int]) -> Optional[int]:
        if v is not None and v < 1:
            raise ValueError("Max allowed users must be positive")
        return v

    @field_validator("max_allowed_rules")
    def _validate_max_allowed_rules(cls, v: Optional[int]) -> Optional[int]:
        if v is not None and v < 1:
            raise ValueError("Max allowed rules must be positive")
        return v
