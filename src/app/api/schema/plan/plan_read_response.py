from __future__ import annotations

from decimal import Decimal
from pydantic import BaseModel, ConfigDict


class PlanReadResponse(BaseModel):
    id: int
    code: str
    monthly_price: Decimal
    requests_per_day: int
    trial_days: int
    max_allowed_users: int
    audit_report: bool
    max_allowed_rules: int
    analytics_dashboard: bool
    advanced_api: bool

    model_config = ConfigDict(
        populate_by_name=True,
        from_attributes=True,
    )
