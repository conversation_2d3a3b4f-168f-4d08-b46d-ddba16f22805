from typing import Optional

from pydantic import field_validator, Field, BaseModel, ConfigDict

from src.app.core.exception.processing_exception import ProcessingException
from src.app.dao.entity.region import Region
from src.app.dao.entity.subscription_level import SubscriptionLevel


class CreateTenantResponse(BaseModel):
    id: int = Field(..., description="Unique database ID of the created tenant.")
    organization_name: str = Field(
        ...,
        description="Organization name."
    )
    industry: Optional[str] = Field(
        None,
        min_length=3,
        max_length=100,
        description="The industry the tenant operates in, if provided (3–100 characters)."
    )
    region_preference: Region = Field(
        ...,
        description="Tenant’s preferred Region. Must be a valid Region enum."
    )
    subscription_level: SubscriptionLevel = Field(
        ...,
        description="Tenant’s subscription level. Must be a valid SubscriptionLevel enum."
    )
    status: str = Field(
        ...,
        description="Tenant status (should be 'PENDING' after creation, will be 'ACTIVE' after email verification)"
    )
    admin_email: str = Field(
        ...,
        description="Admin email address (verification email sent here)"
    )

    @field_validator("industry")
    def industry_not_empty_if_provided(cls, value: Optional[str]) -> Optional[str]:
        if value is not None and not value.strip():
            raise ProcessingException("Industry must not be blank if provided")

        return value

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
        extra="forbid",
    )

    @field_validator('region_preference', mode='before')
    def validate_region(cls, v):
        """Convert string from database to enum for response."""
        if isinstance(v, str):
            return Region(v)
        return v

    @field_validator('subscription_level', mode='before')
    def validate_subscription(cls, v):
        """Convert string from database to enum for response."""
        if isinstance(v, str):
            return SubscriptionLevel(v)
        return v
