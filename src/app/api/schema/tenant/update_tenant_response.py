from typing import Optional

from pydantic import Field, field_validator, BaseModel, ConfigDict

from src.app.core.exception.processing_exception import ProcessingException
from src.app.dao.entity.region import Region
from src.app.dao.entity.subscription_level import SubscriptionLevel


class UpdateTenantResponse(BaseModel):
    organization_name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=100,
        description="Updated tenant name (1–100 characters)."
    )
    industry: Optional[str] = Field(
        None,
        min_length=1,
        max_length=100,
        description="Updated industry, if provided (3–100 characters)."
    )
    region_preference: Optional[Region] = Field(
        None,
        description="Updated Region preference. Must be a valid Region enum."
    )
    subscription_level: Optional[SubscriptionLevel] = Field(
        None,
        description="Updated subscription level. Must be a valid SubscriptionLevel enum."
    )

    @field_validator("organization_name")
    def name_must_not_be_blank_if_provided(cls, value: Optional[str]) -> Optional[str]:
        if value is not None and not value.strip():
            raise ProcessingException("Name must not be blank or only whitespace")

        return value

    @field_validator("industry")
    def industry_not_empty_if_provided(cls, value: Optional[str]) -> Optional[str]:
        if value is not None and not value.strip():
            raise ProcessingException("Industry must not be blank if provided")

        return value

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
        extra="forbid",
    )
