"""
Tenant creation request schema with secure authentication flow.

This module defines the request schema for creating a new tenant with secure
authentication fields, following the security requirements specified in auth-flow.md.
"""

from typing import Optional
from pydantic import BaseModel, ConfigDict, field_validator, Field, EmailStr

from src.app.dao.entity.region import Region
from src.app.dao.entity.subscription_level import SubscriptionLevel
from src.app.core.exception.processing_exception import ProcessingException


class CreateTenantRequest(BaseModel):
    organization_name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="Organization name (1–100 characters)."
    )
    industry: Optional[str] = Field(
        None,
        min_length=3,
        max_length=100,
        description="The industry the organization operates in, if provided (3–100 characters)."
    )
    region_preference: Region = Field(
        ...,
        description="Organization's preferred Region. Must be a valid Region enum."
    )
    subscription_level: SubscriptionLevel = Field(
        ...,
        description="Organization's subscription level. Must be a valid SubscriptionLevel enum."
    )
    
    admin_email: EmailStr = Field(
        ...,
        description="Admin email address - will receive verification email"
    )
    admin_password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="Admin password (8-128 characters, will be hashed securely)"
    )
    admin_first_name: str = Field(
        ...,
        min_length=1,
        max_length=50,
        description="Admin first name (1-50 characters)"
    )
    admin_last_name: str = Field(
        ...,
        min_length=1,
        max_length=50,
        description="Admin last name (1-50 characters)"
    )
    
    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )

    @field_validator("organization_name")
    def organization_name_must_not_be_blank(cls, value: str) -> str:
        if not value.strip():
            raise ProcessingException("Organization name must not be blank or only whitespace")
        return value.strip()

    @field_validator("admin_password")
    def validate_password_strength(cls, value: str) -> str:
        """
        Validate password meets security requirements.
        
        Requirements:
        - At least 8 characters
        - Contains at least one uppercase letter
        - Contains at least one lowercase letter
        - Contains at least one digit
        - Contains at least one special character
        """
        if len(value) < 8:
            raise ProcessingException("Password must be at least 8 characters long")
        
        if not any(c.isupper() for c in value):
            raise ProcessingException("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in value):
            raise ProcessingException("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in value):
            raise ProcessingException("Password must contain at least one digit")
        
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        if not any(c in special_chars for c in value):
            raise ProcessingException("Password must contain at least one special character")
        
        return value

    @field_validator("admin_first_name", "admin_last_name")
    def validate_names(cls, value: str) -> str:
        value = value.strip()
        if not value:
            raise ProcessingException("Name must not be blank or only whitespace")
        return value

    @field_validator("industry")
    def industry_not_empty_if_provided(cls, value: Optional[str]) -> Optional[str]:
        if value is not None and not value.strip():
            raise ProcessingException("Industry must not be blank if provided")
        return value
