from typing import Optional

from pydantic import BaseModel, Field, ConfigDict, field_validator

from src.app.dao.entity.region import Region
from src.app.dao.entity.subscription_level import SubscriptionLevel


class ReadTenantResponse(BaseModel):
    id: int = Field(..., description="Unique database ID of the tenant.")
    organization_name: str = Field(..., description="Unique name of the tenant.")
    industry: Optional[str] = Field(None, description="The industry the tenant operates in.")
    region_preference: Region = Field(..., description="Tenant’s preferred Region.")
    subscription_level: SubscriptionLevel = Field(..., description="Tenant’s subscription level.")

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
        extra="forbid",
    )

    @field_validator('region_preference', mode='before')
    def validate_region(cls, v):
        """Convert string from database to enum for response."""
        if isinstance(v, str):
            return Region(v)
        return v

    @field_validator('subscription_level', mode='before')
    def validate_subscription(cls, v):
        """Convert string from database to enum for response."""
        if isinstance(v, str):
            return SubscriptionLevel(v)
        return v
