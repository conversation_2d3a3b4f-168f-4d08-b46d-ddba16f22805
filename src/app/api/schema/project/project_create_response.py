from __future__ import annotations

from typing import Optional

from pydantic import BaseModel, field_validator, ConfigDict

from src.app.core.util.schema_validators import strip_and_validate_name


class ProjectCreateResponse(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    tenant_id: int

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
        extra="forbid",
    )

    @field_validator("name", mode="before")
    def _strip_name(cls, v: str) -> str:
        return strip_and_validate_name(v)
