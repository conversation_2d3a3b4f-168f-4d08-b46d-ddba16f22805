from __future__ import annotations

from typing import Optional

from pydantic import BaseModel, field_validator, ConfigDict

from src.app.core.util.schema_validators import strip_and_validate_name


class ProjectCreateRequest(BaseModel):
    name: str
    description: Optional[str] = None

    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )

    @field_validator("name", mode="before")
    def _validate_name(cls, v: str) -> str:
        return strip_and_validate_name(v)

    @field_validator("description", mode="before")
    def _validate_description(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return None
        v = v.strip()
        if not v:
            return None
        return v
