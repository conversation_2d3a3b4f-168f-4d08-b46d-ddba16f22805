from typing import List, Optional
from pydantic import BaseModel, ConfigDict


class KeycloakProvisionResponse(BaseModel):
    tenant_id: str
    realm_name: str
    client_id: str
    client_secret: str
    admin_user_id: Optional[str] = None
    realm_roles: List[str]
    redirect_uri: str
    success: bool
    message: str
    
    model_config = ConfigDict(
        from_attributes=True,
        extra="forbid",
        populate_by_name=True,
    )
