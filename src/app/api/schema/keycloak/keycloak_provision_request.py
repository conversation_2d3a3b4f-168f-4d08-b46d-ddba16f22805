"""
Keycloak tenant provisioning request schema.

This module defines the request schema for provisioning a new tenant in Keycloak,
following the existing project patterns for Pydantic schemas.
"""

from typing import Optional
from pydantic import BaseModel, ConfigDict, field_validator, EmailStr


class KeycloakProvisionRequest(BaseModel):
    tenant_id: str
    organization_name: Optional[str] = None
    admin_username: str
    admin_email: EmailStr
    admin_password: str
    admin_first_name: Optional[str] = None
    admin_last_name: Optional[str] = None
    
    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )
    
    @field_validator("tenant_id", mode="before")
    def _validate_tenant_id(cls, v: str) -> str:
        if not isinstance(v, str):
            raise ValueError("Tenant ID must be a string")
        v = v.strip()
        if not v:
            raise ValueError("Tenant ID cannot be empty")
        if not v.replace("-", "").replace("_", "").isalnum():
            raise ValueError("Tenant ID must contain only alphanumeric characters, hyphens, and underscores")
        return v
    
    @field_validator("admin_username", mode="before")
    def _validate_admin_username(cls, v: str) -> str:
        if not isinstance(v, str):
            raise ValueError("Admin username must be a string")
        v = v.strip()
        if not v:
            raise ValueError("Admin username cannot be empty")
        if len(v) < 3:
            raise ValueError("Admin username must be at least 3 characters long")
        return v
    
    @field_validator("admin_password", mode="before")
    def _validate_admin_password(cls, v: str) -> str:
        if not isinstance(v, str):
            raise ValueError("Admin password must be a string")
        if len(v) < 8:
            raise ValueError("Admin password must be at least 8 characters long")
        return v
    
    @field_validator("organization_name", mode="before")
    def _validate_organization_name(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return None
        if not isinstance(v, str):
            raise ValueError("Organization name must be a string")
        v = v.strip()
        return v if v else None

    @field_validator("admin_first_name", mode="before")
    def _validate_admin_first_name(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return None
        if not isinstance(v, str):
            raise ValueError("Admin first name must be a string")
        v = v.strip()
        return v if v else None
    
    @field_validator("admin_last_name", mode="before")
    def _validate_admin_last_name(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return None
        if not isinstance(v, str):
            raise ValueError("Admin last name must be a string")
        v = v.strip()
        return v if v else None
