from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict


class CallbackResponse(BaseModel):
    access_token: str = Field(
        ...,
        description="JWT access token for API authentication"
    )
    token_type: str = Field(
        default="Bearer",
        description="Token type (always Bearer)"
    )
    expires_in: int = Field(
        ...,
        description="Access token expiration time in seconds"
    )
    user_id: str = Field(
        ...,
        description="Unique user identifier"
    )
    username: str = Field(
        ...,
        description="Username"
    )
    email: Optional[str] = Field(
        None,
        description="User email address"
    )
    first_name: Optional[str] = Field(
        None,
        description="User first name"
    )
    last_name: Optional[str] = Field(
        None,
        description="User last name"
    )
    realm: str = Field(
        ...,
        description="Keycloak realm name"
    )
    tenant_id: str = Field(
        ...,
        description="Tenant identifier"
    )
    roles: List[str] = Field(
        default_factory=list,
        description="User roles within the realm"
    )
    
    model_config = ConfigDict(
        from_attributes=True,
        extra="forbid",
        json_schema_extra={
            "example": {
                "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "Bearer",
                "expires_in": 1800,
                "user_id": "user-123",
                "username": "john.doe",
                "email": "<EMAIL>",
                "first_name": "John",
                "last_name": "Doe",
                "realm": "tenant-123",
                "tenant_id": "123",
                "roles": ["TENANT_ADMIN", "DEVELOPER"]
            }
        }
    )
