from typing import Optional
from pydantic import BaseModel, Field, ConfigDict


class LoginRequest(BaseModel):
    tenant_id: str = Field(
        ...,
        description="Tenant ID for realm identification",
        min_length=1,
        max_length=100
    )
    redirect_uri: Optional[str] = Field(
        None,
        description="Optional redirect URI after successful authentication",
        max_length=500
    )
    
    model_config = ConfigDict(
        from_attributes=True,
        extra="forbid",
        json_schema_extra={
            "example": {
                "tenant_id": "tenant-123",
                "redirect_uri": "https://app.example.com/dashboard"
            }
        }
    )
