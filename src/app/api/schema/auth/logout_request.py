from typing import Optional
from pydantic import BaseModel, Field, ConfigDict


class LogoutRequest(BaseModel):
    redirect_uri: Optional[str] = Field(
        None,
        description="Optional redirect URI after logout",
        max_length=500
    )
    
    model_config = ConfigDict(
        from_attributes=True,
        extra="forbid",
        json_schema_extra={
            "example": {
                "redirect_uri": "https://app.example.com/login"
            }
        }
    )
