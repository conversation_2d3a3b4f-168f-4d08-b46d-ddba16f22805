from pydantic import BaseModel, Field, ConfigDict


class LoginResponse(BaseModel):
    authorization_url: str = Field(
        ...,
        description="Keycloak authorization URL for user authentication"
    )
    state: str = Field(
        ...,
        description="OAuth2 state parameter for CSRF protection"
    )
    realm_name: str = Field(
        ...,
        description="Keycloak realm name for the tenant"
    )
    
    model_config = ConfigDict(
        from_attributes=True,
        extra="forbid",
        json_schema_extra={
            "example": {
                "authorization_url": "https://keycloak.example.com/realms/tenant-123/protocol/openid-connect/auth?client_id=securinest&response_type=code&scope=openid&redirect_uri=https://api.example.com/auth/callback&state=abc123",
                "state": "abc123",
                "realm_name": "tenant-123"
            }
        }
    )
