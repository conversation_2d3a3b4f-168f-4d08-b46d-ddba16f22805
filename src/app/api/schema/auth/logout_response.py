from pydantic import BaseModel, Field, ConfigDict


class LogoutResponse(BaseModel):
    success: bool = Field(
        default=True,
        description="Logout success status"
    )
    message: str = Field(
        default="Successfully logged out",
        description="Logout status message"
    )
    logout_url: str = Field(
        ...,
        description="Keycloak logout URL for complete session termination"
    )
    
    model_config = ConfigDict(
        from_attributes=True,
        extra="forbid",
        json_schema_extra={
            "example": {
                "success": True,
                "message": "Successfully logged out",
                "logout_url": "https://keycloak.example.com/realms/tenant-123/protocol/openid-connect/logout?redirect_uri=https://app.example.com/login"
            }
        }
    )
