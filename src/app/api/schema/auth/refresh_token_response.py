from pydantic import BaseModel, Field, ConfigDict


class RefreshTokenResponse(BaseModel):
    access_token: str = Field(
        ...,
        description="New JWT access token"
    )
    token_type: str = Field(
        default="Bearer",
        description="Token type (always Bearer)"
    )
    expires_in: int = Field(
        ...,
        description="Access token expiration time in seconds"
    )
    
    model_config = ConfigDict(
        from_attributes=True,
        extra="forbid",
        json_schema_extra={
            "example": {
                "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "Bearer",
                "expires_in": 1800
            }
        }
    )
