from typing import Optional
from pydantic import BaseModel, Field, ConfigDict


class CallbackRequest(BaseModel):
    code: str = Field(
        ...,
        description="Authorization code from Keycloak",
        min_length=1
    )
    state: str = Field(
        ...,
        description="OAuth2 state parameter for CSRF protection",
        min_length=1
    )
    error: Optional[str] = Field(
        None,
        description="Error code if authentication failed"
    )
    error_description: Optional[str] = Field(
        None,
        description="Human-readable error description"
    )
    
    model_config = ConfigDict(
        from_attributes=True,
        extra="forbid",
        json_schema_extra={
            "example": {
                "code": "auth_code_123456",
                "state": "abc123"
            }
        }
    )
