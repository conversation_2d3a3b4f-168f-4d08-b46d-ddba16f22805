from typing import Optional

from pydantic import BaseModel, Field, field_validator, ConfigDict

from src.app.core.exception.processing_exception import ProcessingException


class UpdateApiKeyResponse(BaseModel):
    hashed_token: str = Field(
        ...,
        min_length=1,
        description="Hashed token string, must not be blank or only whitespace."
    )
    label: Optional[str] = Field(
        None,
        min_length=1,
        max_length=100,
        description="Human-friendly name or label for the API key (1–100 characters), if provided."
    )
    tenant_id: int = Field(
        ...,
        gt=0,
        description="ID of the Tenant that will own this API key. Must be a positive integer."
    )
    user_id: Optional[int] = Field(
        None,
        gt=0,
        description="ID of the User who created or will be associated with this API key, if applicable. Must be a positive integer."
    )
    project_id: Optional[int] = Field(
        None,
        gt=0,
        description="ID of the Project to which this API key belongs, if applicable. Must be a positive integer."
    )

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
        extra="forbid",
    )

    @field_validator("hashed_token")
    def hashed_token_must_not_be_blank(cls, value: str) -> str:
        if not value.strip():
            raise ProcessingException("Hashed token must not be blank or only whitespace")

        return value

    @field_validator("label")
    def label_not_empty_if_provided(cls, value: Optional[str]) -> Optional[str]:
        if value is not None and not value.strip():
            raise ProcessingException("Label must not be blank if provided")

        return value

    @field_validator("tenant_id", "user_id", "project_id")
    def id_must_be_positive(cls, value: Optional[int]) -> Optional[int]:
        if value is not None and value <= 0:
            raise ProcessingException("IDs must be positive integers")

        return value
