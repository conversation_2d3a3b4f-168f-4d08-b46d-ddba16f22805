from typing import Optional
from datetime import datetime

from pydantic import BaseModel, Field, ConfigDict

from src.app.dao.entity.api_key_status import ApiKeyStatus


class ReadApiKeyResponse(BaseModel):
    id: int = Field(
        ...,
        description="Unique identifier of the API key."
    )
    hashed_token: str = Field(
        ...,
        description="Hashed API token string."
    )
    label: Optional[str] = Field(
        None,
        description="Human-friendly name/label for the API key, if any."
    )
    creation_date: datetime = Field(
        ...,
        description="UTC timestamp when this key was created."
    )
    last_used: Optional[datetime] = Field(
        None,
        description="UTC timestamp when this key was last used, if ever."
    )
    status: ApiKeyStatus = Field(
        ...,
        description="Current status of the API key (e.g., ACTIVE, REVOKED)."
    )
    tenant_id: int = Field(
        ...,
        description="ID of the Tenant that owns this API key."
    )
    user_id: Optional[int] = Field(
        None,
        description="ID of the User associated with this API key, if applicable."
    )
    project_id: Optional[int] = Field(
        None,
        description="ID of the Project associated with this API key, if applicable."
    )
    updated_at: datetime = Field(
        ...,
        description="UTC timestamp when this record was last updated."
    )

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
        extra="forbid",
    )
