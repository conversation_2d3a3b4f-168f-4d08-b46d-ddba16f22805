from __future__ import annotations
from typing import Literal, Optional

from pydantic import BaseModel, EmailStr, field_validator, ConfigDict

from src.app.core.constant.constants import UserRole
from src.app.core.util.schema_validators import  (
    strip_and_validate_name,
)

class UserUpdateRequest(BaseModel):
    email: Optional[EmailStr] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    role: Optional[
        Literal[
            UserRole.PLATFORM_ADMIN,
            UserRole.TENANT_ADMIN,
            UserRole.POLICY_EDITOR,
            UserRole.DEVELOPER,
            UserRole.AUDITOR,
            UserRole.SUPPORT_AGENT
        ]
    ] = None
    is_active: Optional[bool] = None

    model_config = ConfigDict(
        populate_by_name=True,
        extra="forbid",
        validate_default=True,
    )

    @field_validator("first_name", mode="before")
    def _validate_first_name(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return None
        return strip_and_validate_name(v)

    @field_validator("last_name", mode="before")
    def _validate_last_name(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return None
        return strip_and_validate_name(v)
