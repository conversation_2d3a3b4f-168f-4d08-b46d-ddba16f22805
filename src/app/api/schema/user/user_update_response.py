from __future__ import annotations

from datetime import datetime
from typing import Literal

from pydantic import BaseModel, EmailStr, field_validator, ConfigDict

from src.app.core.constant.constants import UserRole
from src.app.core.util.schema_validators import (
    strip_and_validate_name,
    ensure_utc,
)


class UserUpdateResponse(BaseModel):
    email: EmailStr
    first_name: str
    last_name: str
    role: Literal[
        UserRole.PLATFORM_ADMIN,
        UserRole.TENANT_ADMIN,
        UserRole.POLICY_EDITOR,
        UserRole.DEVELOPER,
        UserRole.AUDITOR,
        UserRole.SUPPORT_AGENT
    ]
    is_active: bool

    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
        extra="forbid",
    )

    @field_validator("first_name", "last_name", mode="before")
    def _strip_names(cls, v: str) -> str:
        return strip_and_validate_name(v)

    @field_validator("created_at", "updated_at")
    def _ensure_utc(cls, v: datetime) -> datetime:
        return ensure_utc(v)
