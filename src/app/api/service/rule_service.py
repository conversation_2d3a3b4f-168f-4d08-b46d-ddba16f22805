from typing import List

from fastapi import Depends
from sqlalchemy.exc import Integrity<PERSON>rror
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from src.app.api.schema.rule.rule_create_request import RuleCreateRequest
from src.app.api.schema.rule.rule_create_response import RuleCreateResponse
from src.app.api.schema.rule.rule_read_response import RuleReadResponse
from src.app.api.schema.rule.rule_update_request import RuleUpdateRequest
from src.app.api.schema.rule.rule_update_response import RuleUpdateResponse
from src.app.core.db_connection.db_connector import get_session
from src.app.core.exception.exception_util import ExceptionUtil
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.core.exception.processing_exception import ProcessingException
from src.app.dao.entity.rule import Rule


async def get_rule_service(session: AsyncSession = Depends(get_session)) -> "RuleService":
    return RuleService(session)


class RuleService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_rule_by_id(
            self,
            rule_id: int,
            # caller
    ) -> RuleReadResponse:
        #  tenant_id = request.tenant_id
        try:
            sql_query = select(Rule).where(
                Rule.id == rule_id,
                Rule.tenant_id == 1
            )

            rule = (await self.session.execute(sql_query)).scalar_one_or_none()

            if not rule:
                raise NotFoundException("Rule not found")

            # 3) Authorization (commented out until auth is implemented)
            # if caller.role not in (UserRole.ADMIN, UserRole.MODERATOR, UserRole.READER):
            #     raise SecurinestException(
            #         status_code=403,
            #         message="Not authorized to view rule"
            #     )

            # 4) Return
            return RuleReadResponse.model_validate(rule)
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def get_rules(self) -> List[RuleReadResponse]:
        try:
            rules = (await self.session.scalars(select(Rule).where(Rule.tenant_id == 1))).all()
            # hardcoded until auth available

            return [RuleReadResponse.model_validate(r) for r in rules]
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def create_rule(
            self,
            payload: RuleCreateRequest
            # caller
    ) -> RuleCreateResponse:
        # 1) Authorization check (commented out until auth is implemented)
        # if caller.role not in (UserRole.ADMIN, UserRole.MODERATOR):
        #     raise SecurinestException(
        #         status_code=403,
        #         message="Not authorized to create rules"
        #     )

        tenant_id = 1  # hard-coded until auth available
        new_rule = Rule(
            rule_id=payload.rule_id,
            name=payload.name,
            policy_id=payload.policy_id,
            tenant_id=tenant_id
        )

        try:
            self.session.add(new_rule)
            await self.session.commit()
            await self.session.refresh(new_rule)
        except IntegrityError as e:
            await self.session.rollback()
            error_msg = str(e).lower()
            if "unique constraint" in error_msg and "rule_id" in error_msg:
                raise ProcessingException("Rule ID already exists")
            elif "foreign key" in error_msg and "policy" in error_msg:
                raise ProcessingException("Invalid policy reference")
            elif "foreign key" in error_msg and "tenant" in error_msg:
                raise ProcessingException("Invalid tenant reference")
            else:
                raise ProcessingException(f"Database constraint violation: {str(e)}")

        return RuleCreateResponse.model_validate(new_rule)

    async def update_rule(
            self,
            rule_id: int,
            payload: RuleUpdateRequest
            # caller
    ) -> RuleUpdateResponse:
        sql_query = select(Rule).where(
            Rule.id == rule_id,
            Rule.tenant_id == 1  # hardcoded until auth available
        )

        updated_rule = (await self.session.execute(sql_query)).scalar_one_or_none()

        if not updated_rule:
            raise NotFoundException("Rule not found")

        # 3) Authorization check (commented out until auth is implemented)
        # if caller.role == UserRole.READER:
        #     raise SecurinestException(
        #         status_code=403,
        #         message="Not authorized to update rule"
        #     )

        if payload.rule_id is not None:
            updated_rule.rule_id = str(payload.rule_id)

        if payload.name is not None:
            updated_rule.name = payload.name

        if payload.policy_id is not None:
            updated_rule.policy_id = payload.policy_id

        try:
            self.session.add(updated_rule)
            await self.session.commit()
            await self.session.refresh(updated_rule)

            return RuleUpdateResponse.model_validate(updated_rule)
        except Exception as e:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(e)

    async def delete_rule(
            self,
            rule_id: int,
            # caller
    ) -> None:
        sql_query = select(Rule).where(
            Rule.id == rule_id,
            Rule.tenant_id == 1 #hard-coded until auth available
        )
        
        deleted_rule = (await self.session.execute(sql_query)).scalar_one_or_none()

        if not deleted_rule:
            raise NotFoundException("Rule not found")

        # 3) Authorization (commented out until auth is implemented)
        # if caller.role != UserRole.ADMIN:
        #     raise SecurinestException(
        #         status_code=403,
        #         message="Not authorized to delete rule"
        #     )

        try:
            await self.session.delete(deleted_rule)
            await self.session.commit()
        except Exception as e:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(e)
