from typing import List

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from src.app.api.schema.policy.policy_create_request import PolicyCreateRequest
from src.app.api.schema.policy.policy_create_response import PolicyCreateResponse
from src.app.api.schema.policy.policy_read_response import PolicyReadResponse
from src.app.api.schema.policy.policy_update_request import PolicyUpdateRequest
from src.app.api.schema.policy.policy_update_response import PolicyUpdateResponse
from src.app.core.db_connection.db_connector import get_session
from src.app.core.exception.exception_util import ExceptionUtil
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.core.model.user_context import UserContext
from src.app.dao.entity.policy import Policy


async def get_policy_service(session: AsyncSession = Depends(get_session)) -> "PolicyService":
    return PolicyService(session)


class PolicyService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_policy_by_id(
            self,
            policy_id: int
            # caller
    ) -> PolicyReadResponse:
        #  tenant_id = request.tenant_id
        try:
            sql_query = select(Policy).where(
                Policy.id == policy_id,
                Policy.tenant_id == 1
            )

            policy = (await self.session.execute(sql_query)).scalar_one_or_none()

            if not policy:
                raise NotFoundException("Policy not found")

            # 3) Authorization (commented out until auth is implemented)
            # if caller.role not in (UserRole.ADMIN, UserRole.MODERATOR, UserRole.READER):
            #     raise SecurinestException(
            #         status_code=403,
            #         message="Not authorized to view policy"
            #     )

            # 4) Return
            return PolicyReadResponse.model_validate(policy)
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def get_policies(self) -> List[PolicyReadResponse]:
        try:
            policies = ((await self.session.scalars(select(Policy).where(Policy.tenant_id == 1))).all())
            # hardcoded until auth available

            return [PolicyReadResponse.model_validate(p) for p in policies]
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def create_policy(
            self,
            payload: PolicyCreateRequest,
            current_user: UserContext
    ) -> PolicyCreateResponse:
        # 1) Authorization check (commented out until auth is implemented)
        # if caller.role not in (UserRole.ADMIN, UserRole.MODERATOR):
        #     raise SecurinestException(
        #         status_code=403,
        #         message="Not authorized to create policies"
        #     )
        tenant_id = 1  # hard-coded until auth available

        new_policy = Policy(
            name=payload.name,
            version=payload.version,
            status=payload.status,
            yaml_blob=payload.yaml_blob,
            project_id=payload.project_id,
            last_edited_by_id=payload.last_edited_by_id,
            tenant_id=tenant_id
        )

        try:
            self.session.add(new_policy)
            await self.session.commit()
            await self.session.refresh(new_policy)

            return PolicyCreateResponse.model_validate(new_policy)
        except Exception as exception:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(exception)

    async def update_policy(
            self,
            policy_id: int,
            payload: PolicyUpdateRequest
            # caller
    ) -> PolicyUpdateResponse:
        sql_query = select(Policy).where(
            Policy.id == policy_id,
            Policy.tenant_id == 1  # hardcoded until auth available
        )

        updated_policy = (await self.session.execute(sql_query)).scalar_one_or_none()

        if not updated_policy:
            raise NotFoundException("Policy not found")

        # 3) Authorization check (commented out until auth is implemented)
        # if caller.role == UserRole.READER:
        #     raise SecurinestException(
        #         status_code=403,
        #         message="Not authorized to update policy"
        #     )

        if payload.name is not None:
            updated_policy.name = payload.name

        if payload.version is not None:
            updated_policy.version = payload.version

        if payload.status is not None:
            updated_policy.status = payload.status

        if payload.yaml_blob is not None:
            updated_policy.yaml_blob = payload.yaml_blob

        if payload.project_id is not None:
            updated_policy.project_id = payload.project_id

        if payload.last_edited_by_id is not None:
            updated_policy.last_edited_by_id = payload.last_edited_by_id

        try:
            self.session.add(updated_policy)
            await self.session.commit()
            await self.session.refresh(updated_policy)

            return PolicyUpdateResponse.model_validate(updated_policy)
        except Exception as exception:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(exception)

    async def delete_policy(
            self,
            policy_id: int
            # caller
    ) -> None:
        sql_query = select(Policy).where(
            Policy.id == policy_id,
            Policy.tenant_id == 1  # hard-coded until auth available
        )

        deleted_policy = (await self.session.execute(sql_query)).scalar_one_or_none()

        if not deleted_policy:
            raise NotFoundException("Policy not found")

            # 3) Authorization (commented out until auth is implemented)
            # if caller.role != UserRole.ADMIN:
            #     raise SecurinestException(
            #         status_code=403,
            #         message="Not authorized to delete policy"
            #     )

        try:
            await self.session.delete(deleted_policy)
            await self.session.commit()
        except Exception as exception:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(exception)
