from typing import List

from fastapi import Depends
from sqlalchemy.exc import Integrity<PERSON>rror
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from src.app.api.schema.user.user_create_request import UserCreateRequest
from src.app.api.schema.user.user_create_response import UserCreateResponse
from src.app.api.schema.user.user_read_response import UserReadResponse
from src.app.api.schema.user.user_update_request import UserUpdateRequest
from src.app.api.schema.user.user_update_response import UserUpdateResponse
from src.app.core.constant.constants import UserRole
from src.app.core.db_connection.db_connector import get_session
from src.app.core.exception.exception_util import ExceptionUtil
from src.app.core.exception.forbidden_exception import ForbiddenException
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.core.model.user_context import UserContext
from src.app.dao.entity.user import User


async def get_user_service(session: AsyncSession = Depends(get_session)) -> "UserService":
    return UserService(session)


def get_validated_user(current_user, user):
    validated_user = UserReadResponse.model_validate(user)
    tenant_id = str(validated_user.tenant_id)
    user_id = str(validated_user.id)

    if (user_id != current_user.user_id and
            UserRole.TENANT_ADMIN.value not in current_user.roles and
            tenant_id != current_user.tenant_id):
        raise ForbiddenException(message="Access denied. You can only access your own user data.")

    return validated_user


class UserService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_user_by_id(
            self,
            user_id: int,
            current_user: UserContext
    ) -> UserReadResponse:
        try:
            #  tenant_id = request.tenant_id
            sql_query = select(User).where(
                User.id == user_id,
                User.tenant_id == 1
            )

            user = (await self.session.execute(sql_query)).scalar_one_or_none()

            if not user:
                raise NotFoundException("User not found")

            # 3) Authorization (commented out until auth is implemented)
            # if caller.role not in (UserRole.ADMIN, UserRole.MODERATOR, UserRole.READER):
            #     raise SecurinestException(
            #         status_code=403,
            #         message="Not authorized to view user"
            #     )

            # 4) Return
            return get_validated_user(current_user, user)
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def get_users(self) -> List[UserReadResponse]:
        try:
            users = ((await self.session.scalars(select(User).where(User.tenant_id == 1))).all())
            # hardcoded until auth available

            return [UserReadResponse.model_validate(u) for u in users]
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def create_user(self, payload: UserCreateRequest, current_user: UserContext) -> UserCreateResponse:
        # 1) Authorization check (commented out until auth is implemented)
        # if caller.role not in (UserRole.ADMIN, UserRole.MODERATOR):
        #     raise SecurinestException(
        #         status_code=403,
        #         message="Not authorized to create users"
        #     )

        tenant_id = 1  # hard-coded until auth available

        new_user = User(
            email=payload.email,
            first_name=payload.first_name,
            last_name=payload.last_name,
            role=payload.role,
            is_active=payload.is_active,
            tenant_id=tenant_id
        )

        try:
            self.session.add(new_user)
            await self.session.commit()
            await self.session.refresh(new_user)

            return UserCreateResponse.model_validate(new_user)
        except IntegrityError as e:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(e)

    async def update_user(
            self,
            user_id: int,
            payload: UserUpdateRequest
            # caller
    ) -> UserUpdateResponse:
        sql_query = select(User).where(
            User.id == user_id,
            User.tenant_id == 1 #hardcoded until auth available
        )
        
        updated_user = (await self.session.execute(sql_query)).scalar_one_or_none()

        if not updated_user:
            raise NotFoundException("User not found")

        # 3) Authorization check (commented out until auth is implemented)
        # if caller.role == UserRole.READER:
        #     raise SecurinestException(
        #         status_code=403,
        #         message="Not authorized to update user"
        #     )
        #
        # if caller.role == UserRole.MODERATOR:
        #     if payload.role is not None and payload.role != UserRole.READER:
        #         raise SecurinestException(
        #             status_code=403,
        #             message="Moderators may only assign role=READER"
        #         )

        if payload.email is not None:
            updated_user.email = str(payload.email)

        if payload.first_name is not None:
            updated_user.first_name = payload.first_name

        if payload.last_name is not None:
            updated_user.last_name = payload.last_name

        if payload.role is not None:
            updated_user.role = payload.role

        if payload.is_active is not None:
            updated_user.is_active = payload.is_active

        try:
            self.session.add(updated_user)
            await self.session.commit()
            await self.session.refresh(updated_user)

            return UserUpdateResponse.model_validate(updated_user)
        except Exception as e:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(e)

    async def delete_user(
            self,
            user_id: int,
            # caller
    ) -> None:
        sql_query = select(User).where(
            User.id == user_id,
            User.tenant_id == 1 #hard-coded until auth available
        )
        
        deleted_user = (await self.session.execute(sql_query)).scalar_one_or_none()

        if not deleted_user:
            raise NotFoundException("User not found")

        # 3) Authorization (commented out until auth is implemented)
        # if caller.role != UserRole.ADMIN:
        #     raise SecurinestException(
        #         status_code=403,
        #         message="Not authorized to delete user"
        #     )

        try:
            await self.session.delete(deleted_user)
            await self.session.commit()
        except Exception as e:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(e)
