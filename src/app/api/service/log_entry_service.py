from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from src.app.api.schema.log_entry.create_log_entry_request import CreateLogEntryRequest
from src.app.api.schema.log_entry.create_log_entry_response import CreateLogEntryResponse
from src.app.api.schema.log_entry.read_log_entry_response import ReadLogEntryResponse
from src.app.api.schema.log_entry.update_log_entry_request import UpdateLogEntryRequest
from src.app.api.schema.log_entry.update_log_entry_response import UpdateLogEntryResponse
from src.app.core.db_connection.db_connector import get_session
from src.app.core.exception.exception_util import ExceptionUtil
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.dao.entity import LogEntry


async def get_log_entry_service(session: AsyncSession = Depends(get_session)) -> "LogEntryService":
    return LogEntryService(session)


class LogEntryService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_log_entry_by_id(self, log_entry_id: int) -> ReadLogEntryResponse:
        try:
            log_entry = await self.session.get(LogEntry, log_entry_id)

            if not log_entry:
                raise NotFoundException(f"Log entry with id {log_entry_id} does not exist.")

            return ReadLogEntryResponse.model_validate(log_entry)
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def get_all_log_entries(self) -> list[ReadLogEntryResponse]:
        try:
            log_entries = await self.session.execute(select(LogEntry))
            log_entries = log_entries.scalars().all()

            log_entry_responses = map(
                lambda log_entry: ReadLogEntryResponse.model_validate(log_entry),
                log_entries
            )

            return list(log_entry_responses)
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def save_log_entry(self, log_entry_request: CreateLogEntryRequest) -> CreateLogEntryResponse:
        new_log_entry = LogEntry(
            request_id=log_entry_request.request_id,
            api_key_id=log_entry_request.api_key_id,
            user_id=log_entry_request.user_id,
            tenant_id=log_entry_request.tenant_id,
            model=log_entry_request.model,
            prompt_text=log_entry_request.prompt_text,
            outcome=log_entry_request.outcome,
            rule_triggers=log_entry_request.rule_triggers,
            provider_latency_ms=log_entry_request.provider_latency_ms,
            prompt_tokens=log_entry_request.prompt_tokens,
            completion_tokens=log_entry_request.completion_tokens,
            masked_response=log_entry_request.masked_response
        )

        try:
            self.session.add(new_log_entry)
            await self.session.commit()
            await self.session.refresh(new_log_entry)

            return CreateLogEntryResponse.model_validate(new_log_entry)
        except Exception as exception:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(exception)

    async def update_log_entry(self, log_entry_id, log_entry_request: UpdateLogEntryRequest) -> UpdateLogEntryResponse:
        existing_log_entry = await self.session.get(LogEntry, log_entry_id)

        if not existing_log_entry:
            raise NotFoundException(f"Log entry with id {log_entry_id} does not exist.")

        existing_log_entry.request_id = log_entry_request.request_id
        existing_log_entry.api_key_id = log_entry_request.api_key_id
        existing_log_entry.user_id = log_entry_request.user_id
        existing_log_entry.tenant_id = log_entry_request.tenant_id
        existing_log_entry.model = log_entry_request.model
        existing_log_entry.prompt_text = log_entry_request.prompt_text
        existing_log_entry.outcome = log_entry_request.outcome
        existing_log_entry.rule_triggers = log_entry_request.rule_triggers
        existing_log_entry.provider_latency_ms = log_entry_request.provider_latency_ms
        existing_log_entry.prompt_tokens = log_entry_request.prompt_tokens
        existing_log_entry.completion_tokens = log_entry_request.completion_tokens
        existing_log_entry.masked_response = log_entry_request.masked_response

        try:
            self.session.add(existing_log_entry)
            await self.session.commit()
            await self.session.refresh(existing_log_entry)

            return UpdateLogEntryResponse.model_validate(existing_log_entry)
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def delete_log_entry_by_id(self, log_entry_id: int):
        log_entry = await self.session.get(LogEntry, log_entry_id)

        if not log_entry:
            raise NotFoundException(f"Log entry with id {log_entry_id} does not exist.")

        try:
            await self.session.delete(log_entry)
            await self.session.commit()
        except Exception as exception:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(exception)
