from typing import List
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from src.app.api.schema.plan.plan_create_request import PlanCreateRequest
from src.app.api.schema.plan.plan_create_response import PlanCreateResponse
from src.app.api.schema.plan.plan_read_response import Plan<PERSON>eadR<PERSON>ponse
from src.app.api.schema.plan.plan_update_request import PlanUpdateRequest
from src.app.api.schema.plan.plan_update_response import PlanUpdateResponse
from src.app.core.db_connection.db_connector import get_session
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.core.exception.processing_exception import ProcessingException
from src.app.dao.entity.plan import Plan


class PlanService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def create_plan(self, request: PlanCreateRequest) -> PlanCreateResponse:
        """Create a new plan."""
        try:
            # Check if plan with same code already exists
            existing = await self.session.scalar(
                select(Plan).where(Plan.code == request.code)
            )
            if existing:
                raise ProcessingException(f"Plan with code '{request.code}' already exists")

            plan = Plan(
                code=request.code,
                monthly_price=request.monthly_price,
                requests_per_day=request.requests_per_day,
                trial_days=request.trial_days,
                max_allowed_users=request.max_allowed_users,
                audit_report=request.audit_report,
                max_allowed_rules=request.max_allowed_rules,
                analytics_dashboard=request.analytics_dashboard,
                advanced_api=request.advanced_api
            )
            
            self.session.add(plan)
            await self.session.commit()
            await self.session.refresh(plan)
            
            return PlanCreateResponse.model_validate(plan)
        except ProcessingException:
            raise
        except Exception as e:
            await self.session.rollback()
            raise ProcessingException(f"Failed to create plan: {str(e)}")

    async def get_plan_by_id(self, plan_id: int) -> PlanReadResponse:
        """Get a plan by ID."""
        plan = await self.session.get(Plan, plan_id)
        if not plan:
            raise NotFoundException(f"Plan with ID {plan_id} not found")
        return PlanReadResponse.model_validate(plan)

    async def get_all_plans(self) -> List[PlanReadResponse]:
        """Get all plans."""
        result = await self.session.execute(select(Plan))
        plans = result.scalars().all()
        return [PlanReadResponse.model_validate(plan) for plan in plans]

    async def update_plan(self, plan_id: int, request: PlanUpdateRequest) -> PlanUpdateResponse:
        """Update a plan."""
        try:
            plan = await self.session.get(Plan, plan_id)
            if not plan:
                raise NotFoundException(f"Plan with ID {plan_id} not found")

            # Check if code is being changed and if it conflicts
            if request.code and request.code != plan.code:
                existing = await self.session.scalar(
                    select(Plan).where(Plan.code == request.code, Plan.id != plan_id)
                )
                if existing:
                    raise ProcessingException(f"Plan with code '{request.code}' already exists")

            # Update fields
            update_data = request.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(plan, field, value)

            await self.session.commit()
            await self.session.refresh(plan)
            
            return PlanUpdateResponse.model_validate(plan)
        except (NotFoundException, ProcessingException):
            raise
        except Exception as e:
            await self.session.rollback()
            raise ProcessingException(f"Failed to update plan: {str(e)}")

    async def delete_plan(self, plan_id: int) -> bool:
        """Delete a plan."""
        try:
            plan = await self.session.get(Plan, plan_id)
            if not plan:
                raise NotFoundException(f"Plan with ID {plan_id} not found")

            # Check if plan has active subscriptions
            from src.app.dao.entity.subscription import Subscription
            subscription = await self.session.scalar(
                select(Subscription).where(Subscription.plan_id == plan_id)
            )
            if subscription:
                raise ProcessingException("Cannot delete plan with active subscriptions")

            await self.session.delete(plan)
            await self.session.commit()
            return True
        except (NotFoundException, ProcessingException):
            raise
        except Exception as e:
            await self.session.rollback()
            raise ProcessingException(f"Failed to delete plan: {str(e)}")


def get_plan_service(session: AsyncSession = Depends(get_session)) -> PlanService:
    return PlanService(session)
