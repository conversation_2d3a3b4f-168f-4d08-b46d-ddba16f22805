from typing import List
from datetime import datetime, timezone
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from src.app.api.schema.subscription.subscription_create_request import SubscriptionCreateRequest
from src.app.api.schema.subscription.subscription_create_response import SubscriptionCreateResponse
from src.app.api.schema.subscription.subscription_read_response import SubscriptionReadResponse
from src.app.api.schema.subscription.subscription_update_request import SubscriptionUpdateRequest
from src.app.api.schema.subscription.subscription_update_response import SubscriptionUpdateResponse
from src.app.api.service.plan_service import PlanService, get_plan_service
from src.app.api.service.tenant_service import TenantService, get_tenant_service
from src.app.core.db_connection.db_connector import get_session
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.core.exception.processing_exception import ProcessingException
from src.app.dao.entity.subscription import Subscription
from src.app.dao.entity.tenant import Tenant
from src.app.dao.entity.plan import Plan


class SubscriptionService:
    def __init__(self, session: AsyncSession, tenant_service: TenantService, plan_service: PlanService):
        self.session = session
        self.tenant_service = tenant_service
        self.plan_service = plan_service

    async def create_subscription(self, request: SubscriptionCreateRequest) -> SubscriptionCreateResponse:
        """Create a new subscription."""
        try:
            # Validate tenant exists
            tenant = await self.tenant_service.get_tenant_by_id(request.tenant_id)
            if not tenant:
                raise ProcessingException(f"Tenant with ID {request.tenant_id} not found")

            # Validate plan exists
            plan = await self.plan_service.get_plan_by_id(request.plan_id)
            if not plan:
                raise ProcessingException(f"Plan with ID {request.plan_id} not found")

            # Check if tenant already has an active subscription
            existing = await self.session.scalar(
                select(Subscription).where(
                    Subscription.tenant_id == request.tenant_id,
                    Subscription.status.in_(["ACTIVE", "TRIALING"])
                )
            )
            if existing:
                raise ProcessingException(f"Tenant {request.tenant_id} already has an active subscription")

            subscription = Subscription(
                tenant_id=request.tenant_id,
                plan_id=request.plan_id,
                status=request.status,
                started_at=request.started_at or datetime.now(timezone.utc),
                ends_at=request.ends_at,
                trial_ends_at=request.trial_ends_at,
                stripe_subscription_id=request.stripe_subscription_id
            )
            
            self.session.add(subscription)
            await self.session.commit()
            await self.session.refresh(subscription)
            
            return SubscriptionCreateResponse.model_validate(subscription)
        except ProcessingException:
            raise
        except Exception as e:
            await self.session.rollback()
            raise ProcessingException(f"Failed to create subscription: {str(e)}")

    async def get_subscription_by_id(self, subscription_id: int) -> SubscriptionReadResponse:
        """Get a subscription by ID."""
        subscription = await self.session.get(Subscription, subscription_id)
        if not subscription:
            raise NotFoundException(f"Subscription with ID {subscription_id} not found")
        return SubscriptionReadResponse.model_validate(subscription)

    async def get_subscriptions_by_tenant(self, tenant_id: int) -> List[SubscriptionReadResponse]:
        """Get all subscriptions for a tenant."""
        result = await self.session.execute(
            select(Subscription).where(Subscription.tenant_id == tenant_id)
        )
        subscriptions = result.scalars().all()
        return [SubscriptionReadResponse.model_validate(sub) for sub in subscriptions]

    async def get_all_subscriptions(self) -> List[SubscriptionReadResponse]:
        """Get all subscriptions."""
        result = await self.session.execute(select(Subscription))
        subscriptions = result.scalars().all()
        return [SubscriptionReadResponse.model_validate(sub) for sub in subscriptions]

    async def update_subscription(self, subscription_id: int, request: SubscriptionUpdateRequest) -> SubscriptionUpdateResponse:
        """Update a subscription."""
        try:
            subscription = await self.session.get(Subscription, subscription_id)
            if not subscription:
                raise NotFoundException(f"Subscription with ID {subscription_id} not found")

            # Validate plan if being changed
            if request.plan_id:
                plan = await self.plan_service.get_plan_by_id(request.plan_id)
                if not plan:
                    raise ProcessingException(f"Plan with ID {request.plan_id} not found")

            # Update fields
            update_data = request.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(subscription, field, value)

            await self.session.commit()
            await self.session.refresh(subscription)
            
            return SubscriptionUpdateResponse.model_validate(subscription)
        except (NotFoundException, ProcessingException):
            raise
        except Exception as e:
            await self.session.rollback()
            raise ProcessingException(f"Failed to update subscription: {str(e)}")

    async def delete_subscription(self, subscription_id: int) -> bool:
        """Delete a subscription."""
        try:
            subscription = await self.session.get(Subscription, subscription_id)
            if not subscription:
                raise NotFoundException(f"Subscription with ID {subscription_id} not found")

            # Check if subscription has usage records
            from src.app.dao.entity.usage_record import UsageRecord
            usage_record = await self.session.scalar(
                select(UsageRecord).where(UsageRecord.subscription_id == subscription_id)
            )
            if usage_record:
                raise ProcessingException("Cannot delete subscription with usage records")

            await self.session.delete(subscription)
            await self.session.commit()
            return True
        except (NotFoundException, ProcessingException):
            raise
        except Exception as e:
            await self.session.rollback()
            raise ProcessingException(f"Failed to delete subscription: {str(e)}")


def get_subscription_service(session: AsyncSession = Depends(get_session), tenant_service: TenantService = Depends(get_tenant_service), plan_service: PlanService = Depends(get_plan_service)) -> SubscriptionService:
    return SubscriptionService(session, tenant_service, plan_service)
