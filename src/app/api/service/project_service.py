from typing import List

from fastapi import Depends
from sqlalchemy.exc import Integrity<PERSON>rror
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from src.app.api.schema.project.project_create_request import ProjectCreateRequest
from src.app.api.schema.project.project_create_response import ProjectCreateResponse
from src.app.api.schema.project.project_read_response import ProjectReadResponse
from src.app.api.schema.project.project_update_request import ProjectUpdateRequest
from src.app.api.schema.project.project_update_response import ProjectUpdateResponse
from src.app.core.db_connection.db_connector import get_session
from src.app.core.exception.bad_request_exception import BadRequestException
from src.app.core.exception.exception_util import ExceptionUtil
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.dao.entity.project import Project


async def get_project_service(session: AsyncSession = Depends(get_session)) -> "ProjectService":
    return ProjectService(session)


class ProjectService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_project_by_id(
            self,
            project_id: int
            # caller
    ) -> ProjectReadResponse:
        try:
            #  tenant_id = request.tenant_id
            sql_query = select(Project).where(
                Project.id == project_id,
                Project.tenant_id == 1
            )
            project = (await self.session.execute(sql_query)).scalar_one_or_none()

            if not project:
                raise NotFoundException("Project not found")

                # 3) Authorization (commented out until auth is implemented)
                # if caller.role not in (UserRole.ADMIN, UserRole.MODERATOR, UserRole.READER):
                #     raise SecurinestException(
                #         status_code=403,
                #         message="Not authorized to view project"
                #     )

                # 4) Return
            return ProjectReadResponse.model_validate(project)
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)
    
    async def get_projects(self) -> List[ProjectReadResponse]:
        try:
            projects = (
                (
                    await self.session.scalars(
                        select(Project).where(Project.tenant_id == 1))  # hardcoded until auth available
                )
                .all()
            )

            return [ProjectReadResponse.model_validate(p) for p in projects]
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def create_project(
            self,
            payload: ProjectCreateRequest
            # caller
    ) -> ProjectCreateResponse:
        # 1) Authorization check (commented out until auth is implemented)
        # if caller.role not in (UserRole.ADMIN, UserRole.MODERATOR):
        #     raise SecurinestException(
        #         status_code=403,
        #         message="Not authorized to create projects"
        #     )

        tenant_id = 1  # hard-coded until auth available

        new_project = Project(
            name=payload.name,
            description=payload.description,
            tenant_id=tenant_id
        )

        try:
            self.session.add(new_project)
            await self.session.commit()
            await self.session.refresh(new_project)

            return ProjectCreateResponse.model_validate(new_project)
        except IntegrityError as e:
            await self.session.rollback()

            error_msg = str(e).lower()
            if "unique constraint" in error_msg and "name" in error_msg:
                raise BadRequestException("Project name already exists")
            elif "foreign key" in error_msg and "tenant" in error_msg:
                raise BadRequestException("Invalid tenant reference")
            else:
                raise BadRequestException(f"Database constraint violation: {str(e)}")

    async def update_project(
            self,
            project_id: int,
            payload: ProjectUpdateRequest
            # caller
    ) -> ProjectUpdateResponse:
        sql_query = select(Project).where(
            Project.id == project_id,
            Project.tenant_id == 1 #hardcoded until auth available
        )
        
        updated_project = (await self.session.execute(sql_query)).scalar_one_or_none()

        if not updated_project:
            raise NotFoundException("Project not found")

        # 3) Authorization check (commented out until auth is implemented)
        # if caller.role == UserRole.READER:
        #     raise SecurinestException(
        #         status_code=403,
        #         message="Not authorized to update project"
        #     )

        if payload.name is not None:
            updated_project.name = payload.name

        if payload.description is not None:
            updated_project.description = payload.description

        try:
            self.session.add(updated_project)
            await self.session.commit()
            await self.session.refresh(updated_project)

            return ProjectUpdateResponse.model_validate(updated_project)
        except IntegrityError as e:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(e)

    async def delete_project(self,
            project_id: int,
            # caller
    ) -> None:
        sql_query = select(Project).where(
            Project.id == project_id,
            Project.tenant_id == 1 #hard-coded until auth available
        )
        
        deleted_project = (await self.session.execute(sql_query)).scalar_one_or_none()

        if not deleted_project:
            raise NotFoundException("Project not found")

        # 3) Authorization (commented out until auth is implemented)
        # if caller.role != UserRole.ADMIN:
        #     raise SecurinestException(
        #         status_code=403,
        #         message="Not authorized to delete project"
        #     )

        try:
            await self.session.delete(deleted_project)
            await self.session.commit()
        except Exception as e:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(e)
