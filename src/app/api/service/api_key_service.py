from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql.expression import delete
from sqlmodel import select

from src.app.api.schema.api_key.create_api_key_request import CreateApiKeyRequest
from src.app.api.schema.api_key.create_api_key_response import CreateApiKeyResponse
from src.app.api.schema.api_key.read_api_key_response import ReadApi<PERSON>eyResponse
from src.app.api.schema.api_key.update_api_key_request import UpdateApiKeyRequest
from src.app.api.schema.api_key.update_api_key_response import UpdateApiKeyResponse
from src.app.core.db_connection.db_connector import get_session
from src.app.core.exception.exception_util import ExceptionUtil
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.dao.entity import Api<PERSON>ey


async def get_api_key_service(session: AsyncSession = Depends(get_session)) -> "ApiKeyService":
    return ApiKeyService(session)


class ApiKeyService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def save_api_key(self, api_key_request: CreateApiKeyRequest) -> CreateApiKeyResponse:
        new_api_key = ApiKey(
            hashed_token=api_key_request.hashed_token,
            label=api_key_request.label,
            tenant_id=api_key_request.tenant_id,
            user_id=api_key_request.user_id,
            project_id=api_key_request.project_id
        )

        try:
            self.session.add(new_api_key)
            await self.session.commit()
            await self.session.refresh(new_api_key)

            return CreateApiKeyResponse.model_validate(new_api_key)
        except Exception as exception:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(exception)

    async def update_api_key(self, api_key_id, api_key_request: UpdateApiKeyRequest) -> UpdateApiKeyResponse:
        existing_api_key = await self.session.get(ApiKey, api_key_id)

        if not existing_api_key:
            raise NotFoundException(f"ApiKey with id {api_key_id} not found.")

        existing_api_key.hashed_token = api_key_request.hashed_token
        existing_api_key.label = api_key_request.label
        existing_api_key.tenant_id = api_key_request.tenant_id
        existing_api_key.user_id = api_key_request.user_id
        existing_api_key.project_id = api_key_request.project_id

        try:
            self.session.add(existing_api_key)
            await self.session.commit()
            await self.session.refresh(existing_api_key)

            return UpdateApiKeyResponse.model_validate(existing_api_key)
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def get_api_key_by_id(self, api_key_id):
        try:
            api_key = await self.session.get(ApiKey, api_key_id)

            if not api_key:
                raise NotFoundException(f"ApiKey with id {api_key_id} not found.")

            return ReadApiKeyResponse.model_validate(api_key)
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def get_all_api_keys(self):
        try:
            api_keys = await self.session.execute(select(ApiKey))
            api_keys = api_keys.scalars().all()

            api_keys_responses = map(
                lambda api_key: ReadApiKeyResponse(
                    id=api_key.id,
                    hashed_token=api_key.hashed_token,
                    label=api_key.label,
                    creation_date=api_key.creation_date,
                    last_used=api_key.last_used,
                    status=api_key.status,
                    tenant_id=api_key.tenant_id,
                    user_id=api_key.user_id,
                    project_id=api_key.project_id,
                    updated_at=api_key.updated_at,
                ),
                api_keys
            )

            return list(api_keys_responses)
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def delete_api_key_by_id(self, api_key_id):
        api_key = await self.session.get(ApiKey, api_key_id)

        if not api_key:
            raise NotFoundException(f"ApiKey with id {api_key_id} not found.")

        try:
            stmt = delete(ApiKey).where(ApiKey.id == api_key_id)
            await self.session.execute(stmt)
            await self.session.commit()
        except Exception as exception:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(exception)
