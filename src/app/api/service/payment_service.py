from typing import List
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from src.app.api.schema.payment.payment_create_request import PaymentCreateRequest
from src.app.api.schema.payment.payment_create_response import PaymentCreateResponse
from src.app.api.schema.payment.payment_read_response import PaymentReadResponse
from src.app.api.schema.payment.payment_update_request import PaymentUpdateRequest
from src.app.api.schema.payment.payment_update_response import PaymentUpdateResponse
from src.app.api.service.tenant_service import TenantService
from src.app.core.db_connection.db_connector import get_session
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.core.exception.processing_exception import ProcessingException
from src.app.dao.entity.payment import Payment
from src.app.dao.entity.tenant import Tenant


class PaymentService:
    def __init__(self, session: AsyncSession, tenant_service: TenantService):
        self.session = session
        self.tenant_service = tenant_service

    async def create_payment(self, request: PaymentCreateRequest) -> PaymentCreateResponse:
        """Create a new payment method."""
        try:
            # Validate tenant exists
            tenant = await self.tenant_service.get_tenant_by_id(request.tenant_id)
            if not tenant:
                raise ProcessingException(f"Tenant with ID {request.tenant_id} not found")

            # Check if payment method already exists
            existing = await self.session.scalar(
                select(Payment).where(Payment.stripe_payment_method_id == request.stripe_payment_method_id)
            )
            if existing:
                raise ProcessingException(f"Payment method '{request.stripe_payment_method_id}' already exists")

            # If this is set as default, unset other defaults for this tenant
            if request.is_default:
                await self._unset_default_payments(request.tenant_id)

            payment = Payment(
                tenant_id=request.tenant_id,
                is_default=request.is_default,
                stripe_payment_method_id=request.stripe_payment_method_id
            )
            
            self.session.add(payment)
            await self.session.commit()
            await self.session.refresh(payment)
            
            return PaymentCreateResponse.model_validate(payment)
        except ProcessingException:
            raise
        except Exception as e:
            await self.session.rollback()
            raise ProcessingException(f"Failed to create payment method: {str(e)}")

    async def get_payment_by_id(self, payment_id: int) -> PaymentReadResponse:
        """Get a payment method by ID."""
        payment = await self.session.get(Payment, payment_id)
        if not payment:
            raise NotFoundException(f"Payment method with ID {payment_id} not found")
        return PaymentReadResponse.model_validate(payment)

    async def get_payments_by_tenant(self, tenant_id: int) -> List[PaymentReadResponse]:
        """Get all payment methods for a tenant."""
        result = await self.session.execute(
            select(Payment).where(Payment.tenant_id == tenant_id)
        )
        payments = result.scalars().all()
        return [PaymentReadResponse.model_validate(payment) for payment in payments]

    async def get_all_payments(self) -> List[PaymentReadResponse]:
        """Get all payment methods."""
        result = await self.session.execute(select(Payment))
        payments = result.scalars().all()
        return [PaymentReadResponse.model_validate(payment) for payment in payments]

    async def update_payment(self, payment_id: int, request: PaymentUpdateRequest) -> PaymentUpdateResponse:
        """Update a payment method."""
        try:
            payment = await self.session.get(Payment, payment_id)
            if not payment:
                raise NotFoundException(f"Payment method with ID {payment_id} not found")

            # Check if stripe_payment_method_id is being changed and if it conflicts
            if request.stripe_payment_method_id and request.stripe_payment_method_id != payment.stripe_payment_method_id:
                existing = await self.session.scalar(
                    select(Payment).where(
                        Payment.stripe_payment_method_id == request.stripe_payment_method_id,
                        Payment.id != payment_id
                    )
                )
                if existing:
                    raise ProcessingException(f"Payment method '{request.stripe_payment_method_id}' already exists")

            # If this is being set as default, unset other defaults for this tenant
            if request.is_default:
                await self._unset_default_payments(payment.tenant_id, exclude_payment_id=payment_id)

            # Update fields
            update_data = request.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(payment, field, value)

            await self.session.commit()
            await self.session.refresh(payment)
            
            return PaymentUpdateResponse.model_validate(payment)
        except (NotFoundException, ProcessingException):
            raise
        except Exception as e:
            await self.session.rollback()
            raise ProcessingException(f"Failed to update payment method: {str(e)}")

    async def delete_payment(self, payment_id: int) -> bool:
        """Delete a payment method."""
        try:
            payment = await self.session.get(Payment, payment_id)
            if not payment:
                raise NotFoundException(f"Payment method with ID {payment_id} not found")

            await self.session.delete(payment)
            await self.session.commit()
            return True
        except NotFoundException:
            raise
        except Exception as e:
            await self.session.rollback()
            raise ProcessingException(f"Failed to delete payment method: {str(e)}")

    async def _unset_default_payments(self, tenant_id: int, exclude_payment_id: int = None) -> None:
        """Unset default flag for all payment methods of a tenant."""
        query = select(Payment).where(Payment.tenant_id == tenant_id, Payment.is_default == True)
        if exclude_payment_id:
            query = query.where(Payment.id != exclude_payment_id)
        
        result = await self.session.execute(query)
        payments = result.scalars().all()
        
        for payment in payments:
            payment.is_default = False


def get_payment_service(session: AsyncSession = Depends(get_session)) -> PaymentService:
    return PaymentService(session)
