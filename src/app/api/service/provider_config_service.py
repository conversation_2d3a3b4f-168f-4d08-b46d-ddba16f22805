from typing import List

from fastapi import Depends
from sqlalchemy.exc import Integrity<PERSON>rror
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from src.app.api.schema.provider_config.provider_config_create_request import ProviderConfigCreateRequest
from src.app.api.schema.provider_config.provider_config_create_response import ProviderConfigCreateResponse
from src.app.api.schema.provider_config.provider_config_read_response import ProviderConfigReadResponse
from src.app.api.schema.provider_config.provider_config_update_request import ProviderConfigUpdateRequest
from src.app.api.schema.provider_config.provider_config_update_response import ProviderConfigUpdateResponse
from src.app.core.db_connection.db_connector import get_session
from src.app.core.exception.exception_util import ExceptionUtil
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.dao.entity.provider_config import ProviderConfig


async def get_provider_config_service(session: AsyncSession = Depends(get_session)) -> "ProviderConfigService":
    return ProviderConfigService(session)


class ProviderConfigService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_provider_config_by_id(
            self,
            provider_config_id: int,
            # caller
    ) -> ProviderConfigReadResponse:
        #  tenant_id = request.tenant_id
        try:
            sql_query = select(ProviderConfig).where(
                ProviderConfig.id == provider_config_id,
                ProviderConfig.tenant_id == 1
            )

            provider_config = (await self.session.execute(sql_query)).scalar_one_or_none()

            if not provider_config:
                raise NotFoundException("Provider config not found")

        # 3) Authorization (commented out until auth is implemented)
        # if caller.role not in (UserRole.ADMIN, UserRole.MODERATOR, UserRole.READER):
        #     raise SecurinestException(
        #         status_code=403,
        #         message="Not authorized to view provider config"
        #     )

        # 4) Return
            return ProviderConfigReadResponse.model_validate(provider_config)
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def get_provider_configs(self) -> List[ProviderConfigReadResponse]:
        try:
            provider_configs = (
                (
                    await self.session.scalars(select(ProviderConfig).where(ProviderConfig.tenant_id == 1))
                    # hardcoded until auth available
                )
                .all()
            )

            return [ProviderConfigReadResponse.model_validate(pc) for pc in provider_configs]
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def create_provider_config(
            self,
            payload: ProviderConfigCreateRequest
            # caller
    ) -> ProviderConfigCreateResponse:
        # 1) Authorization check (commented out until auth is implemented)
        # if caller.role not in (UserRole.ADMIN, UserRole.MODERATOR):
        #     raise SecurinestException(
        #         status_code=403,
        #         message="Not authorized to create provider configs"
        #     )

        tenant_id = 1  # hard-coded until auth available

        new_provider_config = ProviderConfig(
            provider_type=payload.provider_type,
            credentials=payload.credentials,
            default_model=payload.default_model,
            tenant_id=tenant_id
        )

        try:
            self.session.add(new_provider_config)
            await self.session.commit()
            await self.session.refresh(new_provider_config)

            return ProviderConfigCreateResponse.model_validate(new_provider_config)
        except Exception as e:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(e)

    async def update_provider_config(
            self,
            provider_config_id: int,
            payload: ProviderConfigUpdateRequest
            # caller
    ) -> ProviderConfigUpdateResponse:
        sql_query = select(ProviderConfig).where(
            ProviderConfig.id == provider_config_id,
            ProviderConfig.tenant_id == 1 #hardcoded until auth available
        )
        
        updated_provider_config = (await self.session.execute(sql_query)).scalar_one_or_none()

        if not updated_provider_config:
            raise NotFoundException("Provider config not found")

        # 3) Authorization check (commented out until auth is implemented)
        # if caller.role == UserRole.READER:
        #     raise SecurinestException(
        #         status_code=403,
        #         message="Not authorized to update provider config"
        #     )

        if payload.provider_type is not None:
            updated_provider_config.provider_type = payload.provider_type

        if payload.credentials is not None:
            updated_provider_config.credentials = payload.credentials

        if payload.default_model is not None:
            updated_provider_config.default_model = payload.default_model

        try:
            self.session.add(updated_provider_config)
            await self.session.commit()
            await self.session.refresh(updated_provider_config)

            return ProviderConfigUpdateResponse.model_validate(updated_provider_config)
        except IntegrityError as e:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(e)

    async def delete_provider_config(
            self,
            provider_config_id: int,
            # caller
    ) -> None:
        sql_query = select(ProviderConfig).where(
            ProviderConfig.id == provider_config_id,
            ProviderConfig.tenant_id == 1 #hard-coded until auth available
        )
        
        deleted_provider_config = (await self.session.execute(sql_query)).scalar_one_or_none()

        if not deleted_provider_config:
            raise NotFoundException("Provider config not found")

        # 3) Authorization (commented out until auth is implemented)
        # if caller.role != UserRole.ADMIN:
        #     raise SecurinestException(
        #         status_code=403,
        #         message="Not authorized to delete provider config"
        #     )

        try:
            await self.session.delete(deleted_provider_config)
            await self.session.commit()
        except Exception as exception:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(exception)
