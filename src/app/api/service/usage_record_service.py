from typing import List
from datetime import datetime, timezone
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from src.app.api.schema.usage_record.usage_record_create_request import UsageRecordCreateRequest
from src.app.api.schema.usage_record.usage_record_create_response import UsageRecordCreateResponse
from src.app.api.schema.usage_record.usage_record_read_response import UsageRecordReadResponse
from src.app.api.schema.usage_record.usage_record_update_request import UsageRecordUpdateRequest
from src.app.api.schema.usage_record.usage_record_update_response import UsageRecordUpdateResponse
from src.app.core.db_connection.db_connector import get_session
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.core.exception.processing_exception import ProcessingException
from src.app.dao.entity.usage_record import UsageRecord
from src.app.dao.entity.subscription import Subscription


class UsageRecordService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def create_usage_record(self, request: UsageRecordCreateRequest) -> UsageRecordCreateResponse:
        """Create a new usage record."""
        try:
            # Validate subscription exists
            subscription = await self.session.scalar(
                select(Subscription).where(Subscription.id == request.subscription_id)
            )
            if not subscription:
                raise ProcessingException(f"Subscription with ID {request.subscription_id} not found")

            usage_record = UsageRecord(
                subscription_id=request.subscription_id,
                metric=request.metric,
                used=request.used,
                recorded_at=request.recorded_at or datetime.now(timezone.utc)
            )
            
            self.session.add(usage_record)
            await self.session.commit()
            await self.session.refresh(usage_record)
            
            return UsageRecordCreateResponse.model_validate(usage_record)
        except ProcessingException:
            raise
        except Exception as e:
            await self.session.rollback()
            raise ProcessingException(f"Failed to create usage record: {str(e)}")

    async def get_usage_record_by_id(self, usage_record_id: int) -> UsageRecordReadResponse:
        """Get a usage record by ID."""
        usage_record = await self.session.get(UsageRecord, usage_record_id)
        if not usage_record:
            raise NotFoundException(f"Usage record with ID {usage_record_id} not found")
        return UsageRecordReadResponse.model_validate(usage_record)

    async def get_usage_records_by_subscription(self, subscription_id: int) -> List[UsageRecordReadResponse]:
        """Get all usage records for a subscription."""
        result = await self.session.execute(
            select(UsageRecord).where(UsageRecord.subscription_id == subscription_id)
        )
        usage_records = result.scalars().all()
        return [UsageRecordReadResponse.model_validate(record) for record in usage_records]

    async def get_usage_records_by_metric(self, subscription_id: int, metric: str) -> List[UsageRecordReadResponse]:
        """Get usage records for a specific subscription and metric."""
        result = await self.session.execute(
            select(UsageRecord).where(
                UsageRecord.subscription_id == subscription_id,
                UsageRecord.metric == metric
            )
        )
        usage_records = result.scalars().all()
        return [UsageRecordReadResponse.model_validate(record) for record in usage_records]

    async def get_all_usage_records(self, limit: int = 100, offset: int = 0) -> List[UsageRecordReadResponse]:
        """Get all usage records with pagination."""
        result = await self.session.execute(
            select(UsageRecord).limit(limit).offset(offset)
        )
        usage_records = result.scalars().all()
        return [UsageRecordReadResponse.model_validate(record) for record in usage_records]

    async def update_usage_record(self, usage_record_id: int, request: UsageRecordUpdateRequest) -> UsageRecordUpdateResponse:
        """Update a usage record."""
        try:
            usage_record = await self.session.get(UsageRecord, usage_record_id)
            if not usage_record:
                raise NotFoundException(f"Usage record with ID {usage_record_id} not found")

            # Update fields
            update_data = request.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(usage_record, field, value)

            await self.session.commit()
            await self.session.refresh(usage_record)
            
            return UsageRecordUpdateResponse.model_validate(usage_record)
        except NotFoundException:
            raise
        except Exception as e:
            await self.session.rollback()
            raise ProcessingException(f"Failed to update usage record: {str(e)}")

    async def delete_usage_record(self, usage_record_id: int) -> bool:
        """Delete a usage record."""
        try:
            usage_record = await self.session.get(UsageRecord, usage_record_id)
            if not usage_record:
                raise NotFoundException(f"Usage record with ID {usage_record_id} not found")

            await self.session.delete(usage_record)
            await self.session.commit()
            return True
        except NotFoundException:
            raise
        except Exception as e:
            await self.session.rollback()
            raise ProcessingException(f"Failed to delete usage record: {str(e)}")


def get_usage_record_service(session: AsyncSession = Depends(get_session)) -> UsageRecordService:
    return UsageRecordService(session)
