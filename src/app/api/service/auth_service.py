import logging
from typing import Optional, Dict, Any, Tuple
from fastapi import Depends, HTTPException, status, Request, Response
from sqlalchemy.ext.asyncio import AsyncSession

from src.app.api.service.keycloak_service import get_keycloak_service, KeycloakService
from src.app.api.schema.auth.login_request import LoginRequest
from src.app.api.schema.auth.login_response import LoginResponse
from src.app.api.schema.auth.callback_request import CallbackRequest
from src.app.api.schema.auth.callback_response import CallbackResponse
from src.app.api.schema.auth.refresh_token_response import RefreshTokenResponse
from src.app.api.schema.auth.logout_request import LogoutRequest
from src.app.api.schema.auth.logout_response import LogoutResponse
from src.app.core.config.keycloak_config import keycloak_config
from src.app.core.config.auth_config import auth_config
from src.app.core.db_connection.db_connector import get_session
from src.app.core.exception.processing_exception import ProcessingException
from src.app.core.util.auth_utils import AuthUtils, AuthUtilsError
from src.app.core.util.session_state_manager import session_state_manager, SessionStateError
from src.app.core.security.keycloak_auth import keycloak_auth, KeycloakAuthError
from src.app.core.model.user_context import UserContext

logger = logging.getLogger(__name__)


async def get_auth_service(
    session: AsyncSession = Depends(get_session),
    keycloak_service: KeycloakService = Depends(get_keycloak_service)
) -> "AuthService":
    return AuthService(session, keycloak_service)


class AuthService:
    def __init__(self, session: AsyncSession, keycloak_service: KeycloakService):
        self.session = session
        self.keycloak_service = keycloak_service
    
    async def initiate_login(self, request: LoginRequest) -> LoginResponse:
        try:
            logger.info(f"Initiating login for tenant {request.tenant_id}")
            
            realm_name = await self.keycloak_service.get_realm_name_by_tenant_id(request.tenant_id)
            
            state = AuthUtils.generate_state()
            nonce = AuthUtils.generate_nonce()
            
            await session_state_manager.store_state(
                state=state,
                realm_name=realm_name,
                tenant_id=request.tenant_id,
                nonce=nonce,
                redirect_uri=request.redirect_uri
            )

            redirect_uri = keycloak_config.redirect_uri
            authorization_url = self.keycloak_service.build_authorization_url(
                realm_name=realm_name,
                redirect_uri=redirect_uri,
                state=state,
                nonce=nonce
            )

            response = LoginResponse(
                authorization_url=authorization_url,
                state=state,
                realm_name=realm_name
            )

            logger.info(f"Successfully initiated login for tenant {request.tenant_id}")
            return response
            
        except ProcessingException:
            raise
        except Exception as e:
            logger.error(f"Unexpected error initiating login: {e}")
            raise ProcessingException(f"Login initiation failed: {type(e).__name__}")
    
    async def handle_callback(
        self,
        callback_request: CallbackRequest,
        response: Response
    ) -> CallbackResponse:
        try:
            logger.info("Processing OAuth2 callback")

            if not callback_request.code or len(callback_request.code) > 1000:
                logger.warning("Invalid authorization code in callback")
                raise ProcessingException("Invalid authorization code")

            if not AuthUtils.verify_state(callback_request.state):
                logger.warning("Invalid state parameter in callback")
                raise ProcessingException("Invalid state parameter")

            if callback_request.error:
                error_msg = callback_request.error_description or callback_request.error
                logger.warning(f"OAuth2 error in callback: {error_msg}")
                raise ProcessingException(f"Authentication failed: {error_msg}")

            state_data = await session_state_manager.retrieve_state(callback_request.state)
            if not state_data:
                logger.warning("State not found or expired in callback")
                raise ProcessingException("Invalid or expired state parameter")

            realm_name = state_data['realm_name']
            tenant_id = state_data['tenant_id']
            nonce = state_data['nonce']

            if not realm_name or not tenant_id or not nonce:
                logger.warning("Incomplete state data in callback")
                raise ProcessingException("Invalid state data")

            redirect_uri = keycloak_config.redirect_uri
            token_data = await self.keycloak_service.exchange_code_for_tokens(
                code=callback_request.code,
                realm_name=realm_name,
                redirect_uri=redirect_uri
            )

            user_context = await keycloak_auth.extract_user_context(token_data['access_token'])

            cookie_settings = AuthUtils.get_cookie_settings()
            response.set_cookie(
                key=auth_config.refresh_cookie_name,
                value=token_data['refresh_token'],
                **cookie_settings
            )

            callback_response = CallbackResponse(
                access_token=token_data['access_token'],
                token_type=token_data['token_type'],
                expires_in=token_data['expires_in'],
                user_id=user_context.user_id,
                username=user_context.username,
                email=user_context.email,
                first_name=user_context.first_name,
                last_name=user_context.last_name,
                realm=user_context.realm,
                tenant_id=user_context.tenant_id,
                roles=user_context.roles
            )

            logger.info(f"Successfully processed callback for user {user_context.username} in tenant {tenant_id}")
            return callback_response

        except ProcessingException:
            raise
        except KeycloakAuthError as e:
            logger.error(f"Keycloak auth error in callback: {e}")
            raise ProcessingException(f"Authentication failed: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error in callback handling: {e}")
            raise ProcessingException(f"Callback handling failed: {type(e).__name__}")
    
    async def refresh_token(
        self, 
        request: Request, 
        response: Response
    ) -> RefreshTokenResponse:
        try:
            logger.info("Processing token refresh request")
            
            refresh_token = request.cookies.get(auth_config.refresh_cookie_name)
            if not refresh_token:
                logger.warning("No refresh token found in cookies")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="No refresh token available"
                )
            
            realm_name = await self.keycloak_service.validate_token_and_extract_realm(refresh_token)
            
            refresh_data = await self.keycloak_service.refresh_access_token(
                refresh_token=refresh_token,
                realm_name=realm_name
            )
            
            cookie_settings = AuthUtils.get_cookie_settings()
            response.set_cookie(
                key=auth_config.refresh_cookie_name,
                value=refresh_token,
                **cookie_settings
            )
            
            refresh_response = RefreshTokenResponse(
                access_token=refresh_data['access_token'],
                token_type=refresh_data['token_type'],
                expires_in=refresh_data['expires_in']
            )
            
            logger.info("Successfully refreshed access token")
            return refresh_response
            
        except HTTPException:
            raise
        except ProcessingException as e:
            logger.error(f"Processing error during token refresh: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=str(e)
            )
        except Exception as e:
            logger.error(f"Unexpected error during token refresh: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Token refresh failed"
            )
    
    async def logout_user(
        self, 
        request: Request, 
        response: Response, 
        logout_request: LogoutRequest
    ) -> LogoutResponse:
        try:
            logger.info("Processing logout request")
            
            refresh_token = request.cookies.get(auth_config.refresh_cookie_name)
            
            logout_success = True
            if refresh_token:
                try:
                    realm_name = await self.keycloak_service.validate_token_and_extract_realm(refresh_token)
                    
                    logout_success = await self.keycloak_service.logout_user(
                        refresh_token=refresh_token,
                        realm_name=realm_name
                    )
                    
                    logout_url = self.keycloak_service.build_logout_url(
                        realm_name=realm_name,
                        redirect_uri=logout_request.redirect_uri
                    )
                    
                except Exception as e:
                    logger.warning(f"Error during Keycloak logout: {e}")
                    logout_success = False
                    logout_url = logout_request.redirect_uri or keycloak_config.backend_url
            else:
                logger.info("No refresh token found - user already logged out")
                logout_url = logout_request.redirect_uri or keycloak_config.backend_url
            
            cookie_settings = AuthUtils.get_cookie_settings()
            response.delete_cookie(
                key=auth_config.refresh_cookie_name,
                domain=cookie_settings.get('domain'),
                secure=cookie_settings.get('secure'),
                httponly=True,
                samesite=cookie_settings.get('samesite')
            )
            
            logout_response = LogoutResponse(
                success=logout_success,
                message="Successfully logged out" if logout_success else "Logged out with warnings",
                logout_url=logout_url
            )
            
            logger.info(f"Logout completed with success: {logout_success}")
            return logout_response
            
        except Exception as e:
            logger.error(f"Unexpected error during logout: {e}")
            cookie_settings = AuthUtils.get_cookie_settings()
            response.delete_cookie(
                key=auth_config.refresh_cookie_name,
                domain=cookie_settings.get('domain'),
                secure=cookie_settings.get('secure'),
                httponly=True,
                samesite=cookie_settings.get('samesite')
            )
            
            return LogoutResponse(
                success=False,
                message="Logout completed with errors",
                logout_url=logout_request.redirect_uri or keycloak_config.backend_url
            )
