from typing import List

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from src.app.api.schema.notification.notification_create_request import NotificationCreateRequest
from src.app.api.schema.notification.notification_create_response import NotificationCreateResponse
from src.app.api.schema.notification.notification_read_response import NotificationReadResponse
from src.app.api.schema.notification.notification_update_request import NotificationUpdateRequest
from src.app.api.schema.notification.notification_update_response import NotificationUpdateResponse
from src.app.core.db_connection.db_connector import get_session
from src.app.core.exception.exception_util import ExceptionUtil
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.dao.entity.notification_event import NotificationEvent


async def get_notification_service(session: AsyncSession = Depends(get_session)) -> "NotificationService":
    return NotificationService(session)


class NotificationService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_notification_by_id(
            self,
            notification_id: int
            # caller
    ) -> NotificationReadResponse:
        try:
            sql_query = select(NotificationEvent).where(
                NotificationEvent.id == notification_id,
                NotificationEvent.tenant_id == 1
            )

            notification = (await self.session.execute(sql_query)).scalar_one_or_none()

            if not notification:
                raise Exception("Notification not found")

            # 3) Authorization (commented out until auth is implemented)
            # if caller.role not in (UserRole.ADMIN, UserRole.MODERATOR, UserRole.READER):
            #     raise SecurinestException(
            #         status_code=403,
            #         message="Not authorized to view notification"
            #     )

            # 4) Return
            return NotificationReadResponse.model_validate(notification)
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)
    
    async def get_notifications(self) -> List[NotificationReadResponse]:
        try:
            notifications = (
                (
                    await self.session.scalars(
                        select(NotificationEvent).where(NotificationEvent.tenant_id == 1)
                    )
                    # hardcoded until auth available
                )
                .all()
            )

            return [NotificationReadResponse.model_validate(n) for n in notifications]
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def create_notification(self, payload: NotificationCreateRequest) -> NotificationCreateResponse:
        tenant_id = 1  # hard-coded until auth available

        new_notification = NotificationEvent(
            event_type=payload.event_type,
            details=payload.details,
            sent_to=payload.sent_to,
            user_id=payload.user_id,
            api_key_id=payload.api_key_id,
            tenant_id=tenant_id
        )

        try:
            self.session.add(new_notification)
            await self.session.commit()
            await self.session.refresh(new_notification)

            return NotificationCreateResponse.model_validate(new_notification)
        except Exception as exception:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(exception)

    async def update_notification(
            self,
            notification_id: int,
            payload: NotificationUpdateRequest
            # caller,
    ) -> NotificationUpdateResponse:
        sql_query = select(NotificationEvent).where(
            NotificationEvent.id == notification_id,
            NotificationEvent.tenant_id == 1  # hardcoded until auth available
        )

        updated_notification = (await self.session.execute(sql_query)).scalar_one_or_none()

        if not updated_notification:
            raise NotFoundException("Notification not found")

        if payload.event_type is not None:
            updated_notification.event_type = payload.event_type

        if payload.details is not None:
            updated_notification.details = payload.details

        if payload.sent_to is not None:
            updated_notification.sent_to = payload.sent_to

        if payload.user_id is not None:
            updated_notification.user_id = payload.user_id

        if payload.api_key_id is not None:
            updated_notification.api_key_id = payload.api_key_id

        try:
            self.session.add(updated_notification)
            await self.session.commit()
            await self.session.refresh(updated_notification)

            return NotificationUpdateResponse.model_validate(updated_notification)
        except Exception as exception:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(exception)

    async def delete_notification(
            self,
            notification_id: int
            # caller
    ) -> None:
        sql_query = select(NotificationEvent).where(
            NotificationEvent.id == notification_id,
            NotificationEvent.tenant_id == 1  # hard-coded until auth available
        )

        deleted_notification = (await self.session.execute(sql_query)).scalar_one_or_none()

        if not deleted_notification:
            raise NotFoundException("Notification not found")

        # 3) Authorization (commented out until auth is implemented)
        # if caller.role != UserRole.ADMIN:
        #     raise SecurinestException(
        #         status_code=403,
        #         message="Not authorized to delete notification"
        #     )
        try:
            await self.session.delete(deleted_notification)
            await self.session.commit()
        except Exception as exception:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(exception)
