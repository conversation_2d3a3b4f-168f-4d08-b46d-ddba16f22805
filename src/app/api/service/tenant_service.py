import logging

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from src.app.api.schema.tenant.create_tenant_request import CreateTenantRequest
from src.app.api.schema.tenant.create_tenant_response import CreateTenantResponse
from src.app.api.schema.tenant.read_tenant_response import ReadTenantResponse
from src.app.api.schema.tenant.update_tenant_request import UpdateTenantRequest
from src.app.api.schema.tenant.update_tenant_response import UpdateTenantResponse
from src.app.api.service.keycloak_service import get_keycloak_service
from src.app.core.constant.constants import UserRole
from src.app.core.db_connection.db_connector import get_session
from src.app.core.exception.exception_util import ExceptionUtil
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.core.exception.processing_exception import ProcessingException
from src.app.core.service.email_service import get_email_service
from src.app.core.util.auth_utils import AuthUtils
from src.app.core.util.email_utils import EmailUtils
from src.app.core.model.user_context import UserContext

from src.app.dao.entity import Tenant, User
from src.app.dao.entity.tenant_status import TenantStatus

logger = logging.getLogger(__name__)


async def get_tenant_service(session: AsyncSession = Depends(get_session)) -> "TenantService":
    return TenantService(session)


class TenantService:
    def __init__(self, session: AsyncSession):
        self.session = session
        
    async def get_tenant_by_id(self, tenant_id: int) -> ReadTenantResponse:
        try:
            tenant = await self.session.get(Tenant, tenant_id)

            if not tenant:
                raise NotFoundException(f"Tenant with id {tenant_id} does not exist.")

            return ReadTenantResponse.model_validate(tenant)
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def get_all_tenants(self) -> list[ReadTenantResponse]:
        try:
            tenants = await self.session.execute(select(Tenant))
            tenants = tenants.scalars().all()

            tenant_responses = map(
                lambda tenant: ReadTenantResponse.model_validate(tenant),
                tenants
            )

            return list(tenant_responses)
        except Exception as exception:
            raise ExceptionUtil.process_exception(exception)

    async def create_tenant(self, create_request: CreateTenantRequest) -> CreateTenantResponse:
        logger.info(f"Starting tenant creation for organization: {create_request.organization_name}")

        new_tenant = Tenant(
            organization_name=create_request.organization_name,
            industry=create_request.industry,
            region_preference=create_request.region_preference.value,
            subscription_level=create_request.subscription_level.value,
            status=TenantStatus.PENDING.value
        )

        try:
            self.session.add(new_tenant)
            await self.session.flush()

            logger.info(f"Prepared tenant with ID: {new_tenant.id}")

            password_hash = AuthUtils.hash_password(create_request.admin_password)
            email_utils = EmailUtils()
            verification_token, token_expiration = email_utils.generate_verification_token()

            admin_user = User(
                email=create_request.admin_email,
                first_name=create_request.admin_first_name,
                last_name=create_request.admin_last_name,
                role=UserRole.TENANT_ADMIN.value,
                is_active=False,
                tenant_id=new_tenant.id,
                password_hash=password_hash,
                email_verified=False,
                email_verification_token=verification_token,
                email_verification_expires=token_expiration
            )

            self.session.add(admin_user)
            await self.session.flush()
            logger.info(f"Prepared admin user with ID: {admin_user.id}")

            response = CreateTenantResponse(
                id=new_tenant.id,
                organization_name=new_tenant.organization_name,
                industry=new_tenant.industry,
                region_preference=create_request.region_preference,
                subscription_level=create_request.subscription_level,
                status=new_tenant.status,
                admin_email=str(create_request.admin_email),
            )

            logger.info("Response validation successful")

            try:
                email_service = await get_email_service(self.session)
                email_sent = await email_service.send_verification_email(
                    to_email=str(create_request.admin_email),
                    organization_name=create_request.organization_name,
                    verification_token=verification_token,
                    admin_first_name=create_request.admin_first_name
                )

                if not email_sent:
                    raise ProcessingException("Failed to send verification email")

                logger.info(f"Verification email sent successfully to {create_request.admin_email}")

            except Exception as email_error:
                logger.error(f"Email service error: {email_error}")
                await self.session.rollback()

                raise ProcessingException(f"Failed to send verification email: {str(email_error)}")

            await self.session.commit()
            logger.info(f"Database transaction committed for tenant {new_tenant.id}")
            logger.info(f"Completed tenant creation for organization: {create_request.organization_name}")

            return response
        except Exception as e:
            logger.error(f"Failed to create tenant signup: {e}")
            await self.session.rollback()

            raise ProcessingException(f"Failed to create tenant: {str(e)}")

    async def update_tenant(self, tenant_id, tenant_request: UpdateTenantRequest) -> UpdateTenantResponse:
        existing_tenant = await self.session.get(Tenant, tenant_id)

        if not existing_tenant:
            raise NotFoundException(f"Tenant with id {tenant_id} does not exist.")

        existing_tenant.organization_name = tenant_request.organization_name
        existing_tenant.industry = tenant_request.industry
        existing_tenant.region_preference = tenant_request.region_preference
        existing_tenant.subscription_level = tenant_request.subscription_level

        try:
            self.session.add(existing_tenant)
            await self.session.commit()
            await self.session.refresh(existing_tenant)

            return UpdateTenantResponse.model_validate(existing_tenant)
        except Exception as exception:
            await self.session.rollback()

            raise ExceptionUtil.process_exception(exception)

    async def delete_tenant_by_id(self, tenant_id: int, current_user: UserContext, cleanup_keycloak: bool = True):
        tenant = await self.session.get(Tenant, tenant_id)

        if not tenant:
            raise NotFoundException(f"Tenant with id {tenant_id} does not exist.")

        logger.info(f"Deleting tenant {tenant_id}, cleanup_keycloak={cleanup_keycloak}")

        try:
            if cleanup_keycloak:
                try:
                    keycloak_service = await get_keycloak_service(self.session)
                    keycloak_deleted = await keycloak_service.delete_tenant_realm(str(tenant_id), current_user)

                    if keycloak_deleted:
                        logger.info(f"Successfully deleted Keycloak realm for tenant {tenant_id}")
                    else:
                        logger.warning(f"Failed to delete Keycloak realm for tenant {tenant_id}")

                except Exception as e:
                    logger.error(f"Error deleting Keycloak realm for tenant {tenant_id}: {e}")

            # Delete the tenant from database
            await self.session.delete(tenant)
            await self.session.commit()

            logger.info(f"Successfully deleted tenant {tenant_id}")
        except Exception as exception:
            await self.session.rollback()
            raise ExceptionUtil.process_exception(exception)
