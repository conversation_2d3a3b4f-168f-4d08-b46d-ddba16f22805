import logging
from typing import Optional, Dict, Any
import asyncio
from contextlib import asynccontextmanager
from typing import Optional, Dict, Any, TypedDict, List
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from keycloak import KeycloakAdmin, KeycloakOpenID
from keycloak.exceptions import KeycloakError, KeycloakGetError, KeycloakPostError
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.status import HTTP_409_CONFLICT

from src.app.api.schema.keycloak.keycloak_provision_request import KeycloakProvisionRequest
from src.app.api.schema.keycloak.keycloak_provision_response import KeycloakProvisionResponse
from src.app.core.config.keycloak_config import keycloak_config, get_realm_name
from src.app.core.constant.constants import Use<PERSON><PERSON><PERSON>
from src.app.core.db_connection.db_connector import get_session
from src.app.core.exception.forbidden_exception import ForbiddenException
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.core.exception.processing_exception import ProcessingException
from src.app.core.util.auth_utils import AuthUtils, AuthUtilsError
from src.app.dao.entity.tenant_keycloak_config import TenantKeycloakConfig
from src.app.core.security.client_secret_manager import get_client_secret_manager
from src.app.core.exception.service_unavailable_exception import ServiceUnavailableException
from src.app.core.model.user_context import UserContext

logger = logging.getLogger(__name__)


class RealmPayload(TypedDict):
    realm: str
    enabled: bool
    displayName: str
    registrationAllowed: bool
    loginWithEmailAllowed: bool
    duplicateEmailsAllowed: bool
    resetPasswordAllowed: bool
    editUsernameAllowed: bool
    bruteForceProtected: bool
    permanentLockout: bool
    maxFailureWaitSeconds: int
    minimumQuickLoginWaitSeconds: int
    waitIncrementSeconds: int
    quickLoginCheckMilliSeconds: int
    maxDeltaTimeSeconds: int
    failureFactor: int


class ClientPayload(TypedDict):
    clientId: str
    name: str
    description: str
    enabled: bool
    clientAuthenticatorType: str
    redirectUris: List[str]
    webOrigins: List[str]
    protocol: str
    publicClient: bool
    bearerOnly: bool
    consentRequired: bool
    standardFlowEnabled: bool
    implicitFlowEnabled: bool
    directAccessGrantsEnabled: bool
    serviceAccountsEnabled: bool
    authorizationServicesEnabled: bool
    fullScopeAllowed: bool


class UserPayload(TypedDict):
    username: str
    email: str
    firstName: str
    lastName: str
    enabled: bool
    emailVerified: bool
    credentials: List[Dict[str, Any]]


class RolePayload(TypedDict):
    name: str
    description: str
    composite: bool
    clientRole: bool


class ClientData(TypedDict):
    client_id: str
    client_secret: str


class TokenData(TypedDict):
    access_token: str
    refresh_token: str
    expires_in: int
    token_type: str


class TokenRefreshData(TypedDict):
    access_token: str
    expires_in: int
    token_type: str


async def get_keycloak_service(
    session: AsyncSession = Depends(get_session)
) -> "KeycloakService":
    return KeycloakService(session)


def get_keycloak_failed_response(e, realm_name, request):
    return KeycloakProvisionResponse(
        tenant_id=request.tenant_id,
        realm_name=realm_name,
        client_id="",
        client_secret="",
        admin_user_id=None,
        realm_roles=[],
        redirect_uri="",
        success=False,
        message=f"Failed to provision tenant: {str(e)}"
    )


def get_real_payload(realm_name):
    return {
        "realm": realm_name,
        "enabled": True,
        "displayName": f"Tenant Realm: {realm_name}",
        "registrationAllowed": False,
        "loginWithEmailAllowed": True,
        "duplicateEmailsAllowed": False,
        "resetPasswordAllowed": True,
        "editUsernameAllowed": False,
        "bruteForceProtected": True,
        "permanentLockout": False,
        "maxFailureWaitSeconds": 900,
        "minimumQuickLoginWaitSeconds": 60,
        "waitIncrementSeconds": 60,
        "quickLoginCheckMilliSeconds": 1000,
        "maxDeltaTimeSeconds": 43200,
        "failureFactor": 30
    }


def create_admin_client(realm_name: str = None) -> KeycloakAdmin:
    """
    Create a new KeycloakAdmin client instance for each operation.

    This eliminates shared state and ensures thread safety.
    Each request gets its own admin client with the correct realm.
    """
    try:
        return KeycloakAdmin(
            server_url=keycloak_config.keycloak_url,
            username=keycloak_config.admin_user,
            password=keycloak_config.admin_password,
            realm_name=realm_name or keycloak_config.master_realm,
            verify=True
        )
    except Exception as e:
        logger.error(f"Failed to connect to Keycloak: {e}")

        raise ProcessingException(f"Failed to connect to Keycloak: {e}")


def get_user_payload(request):
    return {
        "username": request.admin_username,
        "email": request.admin_email,
        "firstName": request.admin_first_name or "",
        "lastName": request.admin_last_name or "",
        "enabled": True,
        "emailVerified": True,
        "credentials": [{
            "type": "password",
            "value": request.admin_password,
            "temporary": False
        }]
    }


async def _create_realm(realm_name: str) -> str:
    try:
        # Create master realm admin client for realm creation
        admin_client = create_admin_client(keycloak_config.master_realm)
        realm_payload = get_real_payload(realm_name)
        admin_client.create_realm(payload=realm_payload)

        return realm_name

    except KeycloakPostError as e:
        if str(HTTP_409_CONFLICT) in str(e):
            logger.warning(f"Realm {realm_name} already exists")

            return realm_name
        raise ProcessingException(f"Failed to create realm: {e}")
    except Exception as e:
        raise ProcessingException(f"Failed to create realm: {e}")


async def _cleanup_failed_provisioning(realm_name: str) -> None:
    try:
        logger.info(f"Attempting cleanup for failed provisioning of realm: {realm_name}")

        try:
            # Use master realm admin client for cleanup
            admin_client = create_admin_client(keycloak_config.master_realm)
            admin_client.delete_realm(realm_name=realm_name)
            logger.info(f"Cleaned up realm: {realm_name}")
        except KeycloakGetError:
            logger.debug(f"Realm {realm_name} doesn't exist, no cleanup needed")
        except Exception as e:
            logger.warning(f"Failed to cleanup realm {realm_name}: {e}")

    except Exception as e:
        logger.error(f"Error during cleanup: {e}")


async def _create_admin_user(realm_name: str, request: KeycloakProvisionRequest) -> str:
    try:
        # Create realm-specific admin client
        admin_client = create_admin_client(realm_name)
        user_payload = get_user_payload(request)

        user = admin_client.create_user(payload=user_payload)
        admin_role = admin_client.get_realm_role(UserRole.TENANT_ADMIN.value)
        user_id = user["id"]
        admin_client.assign_realm_roles(user_id=user_id, roles=[admin_role])

        logger.debug(f"Assigned {UserRole.TENANT_ADMIN.value} role to user: {request.admin_username}")

        return user_id
    except Exception as e:
        raise ProcessingException(f"Failed to create admin user: {e}")


async def _create_realm_roles(realm_name: str) -> None:
    try:
        # Create realm-specific admin client
        admin_client = create_admin_client(realm_name)

        for role_name in keycloak_config.realm_roles:
            role_payload = {
                "name": role_name,
                "description": f"Role for {role_name.replace('_', ' ').title()}",
                "composite": False,
                "clientRole": False
            }

            try:
                admin_client.create_realm_role(payload=role_payload)
                logger.debug(f"Created realm role: {role_name}")
            except KeycloakPostError as e:
                if str(HTTP_409_CONFLICT) in str(e):
                    logger.debug(f"Realm role {role_name} already exists")
                else:
                    raise
    except Exception as e:
        raise ProcessingException(f"Failed to create realm roles: {e}")


async def _create_client(realm_name: str) -> Dict[str, str]:
    try:
        # Create realm-specific admin client
        admin_client = create_admin_client(realm_name)

        client_payload = {
            "clientId": keycloak_config.client_name,
            "name": "Securinest Customer App",
            "description": "Securinest Customer Application Client",
            "enabled": True,
            "clientAuthenticatorType": "client-secret",
            "redirectUris": [keycloak_config.redirect_uri],
            "webOrigins": [keycloak_config.backend_url],
            "protocol": "openid-connect",
            "publicClient": False,
            "bearerOnly": False,
            "consentRequired": False,
            "standardFlowEnabled": True,
            "implicitFlowEnabled": False,
            "directAccessGrantsEnabled": True,
            "serviceAccountsEnabled": True,
            "authorizationServicesEnabled": False,
            "fullScopeAllowed": True
        }

        client_id = admin_client.create_client(payload=client_payload)
        client_secret = admin_client.get_client_secrets(client_id)["value"]

        return {
            "client_id": keycloak_config.client_name,
            "client_secret": client_secret
        }

    except Exception as e:
        raise ProcessingException(f"Failed to create client: {e}")


async def provision_tenant(request: KeycloakProvisionRequest) -> KeycloakProvisionResponse:
    realm_name = get_realm_name(request.tenant_id, request.organization_name)

    try:
        logger.info(f"Starting Keycloak provisioning for tenant {request.tenant_id}")

        await _create_realm(realm_name)
        logger.info(f"Created realm: {realm_name}")

        client_data = await _create_client(realm_name)
        logger.info(f"Created client: {keycloak_config.client_name}")

        await _create_realm_roles(realm_name)
        logger.info(f"Created realm roles: {keycloak_config.realm_roles}")

        admin_user_id = None
        if request.admin_username and request.admin_email:
            admin_user_id = await _create_admin_user(realm_name, request)
            logger.info(f"Created admin user: {request.admin_username}")

        return KeycloakProvisionResponse(
            tenant_id=request.tenant_id,
            realm_name=realm_name,
            client_id=client_data["client_id"],
            client_secret=client_data["client_secret"],
            admin_user_id=admin_user_id,
            realm_roles=keycloak_config.realm_roles,
            redirect_uri=keycloak_config.redirect_uri,
            success=True,
            message=f"Successfully provisioned tenant {request.tenant_id}"
        )

    except Exception as e:
        logger.error(f"Failed to provision tenant {request.tenant_id}: {e}")
        await _cleanup_failed_provisioning(realm_name)

        return get_keycloak_failed_response(e, realm_name, request)


def get_filtered_info(realm_info):
    filtered_info = {
        "realm": realm_info.get("realm"),
        "displayName": realm_info.get("displayName"),
        "enabled": realm_info.get("enabled"),
        "registrationAllowed": realm_info.get("registrationAllowed"),
        "loginWithEmailAllowed": realm_info.get("loginWithEmailAllowed"),
        "bruteForceProtected": realm_info.get("bruteForceProtected"),
    }
    return filtered_info


def get_health_check():
    try:
        admin_client = create_admin_client()
        realms = admin_client.get_realms()

        return {
            "status": "healthy",
            "keycloak_url": admin_client.server_url,
            "realms_count": len(realms) if realms else 0,
            "message": "Keycloak connection is working"
        }

    except Exception as e:
        logger.error(f"Keycloak health check failed: {e}")

        raise ServiceUnavailableException(message=f"Keycloak is not available: {str(e)}")


class KeycloakService:
    def __init__(self, session: AsyncSession):
        self.session = session
        self._realm_creation_locks: Dict[str, asyncio.Lock] = {}
        self._locks_lock = asyncio.Lock()

        self._org_name_cache: Dict[str, Optional[str]] = {}
        self._cache_lock = asyncio.Lock()
        self._cache_ttl = 300

    async def _get_realm_lock(self, realm_name: str) -> asyncio.Lock:
        async with self._locks_lock:
            if realm_name not in self._realm_creation_locks:
                self._realm_creation_locks[realm_name] = asyncio.Lock()
            return self._realm_creation_locks[realm_name]

    def _create_admin_client(self, target_realm: Optional[str] = None) -> KeycloakAdmin:
        try:
            client = KeycloakAdmin(
                server_url=keycloak_config.keycloak_url,
                username=keycloak_config.admin_user,
                password=keycloak_config.admin_password,
                realm_name=keycloak_config.master_realm,
                verify=True
            )

            if target_realm:
                client.realm_name = target_realm

            return client

        except Exception as e:
            logger.error(f"Failed to connect to Keycloak: {type(e).__name__}")
            logger.debug(f"Keycloak connection details - URL: {keycloak_config.keycloak_url}, "
                        f"Master realm: {keycloak_config.master_realm}, "
                        f"Target realm: {target_realm or 'master'}")
            raise ProcessingException(f"Failed to connect to Keycloak: {type(e).__name__}")
    
    async def provision_tenant(self, request: KeycloakProvisionRequest) -> KeycloakProvisionResponse:
        if not request.tenant_id or not request.tenant_id.strip():
            raise ProcessingException("Tenant ID is required and cannot be empty")

        realm_name = get_realm_name(request.tenant_id, request.organization_name)

        realm_lock = await self._get_realm_lock(realm_name)

        async with realm_lock:
            try:
                logger.info(f"Starting Keycloak provisioning for tenant {request.tenant_id}")

                await self._create_realm_safe(realm_name)
                logger.info(f"Created realm: {realm_name}")

                await self._create_realm_roles_safe(realm_name)
                logger.info(f"Created realm roles: {keycloak_config.realm_roles}")

                client_data = await self._create_client_safe(realm_name)
                logger.info(f"Created client: {keycloak_config.client_name}")

                await self._store_tenant_keycloak_config(
                    tenant_id=request.tenant_id,
                    realm_name=realm_name,
                    client_data=client_data
                )

                admin_user_id = None
                if request.admin_username and request.admin_email:
                    admin_user_id = await self._create_admin_user_safe(realm_name, request)
                    logger.info(f"Created admin user: {request.admin_username}")

                return KeycloakProvisionResponse(
                    tenant_id=request.tenant_id,
                    realm_name=realm_name,
                    client_id=client_data["client_id"],
                    client_secret=client_data["client_secret"],
                    admin_user_id=admin_user_id,
                    realm_roles=keycloak_config.realm_roles,
                    redirect_uri=keycloak_config.redirect_uri,
                    success=True,
                    message=f"Successfully provisioned tenant {request.tenant_id}"
                )

            except ProcessingException:
                await self._cleanup_failed_provisioning_safe(realm_name)
                raise

            except Exception as e:
                logger.error(f"Unexpected error provisioning tenant {request.tenant_id}: {type(e).__name__}")
                await self._cleanup_failed_provisioning_safe(realm_name)

                return KeycloakProvisionResponse(
                    tenant_id=request.tenant_id,
                    realm_name=realm_name,
                    client_id="",
                    client_secret="",
                    admin_user_id=None,
                    realm_roles=[],
                    redirect_uri="",
                    success=False,
                    message=f"Failed to provision tenant: {type(e).__name__}"
                )
    
    async def _create_realm_safe(self, realm_name: str) -> str:
        try:
            admin_client = self._create_admin_client(realm_name)

            realm_payload: RealmPayload = {
                "realm": realm_name,
                "enabled": True,
                "displayName": f"Tenant Realm: {realm_name}",
                "registrationAllowed": False,
                "loginWithEmailAllowed": True,
                "duplicateEmailsAllowed": False,
                "resetPasswordAllowed": True,
                "editUsernameAllowed": False,
                "bruteForceProtected": True,
                "permanentLockout": False,
                "maxFailureWaitSeconds": 900,
                "minimumQuickLoginWaitSeconds": 60,
                "waitIncrementSeconds": 60,
                "quickLoginCheckMilliSeconds": 1000,
                "maxDeltaTimeSeconds": 43200,
                "failureFactor": 30
            }

            admin_client.create_realm(realm_payload)
            logger.debug(f"Successfully created realm: {realm_name}")
            return realm_name

        except KeycloakPostError as e:
            if "409" in str(e):
                logger.warning(f"Realm {realm_name} already exists, continuing...")
                return realm_name
            logger.error(f"Keycloak POST error creating realm {realm_name}: {type(e).__name__}")
            raise ProcessingException(f"Failed to create realm: Keycloak server error")

        except KeycloakError as e:
            logger.error(f"Keycloak error creating realm {realm_name}: {type(e).__name__}")
            raise ProcessingException(f"Failed to create realm: Keycloak authentication error")

        except Exception as e:
            logger.error(f"Unexpected error creating realm {realm_name}: {type(e).__name__}")
            raise ProcessingException(f"Failed to create realm: {type(e).__name__}")
    
    async def _create_client_safe(self, realm_name: str) -> ClientData:
        try:
            logger.debug(f"Creating client in realm: {realm_name}")
            admin_client = self._create_admin_client(realm_name)

            client_payload: ClientPayload = {
                "clientId": keycloak_config.client_name,
                "name": "Securinest Customer App",
                "description": "Securinest Customer Application Client",
                "enabled": True,
                "clientAuthenticatorType": "client-secret",
                "redirectUris": [keycloak_config.redirect_uri],
                "webOrigins": [keycloak_config.backend_url],
                "protocol": "openid-connect",
                "publicClient": False,
                "bearerOnly": False,
                "consentRequired": False,
                "standardFlowEnabled": True,
                "implicitFlowEnabled": False,
                "directAccessGrantsEnabled": True,
                "serviceAccountsEnabled": True,
                "authorizationServicesEnabled": False,
                "fullScopeAllowed": True
            }

            client_uuid = admin_client.create_client(client_payload)
            logger.debug(f"Created client with UUID: {client_uuid}")

            client_secret_data = admin_client.get_client_secrets(client_uuid)
            client_secret = client_secret_data["value"]

            logger.debug(f"Successfully created client: {keycloak_config.client_name}")

            return ClientData(
                client_id=keycloak_config.client_name,
                client_secret=client_secret
            )

        except KeycloakPostError as e:
            if "409" in str(e):
                logger.warning(f"Client {keycloak_config.client_name} already exists in realm {realm_name}")
                try:
                    existing_client = admin_client.get_client_id(keycloak_config.client_name)
                    client_secret_data = admin_client.get_client_secrets(existing_client)
                    return ClientData(
                        client_id=keycloak_config.client_name,
                        client_secret=client_secret_data["value"]
                    )
                except Exception:
                    raise ProcessingException("Client exists but cannot retrieve credentials")

            logger.error(f"Keycloak POST error creating client in realm {realm_name}: {type(e).__name__}")
            raise ProcessingException("Failed to create client: Keycloak server error")

        except KeycloakError as e:
            logger.error(f"Keycloak error creating client in realm {realm_name}: {type(e).__name__}")
            raise ProcessingException("Failed to create client: Keycloak authentication error")

        except Exception as e:
            logger.error(f"Unexpected error creating client in realm {realm_name}: {type(e).__name__}")
            raise ProcessingException(f"Failed to create client: {type(e).__name__}")

    async def _create_realm_roles_safe(self, realm_name: str) -> None:
        try:
            admin_client = self._create_admin_client(realm_name)

            created_roles = []
            for role_name in keycloak_config.realm_roles:
                try:
                    role_payload: RolePayload = {
                        "name": role_name,
                        "description": f"Role for {role_name.replace('_', ' ').title()}",
                        "composite": False,
                        "clientRole": False
                    }

                    admin_client.create_realm_role(role_payload)
                    created_roles.append(role_name)
                    logger.debug(f"Created realm role: {role_name}")

                except KeycloakPostError as e:
                    if "409" in str(e):
                        logger.debug(f"Realm role {role_name} already exists")
                        created_roles.append(role_name)
                    else:
                        logger.error(f"Failed to create role {role_name}: {type(e).__name__}")
                        raise ProcessingException(f"Failed to create role {role_name}")

                except KeycloakError as e:
                    logger.error(f"Keycloak error creating role {role_name}: {type(e).__name__}")
                    raise ProcessingException(f"Failed to create role {role_name}: Authentication error")

            logger.debug(f"Successfully processed {len(created_roles)} roles in realm {realm_name}")

        except ProcessingException:
            raise
        except Exception as e:
            logger.error(f"Unexpected error creating realm roles in {realm_name}: {type(e).__name__}")
            raise ProcessingException(f"Failed to create realm roles: {type(e).__name__}")

    async def _create_admin_user_safe(self, realm_name: str, request: KeycloakProvisionRequest) -> str:
        try:
            logger.debug(f"Creating admin user in realm: {realm_name}")

            if not request.admin_username or not request.admin_email:
                raise ProcessingException("Admin username and email are required")

            admin_client = self._create_admin_client(realm_name)

            user_payload: UserPayload = {
                "username": request.admin_username.strip(),
                "email": request.admin_email.strip(),
                "firstName": (request.admin_first_name or "").strip(),
                "lastName": (request.admin_last_name or "").strip(),
                "enabled": True,
                "emailVerified": True,
                "credentials": [{
                    "type": "password",
                    "value": request.admin_password,
                    "temporary": False
                }]
            }

            user_id = admin_client.create_user(user_payload)
            logger.debug(f"Created user with ID: {user_id}")

            await self._ensure_and_assign_admin_role(admin_client, user_id, request.admin_username)

            return user_id

        except KeycloakPostError as e:
            if "409" in str(e):
                logger.warning(f"User {request.admin_username} already exists in realm {realm_name}")
                try:
                    users = admin_client.get_users({"username": request.admin_username})
                    if users:
                        return users[0]["id"]
                except Exception:
                    pass
                raise ProcessingException(f"User {request.admin_username} already exists")

            logger.error(f"Keycloak POST error creating admin user: {type(e).__name__}")
            raise ProcessingException("Failed to create admin user: Keycloak server error")

        except KeycloakError as e:
            logger.error(f"Keycloak error creating admin user: {type(e).__name__}")
            raise ProcessingException("Failed to create admin user: Keycloak authentication error")

        except ProcessingException:
            raise

        except Exception as e:
            logger.error(f"Unexpected error creating admin user: {type(e).__name__}")
            raise ProcessingException(f"Failed to create admin user: {type(e).__name__}")

    async def _ensure_and_assign_admin_role(self, admin_client: KeycloakAdmin, user_id: str, username: str) -> None:
        try:
            from src.app.core.constant.constants import UserRole
            role_name = UserRole.TENANT_ADMIN.value

            try:
                role_payload: RolePayload = {
                    "name": role_name,
                    "description": "Tenant Administrator Role",
                    "composite": False,
                    "clientRole": False
                }
                admin_client.create_realm_role(payload=role_payload)
                logger.debug(f"Created admin role: {role_name}")
            except KeycloakPostError as e:
                if "409" not in str(e):
                    raise

            admin_role = admin_client.get_realm_role(role_name)
            admin_client.assign_realm_roles(user_id=user_id, roles=[admin_role])
            logger.debug(f"Assigned {role_name} role to user: {username}")

        except Exception as e:
            logger.error(f"Failed to assign admin role to user {username}: {type(e).__name__}")
            raise ProcessingException("Failed to assign admin role to user")

    async def _cleanup_failed_provisioning_safe(self, realm_name: str) -> None:
        try:
            logger.info(f"Attempting cleanup for failed provisioning of realm: {realm_name}")

            try:
                admin_client = self._create_admin_client(realm_name)
                admin_client.delete_realm(realm_name=realm_name)
                logger.info(f"Successfully cleaned up realm: {realm_name}")

            except KeycloakGetError:
                logger.debug(f"Realm {realm_name} doesn't exist, no cleanup needed")

            except KeycloakError as e:
                logger.warning(f"Keycloak error during cleanup of realm {realm_name}: {type(e).__name__}")

            except Exception as e:
                logger.warning(f"Unexpected error during cleanup of realm {realm_name}: {type(e).__name__}")

        except Exception as e:
            logger.error(f"Critical error during cleanup: {type(e).__name__}")   
            
    async def delete_tenant_realm(self, tenant_id: str, current_user: UserContext) -> bool:
        try:
            if not tenant_id or not tenant_id.strip():
                raise ProcessingException("Tenant ID is required")

            organization_name = await self._get_organization_name_safe(tenant_id)
            realm_name = get_realm_name(tenant_id, organization_name)

            logger.info(f"Deleting realm for tenant {tenant_id}: {realm_name}") 

            # Use master realm admin client for deletion
            admin_client = create_admin_client(keycloak_config.master_realm)
            admin_client.delete_realm(realm_name=realm_name)

            logger.info(f"Successfully deleted realm: {realm_name}")
            return True

        except KeycloakGetError:
            logger.warning(f"Realm {realm_name} doesn't exist")

            return True

        except KeycloakError as e:
            logger.error(f"Keycloak error deleting realm {realm_name}: {type(e).__name__}")
            return False

        except ProcessingException:
            raise

        except Exception as e:
            logger.error(f"Failed to delete realm {realm_name}: {e}")

            return False

    async def get_realm_info(self, tenant_id: str, current_user: UserContext) -> Optional[Dict[str, Any]]:
        if current_user.tenant_id != tenant_id:
            raise ForbiddenException(message="Access denied. You can only access your own tenant's realm information.")

        logger.info(f"Getting realm info for tenant: {tenant_id}")

        organization_name = await self._get_organization_name(tenant_id)
        realm_name = get_realm_name(tenant_id, organization_name)

        try:
            admin_client = create_admin_client(keycloak_config.master_realm)
            realm_info = admin_client.get_realm(realm_name=realm_name)

            return get_filtered_info(realm_info)
        except KeycloakGetError:
            logger.warning(f"Realm {realm_name} not found")

            raise NotFoundException(message=f"Realm not found for tenant {tenant_id}")
        except Exception as e:
            logger.error(f"Error getting realm info for tenant {tenant_id}: {e}")

            raise ProcessingException(message=f"Failed to get realm info: {str(e)}")

        except KeycloakError as e:
            logger.error(f"Keycloak error getting realm info for tenant {tenant_id}: {type(e).__name__}")
            return None

        except Exception as e:
            logger.error(f"Unexpected error getting realm info for tenant {tenant_id}: {type(e).__name__}")
            return None

    async def _get_organization_name_safe(self, tenant_id: str) -> Optional[str]:
        try:
            async with self._cache_lock:
                cached_name = self._org_name_cache.get(tenant_id)
                if cached_name is not None:
                    logger.debug(f"Retrieved organization name from cache for tenant {tenant_id}")
                    return cached_name

            from src.app.dao.entity.tenant import Tenant

            try:
                tenant_id_int = int(tenant_id)
            except ValueError:
                logger.warning(f"Invalid tenant ID format: {tenant_id}")
                return None

            stmt = select(Tenant.organization_name).where(Tenant.id == tenant_id_int)
            result = await self.session.execute(stmt)
            organization_name = result.scalar_one_or_none()

            async with self._cache_lock:
                self._org_name_cache[tenant_id] = organization_name

            if organization_name:
                logger.debug(f"Found organization name for tenant {tenant_id}: {organization_name}")
            else:
                logger.debug(f"No organization name found for tenant {tenant_id}")

            return organization_name

        except Exception as e:
            logger.warning(f"Could not get organization name for tenant {tenant_id}: {e}")

            return None

    async def validate_realm_exists(self, tenant_id: str) -> bool:
        try:
            realm_info = await self.get_realm_info(tenant_id)
            return realm_info is not None
        except Exception as e:
            logger.error(f"Error validating realm existence for tenant {tenant_id}: {type(e).__name__}")
            return False

    async def get_realm_users_count(self, tenant_id: str) -> int:
        try:
            organization_name = await self._get_organization_name_safe(tenant_id)
            realm_name = get_realm_name(tenant_id, organization_name)

            admin_client = self._create_admin_client(realm_name)
            users = admin_client.get_users()

            return len(users) if users else 0

        except Exception as e:
            logger.error(f"Error getting user count for tenant {tenant_id}: {type(e).__name__}")
            return -1

    async def get_realm_clients_info(self, tenant_id: str) -> List[Dict[str, Any]]:
        try:
            organization_name = await self._get_organization_name_safe(tenant_id)
            realm_name = get_realm_name(tenant_id, organization_name)

            admin_client = self._create_admin_client(realm_name)
            clients = admin_client.get_clients()

            return clients if clients else []

        except Exception as e:
            logger.error(f"Error getting clients info for tenant {tenant_id}: {type(e).__name__}")
            return []

    @asynccontextmanager
    async def get_admin_client_context(self, realm_name: Optional[str] = None):
        admin_client = None
        try:
            admin_client = self._create_admin_client(realm_name)
            yield admin_client
        except Exception as e:
            logger.error(f"Error in admin client context: {type(e).__name__}")
            raise
        finally:
            if admin_client:
                logger.debug("Admin client context closed")

    def get_realm_url(self, tenant_id: str, organization_name: Optional[str] = None) -> str:
        realm_name = get_realm_name(tenant_id, organization_name)
        return f"{keycloak_config.keycloak_url}/realms/{realm_name}"

    async def _store_tenant_keycloak_config(
        self,
        tenant_id: str,
        realm_name: str,
        client_data: ClientData
    ) -> None:
        try:
            client_secret_manager = get_client_secret_manager()
            client_secret = client_data["client_secret"]

            client_secret_hash = client_secret_manager.hash_client_secret(client_secret)

            await client_secret_manager.cache_client_secret(realm_name, client_secret)

            existing_query = select(TenantKeycloakConfig).where(
                TenantKeycloakConfig.tenant_id == int(tenant_id)
            )
            existing_config = (await self.session.execute(existing_query)).scalar_one_or_none()

            if existing_config:
                existing_config.realm_name = realm_name
                existing_config.client_id = client_data["client_id"]
                existing_config.client_secret_hash = client_secret_hash
                self.session.add(existing_config)
                logger.info(f"Updated Keycloak config for tenant {tenant_id}")
            else:
                keycloak_config_entity = TenantKeycloakConfig(
                    tenant_id=int(tenant_id),
                    realm_name=realm_name,
                    client_id=client_data["client_id"],
                    client_secret_hash=client_secret_hash
                )
                self.session.add(keycloak_config_entity)
                logger.info(f"Created Keycloak config for tenant {tenant_id}")

            await self.session.commit()

        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to store Keycloak config for tenant {tenant_id}: {e}")
            raise ProcessingException(f"Failed to store Keycloak configuration: {type(e).__name__}")

    async def _get_tenant_client_secret(self, realm_name: str) -> str:
        try:
            client_secret_manager = get_client_secret_manager()

            cached_secret = await client_secret_manager.get_cached_client_secret(realm_name)
            if cached_secret:
                logger.debug(f"Using cached client secret for realm {realm_name}")
                return cached_secret

            logger.info(f"Attempting to retrieve client secret from Keycloak for realm {realm_name}")
            retrieved_secret = await self._retrieve_client_secret_from_keycloak(realm_name)

            if retrieved_secret:
                await client_secret_manager.cache_client_secret(realm_name, retrieved_secret)
                return retrieved_secret

            logger.warning(f"Using global client secret as fallback for realm {realm_name}")
            return keycloak_config.client_secret

        except Exception as e:
            logger.error(f"Error getting client secret for realm {realm_name}: {e}")
            return keycloak_config.client_secret

    async def _retrieve_client_secret_from_keycloak(self, realm_name: str) -> Optional[str]:
        try:
            admin_client = self._create_admin_client(realm_name)

            client_uuid = admin_client.get_client_id(keycloak_config.client_name)
            if not client_uuid:
                logger.error(f"Client {keycloak_config.client_name} not found in realm {realm_name}")
                return None

            client_secrets = admin_client.get_client_secrets(client_uuid)
            client_secret = client_secrets.get('value')

            if client_secret:
                logger.info(f"Successfully retrieved client secret from Keycloak for realm {realm_name}")
                return client_secret
            else:
                logger.error(f"No client secret found for client in realm {realm_name}")
                return None

        except Exception as e:
            logger.error(f"Failed to retrieve client secret from Keycloak for realm {realm_name}: {e}")
            return None

    async def _create_openid_client(self, realm_name: str) -> KeycloakOpenID:
        try:
            client_secret = await self._get_tenant_client_secret(realm_name)

            return KeycloakOpenID(
                server_url=keycloak_config.keycloak_url,
                client_id=keycloak_config.client_name,
                realm_name=realm_name,
                client_secret_key=client_secret,
                verify=True
            )
        except Exception as e:
            logger.error(f"Failed to create OpenID client for realm {realm_name}: {e}")
            raise ProcessingException(f"Failed to create OpenID client: {type(e).__name__}")

    async def exchange_code_for_tokens(
        self,
        code: str,
        realm_name: str,
        redirect_uri: str
    ) -> TokenData:
        try:
            logger.info(f"Exchanging authorization code for tokens in realm {realm_name}")

            openid_client = await self._create_openid_client(realm_name)

            token_response = openid_client.token(
                grant_type='authorization_code',
                code=code,
                redirect_uri=redirect_uri
            )

            required_fields = ['access_token', 'refresh_token', 'expires_in', 'token_type']
            for field in required_fields:
                if field not in token_response:
                    raise ProcessingException(f"Missing required field in token response: {field}")

            token_data = TokenData(
                access_token=token_response['access_token'],
                refresh_token=token_response['refresh_token'],
                expires_in=int(token_response['expires_in']),
                token_type=token_response.get('token_type', 'Bearer')
            )

            logger.info(f"Successfully exchanged code for tokens in realm {realm_name}")
            return token_data

        except KeycloakError as e:
            logger.error(f"Keycloak error during token exchange: {e}")
            raise ProcessingException(f"Token exchange failed: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error during token exchange: {e}")
            raise ProcessingException(f"Token exchange failed: {type(e).__name__}")

    async def refresh_access_token(
        self,
        refresh_token: str,
        realm_name: str
    ) -> TokenRefreshData:
        try:
            logger.info(f"Refreshing access token in realm {realm_name}")

            openid_client = await self._create_openid_client(realm_name)

            token_response = openid_client.refresh_token(refresh_token)

            required_fields = ['access_token', 'expires_in']
            for field in required_fields:
                if field not in token_response:
                    raise ProcessingException(f"Missing required field in refresh response: {field}")

            refresh_data = TokenRefreshData(
                access_token=token_response['access_token'],
                expires_in=int(token_response['expires_in']),
                token_type=token_response.get('token_type', 'Bearer')
            )

            logger.info(f"Successfully refreshed access token in realm {realm_name}")
            return refresh_data

        except KeycloakError as e:
            logger.error(f"Keycloak error during token refresh: {e}")
            raise ProcessingException(f"Token refresh failed: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error during token refresh: {e}")
            raise ProcessingException(f"Token refresh failed: {type(e).__name__}")

    async def logout_user(
        self,
        refresh_token: str,
        realm_name: str
    ) -> bool:
        try:
            logger.info(f"Logging out user in realm {realm_name}")

            openid_client = await self._create_openid_client(realm_name)

            openid_client.logout(refresh_token)

            logger.info(f"Successfully logged out user in realm {realm_name}")
            return True

        except KeycloakError as e:
            logger.error(f"Keycloak error during logout: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during logout: {e}")
            return False

    def build_authorization_url(
        self,
        realm_name: str,
        redirect_uri: str,
        state: str,
        nonce: str
    ) -> str:
        try:
            base_url = f"{keycloak_config.keycloak_url}/realms/{realm_name}/protocol/openid-connect/auth"

            authorization_url = AuthUtils.build_authorization_url(
                base_url=base_url,
                client_id=keycloak_config.client_name,
                redirect_uri=redirect_uri,
                state=state,
                nonce=nonce
            )

            logger.debug(f"Built authorization URL for realm {realm_name}")
            return authorization_url

        except AuthUtilsError as e:
            logger.error(f"Auth utils error building authorization URL: {e}")
            raise ProcessingException(f"Failed to build authorization URL: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error building authorization URL: {e}")
            raise ProcessingException(f"Failed to build authorization URL: {type(e).__name__}")

    def build_logout_url(
        self,
        realm_name: str,
        redirect_uri: Optional[str] = None
    ) -> str:
        try:
            logout_url = f"{keycloak_config.keycloak_url}/realms/{realm_name}/protocol/openid-connect/logout"

            if redirect_uri:
                from urllib.parse import urlencode
                params = {'redirect_uri': redirect_uri}
                logout_url += f"?{urlencode(params)}"

            logger.debug(f"Built logout URL for realm {realm_name}")
            return logout_url

        except Exception as e:
            logger.error(f"Error building logout URL: {e}")
            raise ProcessingException(f"Failed to build logout URL: {type(e).__name__}")

    async def validate_token_and_extract_realm(self, token: str) -> str:
        try:
            from jose import jwt

            unverified_payload = jwt.get_unverified_claims(token)

            issuer = unverified_payload.get("iss", "")
            if not issuer:
                raise ProcessingException("Invalid token: missing issuer")

            realm_name = issuer.split("/realms/")[-1]
            if not realm_name or realm_name == issuer:
                raise ProcessingException("Invalid token: cannot extract realm")

            logger.debug(f"Extracted realm {realm_name} from token")
            return realm_name

        except Exception as e:
            logger.error(f"Error validating token and extracting realm: {e}")
            raise ProcessingException(f"Token validation failed: {type(e).__name__}")

    async def get_realm_name_by_tenant_id(self, tenant_id: str) -> str:
        try:
            organization_name = await self._get_organization_name_safe(tenant_id)
            realm_name = keycloak_config.get_realm_name(tenant_id, organization_name)

            logger.debug(f"Retrieved realm name {realm_name} for tenant {tenant_id}")
            return realm_name

        except Exception as e:
            logger.error(f"Error getting realm name for tenant {tenant_id}: {e}")
            raise ProcessingException(f"Failed to get realm name: {type(e).__name__}")

    def get_realm_admin_url(self, tenant_id: str, organization_name: Optional[str] = None) -> str:
        realm_name = get_realm_name(tenant_id, organization_name)
        return f"{keycloak_config.keycloak_url}/admin/master/console/#/{realm_name}"
