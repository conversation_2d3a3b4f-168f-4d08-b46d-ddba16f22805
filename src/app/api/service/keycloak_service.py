import logging
from typing import Optional, Dict, Any

from fastapi import Depends
from keycloak import KeycloakAdmin
from keycloak.exceptions import KeycloakGetError, KeycloakPostError
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.status import HTTP_409_CONFLICT

from src.app.api.schema.keycloak.keycloak_provision_request import KeycloakProvisionRequest
from src.app.api.schema.keycloak.keycloak_provision_response import KeycloakProvisionResponse
from src.app.core.config.keycloak_config import keycloak_config
from src.app.core.constant.constants import UserRole
from src.app.core.db_connection.db_connector import get_session
from src.app.core.exception.forbidden_exception import ForbiddenException
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.core.exception.processing_exception import ProcessingException
from src.app.core.exception.service_unavailable_exception import ServiceUnavailableException
from src.app.core.model.user_context import UserContext

logger = logging.getLogger(__name__)


async def get_keycloak_service(session: AsyncSession = Depends(get_session)) -> "KeycloakService":
    return KeycloakService(session)


def get_keycloak_failed_response(e, realm_name, request):
    return KeycloakProvisionResponse(
        tenant_id=request.tenant_id,
        realm_name=realm_name,
        client_id="",
        client_secret="",
        admin_user_id=None,
        realm_roles=[],
        redirect_uri="",
        success=False,
        message=f"Failed to provision tenant: {str(e)}"
    )


def get_real_payload(realm_name):
    return {
        "realm": realm_name,
        "enabled": True,
        "displayName": f"Tenant Realm: {realm_name}",
        "registrationAllowed": False,
        "loginWithEmailAllowed": True,
        "duplicateEmailsAllowed": False,
        "resetPasswordAllowed": True,
        "editUsernameAllowed": False,
        "bruteForceProtected": True,
        "permanentLockout": False,
        "maxFailureWaitSeconds": 900,
        "minimumQuickLoginWaitSeconds": 60,
        "waitIncrementSeconds": 60,
        "quickLoginCheckMilliSeconds": 1000,
        "maxDeltaTimeSeconds": 43200,
        "failureFactor": 30
    }


def create_admin_client(realm_name: str = None) -> KeycloakAdmin:
    """
    Create a new KeycloakAdmin client instance for each operation.

    This eliminates shared state and ensures thread safety.
    Each request gets its own admin client with the correct realm.
    """
    try:
        return KeycloakAdmin(
            server_url=keycloak_config.keycloak_url,
            username=keycloak_config.admin_user,
            password=keycloak_config.admin_password,
            realm_name=realm_name or keycloak_config.master_realm,
            verify=True
        )
    except Exception as e:
        logger.error(f"Failed to connect to Keycloak: {e}")

        raise ProcessingException(f"Failed to connect to Keycloak: {e}")


def get_user_payload(request):
    return {
        "username": request.admin_username,
        "email": request.admin_email,
        "firstName": request.admin_first_name or "",
        "lastName": request.admin_last_name or "",
        "enabled": True,
        "emailVerified": True,
        "credentials": [{
            "type": "password",
            "value": request.admin_password,
            "temporary": False
        }]
    }


async def _create_realm(realm_name: str) -> str:
    try:
        # Create master realm admin client for realm creation
        admin_client = create_admin_client(keycloak_config.master_realm)
        realm_payload = get_real_payload(realm_name)
        admin_client.create_realm(payload=realm_payload)

        return realm_name

    except KeycloakPostError as e:
        if str(HTTP_409_CONFLICT) in str(e):
            logger.warning(f"Realm {realm_name} already exists")

            return realm_name
        raise ProcessingException(f"Failed to create realm: {e}")
    except Exception as e:
        raise ProcessingException(f"Failed to create realm: {e}")


async def _cleanup_failed_provisioning(realm_name: str) -> None:
    try:
        logger.info(f"Attempting cleanup for failed provisioning of realm: {realm_name}")

        try:
            # Use master realm admin client for cleanup
            admin_client = create_admin_client(keycloak_config.master_realm)
            admin_client.delete_realm(realm_name=realm_name)
            logger.info(f"Cleaned up realm: {realm_name}")
        except KeycloakGetError:
            logger.debug(f"Realm {realm_name} doesn't exist, no cleanup needed")
        except Exception as e:
            logger.warning(f"Failed to cleanup realm {realm_name}: {e}")

    except Exception as e:
        logger.error(f"Error during cleanup: {e}")


async def _create_admin_user(realm_name: str, request: KeycloakProvisionRequest) -> str:
    try:
        # Create realm-specific admin client
        admin_client = create_admin_client(realm_name)
        user_payload = get_user_payload(request)

        user = admin_client.create_user(payload=user_payload)
        admin_role = admin_client.get_realm_role(UserRole.TENANT_ADMIN.value)
        user_id = user["id"]
        admin_client.assign_realm_roles(user_id=user_id, roles=[admin_role])

        logger.debug(f"Assigned {UserRole.TENANT_ADMIN.value} role to user: {request.admin_username}")

        return user_id
    except Exception as e:
        raise ProcessingException(f"Failed to create admin user: {e}")


async def _create_realm_roles(realm_name: str) -> None:
    try:
        # Create realm-specific admin client
        admin_client = create_admin_client(realm_name)

        for role_name in keycloak_config.realm_roles:
            role_payload = {
                "name": role_name,
                "description": f"Role for {role_name.replace('_', ' ').title()}",
                "composite": False,
                "clientRole": False
            }

            try:
                admin_client.create_realm_role(payload=role_payload)
                logger.debug(f"Created realm role: {role_name}")
            except KeycloakPostError as e:
                if str(HTTP_409_CONFLICT) in str(e):
                    logger.debug(f"Realm role {role_name} already exists")
                else:
                    raise
    except Exception as e:
        raise ProcessingException(f"Failed to create realm roles: {e}")


async def _create_client(realm_name: str) -> Dict[str, str]:
    try:
        # Create realm-specific admin client
        admin_client = create_admin_client(realm_name)

        client_payload = {
            "clientId": keycloak_config.client_name,
            "name": "Securinest Customer App",
            "description": "Securinest Customer Application Client",
            "enabled": True,
            "clientAuthenticatorType": "client-secret",
            "redirectUris": [keycloak_config.redirect_uri],
            "webOrigins": [keycloak_config.backend_url],
            "protocol": "openid-connect",
            "publicClient": False,
            "bearerOnly": False,
            "consentRequired": False,
            "standardFlowEnabled": True,
            "implicitFlowEnabled": False,
            "directAccessGrantsEnabled": True,
            "serviceAccountsEnabled": True,
            "authorizationServicesEnabled": False,
            "fullScopeAllowed": True
        }

        client_id = admin_client.create_client(payload=client_payload)
        client_secret = admin_client.get_client_secrets(client_id)["value"]

        return {
            "client_id": keycloak_config.client_name,
            "client_secret": client_secret
        }

    except Exception as e:
        raise ProcessingException(f"Failed to create client: {e}")


async def provision_tenant(request: KeycloakProvisionRequest) -> KeycloakProvisionResponse:
    realm_name = keycloak_config.get_realm_name(request.tenant_id, request.organization_name)

    try:
        logger.info(f"Starting Keycloak provisioning for tenant {request.tenant_id}")

        await _create_realm(realm_name)
        logger.info(f"Created realm: {realm_name}")

        client_data = await _create_client(realm_name)
        logger.info(f"Created client: {keycloak_config.client_name}")

        await _create_realm_roles(realm_name)
        logger.info(f"Created realm roles: {keycloak_config.realm_roles}")

        admin_user_id = None
        if request.admin_username and request.admin_email:
            admin_user_id = await _create_admin_user(realm_name, request)
            logger.info(f"Created admin user: {request.admin_username}")

        return KeycloakProvisionResponse(
            tenant_id=request.tenant_id,
            realm_name=realm_name,
            client_id=client_data["client_id"],
            client_secret=client_data["client_secret"],
            admin_user_id=admin_user_id,
            realm_roles=keycloak_config.realm_roles,
            redirect_uri=keycloak_config.redirect_uri,
            success=True,
            message=f"Successfully provisioned tenant {request.tenant_id}"
        )

    except Exception as e:
        logger.error(f"Failed to provision tenant {request.tenant_id}: {e}")
        await _cleanup_failed_provisioning(realm_name)

        return get_keycloak_failed_response(e, realm_name, request)


def get_filtered_info(realm_info):
    filtered_info = {
        "realm": realm_info.get("realm"),
        "displayName": realm_info.get("displayName"),
        "enabled": realm_info.get("enabled"),
        "registrationAllowed": realm_info.get("registrationAllowed"),
        "loginWithEmailAllowed": realm_info.get("loginWithEmailAllowed"),
        "bruteForceProtected": realm_info.get("bruteForceProtected"),
    }
    return filtered_info


def get_health_check():
    try:
        admin_client = create_admin_client()
        realms = admin_client.get_realms()

        return {
            "status": "healthy",
            "keycloak_url": admin_client.server_url,
            "realms_count": len(realms) if realms else 0,
            "message": "Keycloak connection is working"
        }

    except Exception as e:
        logger.error(f"Keycloak health check failed: {e}")

        raise ServiceUnavailableException(message=f"Keycloak is not available: {str(e)}")


class KeycloakService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def delete_tenant_realm(self, tenant_id: str, current_user: UserContext) -> bool:
        organization_name = await self._get_organization_name(tenant_id)
        realm_name = keycloak_config.get_realm_name(tenant_id, organization_name)

        try:
            logger.info(f"Deleting realm for tenant {tenant_id}: {realm_name}")

            # Use master realm admin client for deletion
            admin_client = create_admin_client(keycloak_config.master_realm)
            admin_client.delete_realm(realm_name=realm_name)

            logger.info(f"Successfully deleted realm: {realm_name}")
            return True

        except KeycloakGetError:
            logger.warning(f"Realm {realm_name} doesn't exist")

            return True
        except Exception as e:
            logger.error(f"Failed to delete realm {realm_name}: {e}")

            return False

    async def get_realm_info(self, tenant_id: str, current_user: UserContext) -> Optional[Dict[str, Any]]:
        if current_user.tenant_id != tenant_id:
            raise ForbiddenException(message="Access denied. You can only access your own tenant's realm information.")

        logger.info(f"Getting realm info for tenant: {tenant_id}")

        organization_name = await self._get_organization_name(tenant_id)
        realm_name = keycloak_config.get_realm_name(tenant_id, organization_name)

        try:
            # Use master realm admin client for realm info
            admin_client = create_admin_client(keycloak_config.master_realm)
            realm_info = admin_client.get_realm(realm_name=realm_name)

            return get_filtered_info(realm_info)
        except KeycloakGetError:
            logger.warning(f"Realm {realm_name} not found")

            raise NotFoundException(message=f"Realm not found for tenant {tenant_id}")
        except Exception as e:
            logger.error(f"Error getting realm info for tenant {tenant_id}: {e}")

            raise ProcessingException(message=f"Failed to get realm info: {str(e)}")

    async def _get_organization_name(self, tenant_id: str) -> Optional[str]:
        try:
            from src.app.dao.entity.tenant import Tenant
            from sqlalchemy import select

            stmt = select(Tenant.organization_name).where(Tenant.id == int(tenant_id))
            result = await self.session.execute(stmt)
            organization_name = result.scalar_one_or_none()

            return organization_name

        except Exception as e:
            logger.warning(f"Could not get organization name for tenant {tenant_id}: {e}")

            return None
