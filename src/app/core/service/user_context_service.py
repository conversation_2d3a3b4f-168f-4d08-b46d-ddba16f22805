import logging
from typing import Optional
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from src.app.core.security.keycloak_auth import keycloak_auth, KeycloakAuthError
from src.app.core.model.user_context import UserContext

logger = logging.getLogger(__name__)

security = HTTPBearer()


async def get_user_context_service() -> "UserContextService":
    return UserContextService()

async def get_current_user(
    service: "UserContextService" = Depends(get_user_context_service),
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> UserContext:
    return await service.get_current_user(credentials)


async def get_optional_user(
    service: "UserContextService" = Depends(get_user_context_service),
    request: Request = None
) -> Optional[UserContext]:
    return await service.get_optional_user(request)


class UserContextService:
    def __init__(self):
        pass
    
    async def get_current_user(
        self,
        credentials: HTTPAuthorizationCredentials = Depends(security)
    ) -> UserContext:
        try:
            token = credentials.credentials
            user_context = await keycloak_auth.extract_user_context(token)
            
            if not user_context.is_active:
                raise KeycloakAuthError("User account is inactive")
            
            return user_context
            
        except KeycloakAuthError:
            raise
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication failed"
            )
    
    async def get_optional_user(
        self,
        request: Request
    ) -> Optional[UserContext]:
        try:
            auth_header = request.headers.get("Authorization")
            if not auth_header or not auth_header.startswith("Bearer "):
                return None
            
            token = auth_header.replace("Bearer ", "")
            user_context = await keycloak_auth.extract_user_context(token)
            
            if not user_context.is_active:
                return None
            
            return user_context
            
        except Exception:
            return None
    
    async def validate_user_roles(
        self,
        user_context: UserContext,
        required_roles: list[str]
    ) -> bool:
        if not required_roles:
            return True
        
        user_roles = set(user_context.roles)
        required_roles_set = set(required_roles)
        
        return bool(user_roles.intersection(required_roles_set))
