import logging
from typing import Optional

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from src.app.api.schema.email.email_verification_request import EmailVerificationRequest
from src.app.api.schema.email.email_verification_response import EmailVerificationResponse
from src.app.api.schema.keycloak.keycloak_provision_request import KeycloakProvisionRequest
from src.app.api.service.keycloak_service import get_keycloak_service
from src.app.core.db_connection.db_connector import get_session
from src.app.core.util.email_utils import EmailUtils, get_email_utils
from src.app.core.exception.processing_exception import ProcessingException
from src.app.core.util.email_utils import EmailUtils
from src.app.dao.entity import Tenant, User
from src.app.dao.entity.tenant_status import TenantStatus

logger = logging.getLogger(__name__)


async def get_email_service(
    session: AsyncSession = Depends(get_session)
) -> "EmailService":
    return EmailService(session)

class EmailService:
    def __init__(self, session: AsyncSession):
        self.session = session
        # TODO: Configure actual email service (SMTP, SendGrid, etc.)
        # For now, we'll log emails for development
        self.base_url = "http://localhost:8000"  # TODO: Move to configuration
    
    """
    Send email verification email.
    
    Args:
        to_email: Recipient email address
        organization_name: Organization name for personalization
        verification_token: Verification token to include in link
        admin_first_name: Admin first name for personalization
        
    Returns:
        True if email sent successfully, False otherwise
    """
    async def send_verification_email(
        self,
        to_email: str,
        organization_name: str,
        verification_token: str,
        admin_first_name: Optional[str] = None
    ) -> bool:
        try:
            verification_link = f"{self.base_url}/tenants/verify-email?token={verification_token}"
            
            greeting = f"Hello {admin_first_name}," if admin_first_name else "Hello,"
            
            subject = f"Verify your email for {organization_name} - SecuriNest"
            
            body = f"""
{greeting}

Thank you for signing up for SecuriNest with {organization_name}!

To complete your registration and activate your account, please click the link below to verify your email address:

{verification_link}

This verification link will expire in 24 hours.

Once verified, your Keycloak authentication realm will be provisioned and you'll receive login credentials.

If you didn't create this account, please ignore this email.

Best regards,
The SecuriNest Team
            """.strip()
            
            # TODO: Replace with actual email sending
            # For development, log the email content
            logger.info(f"=== EMAIL VERIFICATION ===")
            logger.info(f"To: {to_email}")
            logger.info(f"Subject: {subject}")
            logger.info(f"Body:\n{body}")
            logger.info(f"Verification Link: {verification_link}")
            logger.info(f"=== END EMAIL ===")
            
            # TODO: Implement actual email sending here
            # Example with SMTP:
            # smtp_client.send_email(to_email, subject, body)
            
            # For now, always return True for development
            return True
            
        except Exception as e:
            logger.error(f"Failed to send verification email to {to_email}: {e}")
            return False
    
    async def send_welcome_email(
        self,
        to_email: str,
        organization_name: str,
        keycloak_realm_name: str,
        admin_first_name: Optional[str] = None
    ) -> bool:
        """
        Send welcome email after successful verification.
        
        Args:
            to_email: Recipient email address
            organization_name: Organization name
            keycloak_realm_name: Keycloak realm name for login
            admin_first_name: Admin first name for personalization
            
        Returns:
            True if email sent successfully, False otherwise
        """
        try:
            greeting = f"Hello {admin_first_name}," if admin_first_name else "Hello,"
            subject = f"Welcome to SecuriNest - {organization_name} is now active!"
            body = f"""
{greeting}

Congratulations! Your SecuriNest account for {organization_name} has been successfully activated.

Your Keycloak authentication realm has been provisioned:
- Realm: {keycloak_realm_name}
- Login URL: {self.base_url}/auth/realms/{keycloak_realm_name}

You can now:
1. Log in to your SecuriNest dashboard
2. Create additional users for your organization
3. Configure your AI security policies
4. Generate API keys for your applications

If you need help getting started, please check our documentation or contact support.

Welcome aboard!

Best regards,
The SecuriNest Team
            """.strip()
            
            # TODO: Replace with actual email sending
            logger.info(f"=== WELCOME EMAIL ===")
            logger.info(f"To: {to_email}")
            logger.info(f"Subject: {subject}")
            logger.info(f"Body:\n{body}")
            logger.info(f"=== END EMAIL ===")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send welcome email to {to_email}: {e}")

            return False

    async def verify_email(self, token: str) -> dict:
        verification_request = EmailVerificationRequest(token=token)
        logger.info(f"Starting email verification for token: {verification_request.token[:10]}...")

        try:
            email_utils = get_email_utils()

            token_payload = email_utils.decode_verification_token(verification_request.token)
            if not token_payload:
                raise ProcessingException("Invalid verification token format")

            user_query = select(User).where(User.email_verification_token == verification_request.token)
            user = (await self.session.execute(user_query)).scalar_one_or_none()

            if not user:
                raise ProcessingException("Invalid or expired verification token")

            if EmailUtils.is_token_expired(user.email_verification_expires):
                raise ProcessingException("Verification token has expired")

            original_password = token_payload.get("pwd")
            if not original_password:
                raise ProcessingException("Password not found in verification token")

            tenant = await self.session.get(Tenant, user.tenant_id)
            if not tenant:
                raise ProcessingException("Associated tenant not found")

            logger.info(f"Verifying email for user {user.id} and tenant {tenant.id}")

            user.email_verified = True
            user.is_active = True
            user.email_verification_token = None
            user.email_verification_expires = None

            tenant.status = TenantStatus.ACTIVE.value

            self.session.add(user)
            self.session.add(tenant)
            await self.session.commit()

            logger.info(f"Activated user {user.id} and tenant {tenant.id}")

            keycloak_provisioned = False
            keycloak_realm_name = None

            try:
                keycloak_request = KeycloakProvisionRequest(
                    tenant_id=str(tenant.id),
                    organization_name=tenant.organization_name,
                    admin_username=user.email,
                    admin_email=user.email,
                    admin_password=original_password,
                    admin_first_name=user.first_name,
                    admin_last_name=user.last_name
                )

                keycloak_service = await get_keycloak_service(self.session)
                keycloak_response = await keycloak_service.provision_tenant(keycloak_request)

                if keycloak_response.success:
                    keycloak_provisioned = True
                    keycloak_realm_name = keycloak_response.realm_name

                    logger.info(f"Successfully provisioned Keycloak for tenant {tenant.id}")

                    await self.send_welcome_email(
                        to_email=user.email,
                        organization_name=tenant.organization_name,
                        keycloak_realm_name=keycloak_realm_name,
                        admin_first_name=user.first_name
                    )
                else:
                    logger.error(f"Failed to provision Keycloak: {keycloak_response.message}")

            except Exception as e:
                logger.error(f"Error during Keycloak provisioning: {e}")
                # Continue without Keycloak - tenant is still activated

            # Step 8: Return response
            response = EmailVerificationResponse(
                tenant_id=tenant.id,
                organization_name=tenant.organization_name,
                status=tenant.status,
                email_verified=user.email_verified,
                keycloak_provisioned=keycloak_provisioned,
                keycloak_realm_name=keycloak_realm_name,
                login_session_token=None,
                message="Email verified successfully! Your account is now active."
            )

            logger.info(f"Completed email verification for tenant {tenant.id}")
            return response

        except ProcessingException:
            raise
        except Exception as e:
            logger.error(f"Failed to verify email: {e}")
            raise ProcessingException(f"Email verification failed: {str(e)}")
