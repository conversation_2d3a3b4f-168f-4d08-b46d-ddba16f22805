import os
from typing import AsyncGenerator, Optional

from dotenv import load_dotenv
from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    create_async_engine,
    async_sessionmaker,
)

from src.app.core.constant.constants import DatabaseConfig

load_dotenv()

DATABASE_URL = os.getenv(DatabaseConfig.DATABASE_URL.value)
if DATABASE_URL is None:
    raise RuntimeError(f"{DatabaseConfig.DATABASE_URL.value} not set. Please set it in your .env file.")

DB_POOL_SIZE = int(os.getenv(DatabaseConfig.DB_POOL_SIZE.value))
DB_MAX_OVERFLOW = int(os.getenv(DatabaseConfig.DB_MAX_OVERFLOW.value))
DB_POOL_TIMEOUT = int(os.getenv(DatabaseConfig.DB_POOL_TIMEOUT.value))
DB_ECHO = os.getenv(DatabaseConfig.DB_ECHO.value).lower() == "true"

engine: Optional[AsyncEngine] = None
AsyncSessionLocal: Optional[async_sessionmaker] = None

def get_engine() -> AsyncEngine:
    global engine
    if engine is None:
        engine = create_async_engine(
            DATABASE_URL,
            echo=DB_ECHO,
            future=True,
            pool_pre_ping=True,
            pool_recycle=300,
            pool_size=DB_POOL_SIZE,
            max_overflow=DB_MAX_OVERFLOW,
            pool_timeout=DB_POOL_TIMEOUT,
        )
    return engine

def get_session_maker() -> async_sessionmaker:
    global AsyncSessionLocal
    if AsyncSessionLocal is None:
        AsyncSessionLocal = async_sessionmaker(
            bind=get_engine(),
            expire_on_commit=False,
            class_=AsyncSession,
        )
    return AsyncSessionLocal

async def get_session() -> AsyncGenerator[AsyncSession, None]:
    session_maker = get_session_maker()
    async with session_maker() as session:
        yield session

async def close_engine():
    global engine
    if engine is not None:
        await engine.dispose()
        engine = None
