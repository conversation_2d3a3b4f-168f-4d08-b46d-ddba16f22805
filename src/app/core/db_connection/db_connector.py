from typing import AsyncGenerator, Optional

from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    create_async_engine,
    async_sessionmaker,
)

from src.app.core.config.database_config import get_database_config

engine: Optional[AsyncEngine] = None
AsyncSessionLocal: Optional[async_sessionmaker] = None

def get_engine() -> AsyncEngine:
    global engine
    if engine is None:
        db_config = get_database_config()
        connection_params = db_config.get_connection_params()
        engine = create_async_engine(**connection_params)
    return engine

def get_session_maker() -> async_sessionmaker:
    global AsyncSessionLocal
    if AsyncSessionLocal is None:
        AsyncSessionLocal = async_sessionmaker(
            bind=get_engine(),
            expire_on_commit=False,
            class_=AsyncSession,
        )
    return AsyncSessionLocal

async def get_session() -> AsyncGenerator[AsyncSession, None]:
    session_maker = get_session_maker()
    async with session_maker() as session:
        yield session

async def close_engine():
    global engine
    if engine is not None:
        await engine.dispose()
        engine = None
