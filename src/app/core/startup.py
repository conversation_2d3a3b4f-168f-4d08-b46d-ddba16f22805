"""
Application startup utilities.

Optional integration for database seeding during application startup.
Useful for development environments.
"""

import os
import logging
from typing import Optional

from src.app.core.database.seeder import seed_database
from src.app.core.constant.constants import DatabaseConfig

logger = logging.getLogger(__name__)


async def startup_tasks() -> None:
    auto_seed = os.getenv(DatabaseConfig.AUTO_SEED_DATABASE.value).lower() == "true"
    
    if auto_seed:
        logger.info("🌱 Auto-seeding enabled, seeding database...")
        try:
            await seed_database()
            logger.info("✅ Database auto-seeding completed")
        except Exception as e:
            logger.error(f"❌ Database auto-seeding failed: {e}")
    else:
        logger.info("ℹ️  Auto-seeding disabled (set AUTO_SEED_DATABASE=true to enable)")


def configure_startup_seeding() -> Optional[callable]:
    auto_seed = os.getenv(DatabaseConfig.AUTO_SEED_DATABASE.value).lower() == "true"
    
    if auto_seed:
        return startup_tasks
    return None
