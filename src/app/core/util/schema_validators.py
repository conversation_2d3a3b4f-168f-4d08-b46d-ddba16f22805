from datetime import datetime, timezone

def strip_and_validate_name(name: str) -> str:
    if not isinstance(name, str):
        raise ValueError("Name must be a string")
    val = name.strip()
    if not val:
        raise ValueError("Name must not be blank")
    return val


def validate_positive_int(v: int) -> int:
    if not isinstance(v, int) or v <= 0:
        raise ValueError("Must be a positive integer")
    return v


def ensure_utc(dt: datetime) -> datetime:
    if dt.tzinfo is None:
        return dt.replace(tzinfo=timezone.utc)
    return dt.astimezone(timezone.utc)

