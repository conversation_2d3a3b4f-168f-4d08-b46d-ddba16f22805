import hashlib
import hmac
import logging
import os
import secrets
import uuid
from datetime import datetime, timezone, timedelta
from typing import <PERSON>ple

from dotenv import load_dotenv

from src.app.core.constant.constants import EmailConfig

load_dotenv()

logger = logging.getLogger(__name__)


class EmailUtilsError(Exception): pass


class EmailUtils:
    @staticmethod
    def _get_verification_secret() -> str | None:
        secret = os.getenv(EmailConfig.EMAIL_VERIFICATION_SECRET.value)
        if not secret:
            logger.warning("Email verification secret is not set")

            return None

        return secret
    
    @staticmethod
    def _get_session_secret() -> str | None:
        secret = os.getenv(EmailConfig.EMAIL_SESSION_SECRET.value)

        if not secret:
            logger.warning("Email session secret is not set")

            return None

        return secret
    
    @staticmethod
    def _get_token_expiry_hours() -> int | None:
        try:
            hours = int(os.getenv(EmailConfig.EMAIL_TOKEN_EXPIRY_HOURS.value))

            return hours
        except ValueError:
            logger.warning("Email token expiry hours is not set")

            return None
    
    @staticmethod
    def generate_verification_token() -> Tuple[str, datetime]:
        try:
            token_uuid = str(uuid.uuid4())
            secret = EmailUtils._get_verification_secret()

            hmac_signature = (
                hmac.new(
                    secret.encode('utf-8'),
                    token_uuid.encode('utf-8'),
                    hashlib.sha256
                )
                .hexdigest()
            )
            
            token = f"{token_uuid}.{hmac_signature}"
            expiry_hours = EmailUtils._get_token_expiry_hours()
            expiration = datetime.now(timezone.utc) + timedelta(hours=expiry_hours)
            
            logger.debug(f"Generated email verification token with {expiry_hours}h expiry")

            return token, expiration
        except Exception as e:
            logger.error(f"Failed to generate verification token: {e}")

            raise EmailUtilsError(f"Token generation failed: {e}")
    
    @staticmethod
    def verify_verification_token(token: str) -> bool:
        try:
            parts = token.split('.')
            if len(parts) != 2:
                logger.debug("Invalid token format: missing parts")

                return False
            
            token_uuid, provided_signature = parts
            
            try:
                uuid.UUID(token_uuid)
            except ValueError:
                logger.debug("Invalid token format: malformed UUID")

                return False
            
            secret = EmailUtils._get_verification_secret()

            expected_signature = (
                hmac.new(
                    secret.encode('utf-8'),
                    token_uuid.encode('utf-8'),
                    hashlib.sha256
                )
                .hexdigest()
            )
            
            is_valid = hmac.compare_digest(expected_signature, provided_signature)
            
            if not is_valid:
                logger.debug("Token verification failed: HMAC mismatch")
            
            return is_valid
            
        except Exception as e:
            logger.error(f"Error verifying verification token: {e}")

            return False
    
    @staticmethod
    def generate_session_token() -> str:
        try:
            token = secrets.token_urlsafe(32)
            logger.debug("Generated session token")

            return token
            
        except Exception as e:
            logger.error(f"Failed to generate session token: {e}")

            raise EmailUtilsError(f"Session token generation failed: {e}")
    
    @staticmethod
    def is_token_expired(expiration: datetime) -> bool:
        try:
            current_time = datetime.now(timezone.utc)
            
            if expiration.tzinfo is None:
                logger.warning("Expiration datetime is not timezone-aware, assuming UTC")
                expiration = expiration.replace(tzinfo=timezone.utc)
            
            is_expired = current_time > expiration
            
            if is_expired:
                logger.debug(f"Token expired at {expiration}, current time: {current_time}")
            
            return is_expired
            
        except Exception as e:
            logger.error(f"Error checking token expiration: {e}")

            return True
    
    @staticmethod
    def generate_secure_token_with_expiry() -> Tuple[str, datetime]:
        try:
            token = EmailUtils.generate_session_token()
            
            expiry_hours = EmailUtils._get_token_expiry_hours()
            expiration = datetime.now(timezone.utc) + timedelta(hours=expiry_hours)
            
            logger.debug(f"Generated token with {expiry_hours}h expiry")

            return token, expiration
        except Exception as e:
            logger.error(f"Failed to generate token: {e}")

            raise EmailUtilsError(f"Token generation failed: {e}")
