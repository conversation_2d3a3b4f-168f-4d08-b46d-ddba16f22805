import hashlib
import hmac
import uuid
import logging
from datetime import datetime, timezone, timedelta
from typing import Tuple, Optional
from datetime import datetime, timezone, timedelta
from typing import Tuple

from dotenv import load_dotenv

from src.app.core.constant.constants import EmailConfig
from src.app.core.config.base_environment_config import BaseEnvironmenConfig, BaseConfigError

load_dotenv()

logger = logging.getLogger(__name__)


class EmailUtilsError(BaseConfigError):
    pass


class EmailUtils(BaseEnvironmenConfig):
    def __init__(self):
        self.verification_secret = self._get_required_env(EmailConfig.EMAIL_VERIFICATION_SECRET.value)
        self.session_secret = self._get_required_env(EmailConfig.EMAIL_SESSION_SECRET.value)
        self.token_expiry_hours = self._get_int_env(EmailConfig.EMAIL_TOKEN_EXPIRY_HOURS.value)

        self._validate_config()
        self._log_config_loaded("EmailUtils")

    def _validate_config(self) -> None:
        self._validate_secret_strength(self.verification_secret, EmailConfig.EMAIL_VERIFICATION_SECRET.value)
        self._validate_secret_strength(self.session_secret, EmailConfig.EMAIL_SESSION_SECRET.value)

        self._validate_range(self.token_expiry_hours, EmailConfig.EMAIL_TOKEN_EXPIRY_HOURS.value, min_value=1, max_value=168)
    
    def generate_verification_token(self, password: Optional[str] = None) -> Tuple[str, datetime]:
        try:
            import json
            import base64

            token_uuid = str(uuid.uuid4())
            expiration = datetime.now(timezone.utc) + timedelta(hours=self.token_expiry_hours)

            payload = {
                "uuid": token_uuid,
                "exp": expiration.isoformat()
            }

            if password:
                payload["pwd"] = password

            payload_json = json.dumps(payload)
            payload_b64 = base64.urlsafe_b64encode(payload_json.encode('utf-8')).decode('utf-8')

            hmac_signature = hmac.new(
                self.verification_secret.encode('utf-8'),
                payload_b64.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            token = f"{payload_b64}.{hmac_signature}"

            logger.debug(f"Generated email verification token with {self.token_expiry_hours}h expiry")
            return token, expiration

        except Exception as e:
            logger.error(f"Failed to generate verification token: {e}")

            raise EmailUtilsError(f"Token generation failed: {e}")
    
    def verify_verification_token(self, token: str) -> bool:
        try:
            payload = self.decode_verification_token(token)
            return payload is not None
        except Exception as e:
            logger.error(f"Error verifying verification token: {e}")

            return False

    def decode_verification_token(self, token: str) -> Optional[dict]:
        try:
            import json
            import base64

            parts = token.split('.')
            if len(parts) != 2:
                logger.debug("Invalid token format: missing parts")
                return None

            payload_b64, provided_signature = parts

            expected_signature = hmac.new(
                self.verification_secret.encode('utf-8'),
                payload_b64.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            if not hmac.compare_digest(expected_signature, provided_signature):
                logger.debug("Token verification failed: HMAC mismatch")
                return None

            try:
                payload_json = base64.urlsafe_b64decode(payload_b64.encode('utf-8')).decode('utf-8')
                payload = json.loads(payload_json)
            except Exception as e:
                logger.debug(f"Failed to decode token payload: {e}")
                return None

            try:
                uuid.UUID(payload.get("uuid", ""))
            except ValueError:
                logger.debug("Invalid token format: malformed UUID")
                return None

            try:
                exp_str = payload.get("exp")
                if exp_str:
                    exp_time = datetime.fromisoformat(exp_str.replace('Z', '+00:00'))
                    if datetime.now(timezone.utc) > exp_time:
                        logger.debug("Token has expired")
                        return None
            except Exception as e:
                logger.debug(f"Failed to parse expiration: {e}")
                return None

            return payload

        except Exception as e:
            logger.error(f"Error decoding verification token: {e}")
            return None
    


    @staticmethod
    def is_token_expired(expiration: datetime) -> bool:
        try:
            current_time = datetime.now(timezone.utc)

            if expiration.tzinfo is None:
                logger.warning("Expiration datetime is not timezone-aware, assuming UTC")
                expiration = expiration.replace(tzinfo=timezone.utc)

            is_expired = current_time > expiration

            if is_expired:
                logger.debug(f"Token expired at {expiration}, current time: {current_time}")

            return is_expired

        except Exception as e:
            logger.error(f"Error checking token expiration: {e}")

            return True

    def generate_secure_token_with_expiry(self) -> Tuple[str, datetime]:
        try:
            import secrets
            token = secrets.token_urlsafe(32)

            expiration = datetime.now(timezone.utc) + timedelta(hours=self.token_expiry_hours)

            logger.debug(f"Generated token with {self.token_expiry_hours}h expiry")
            return token, expiration

        except Exception as e:
            logger.error(f"Failed to generate token: {e}")

            raise EmailUtilsError(f"Token generation failed: {e}")


email_utils = EmailUtils()


def get_email_utils() -> EmailUtils:
    return email_utils
