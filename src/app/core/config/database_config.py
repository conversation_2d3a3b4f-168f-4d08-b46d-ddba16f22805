import logging
from dotenv import load_dotenv

from src.app.core.constant.constants import DatabaseConfig as DatabaseConstants
from src.app.core.config.base_environment_config import BaseEnvironmenConfig, BaseConfigError

load_dotenv()

logger = logging.getLogger(__name__)


class DatabaseConfigError(BaseConfigError):
    pass


class DatabaseConfig(BaseEnvironmenConfig):
    def __init__(self):
        self.database_url = self._get_required_env(DatabaseConstants.DATABASE_URL.value)
        
        self.auto_seed_database = self._get_bool_env(
            DatabaseConstants.AUTO_SEED_DATABASE.value)
        self.pool_size = self._get_int_env(DatabaseConstants.DB_POOL_SIZE.value)
        self.max_overflow = self._get_int_env(DatabaseConstants.DB_MAX_OVERFLOW.value)
        self.pool_timeout = self._get_int_env(DatabaseConstants.DB_POOL_TIMEOUT.value)
        self.echo = self._get_bool_env(DatabaseConstants.DB_ECHO.value)
        
        self._validate_config()
        self._log_config_loaded("DatabaseConfig")
    
    def _validate_config(self) -> None:
        if not self.database_url.startswith(('postgresql://', 'postgresql+asyncpg://')):
            raise DatabaseConfigError(
                f"{DatabaseConstants.DATABASE_URL.value} must be a valid PostgreSQL URL"
            )
        
        self._validate_range(self.pool_size, DatabaseConstants.DB_POOL_SIZE.value, min_value=1, max_value=100)
        self._validate_range(self.max_overflow, DatabaseConstants.DB_MAX_OVERFLOW.value, min_value=0, max_value=200)
        self._validate_range(self.pool_timeout, DatabaseConstants.DB_POOL_TIMEOUT.value, min_value=1, max_value=300)
        
        if self._is_production_environment():
            self._validate_production_config()
    
    def _validate_production_config(self) -> None:
        super()._validate_production_config()
        
        development_indicators = [
            ("localhost", "DATABASE_URL"),
            ("127.0.0.1", "DATABASE_URL"),
            ("test", "DATABASE_URL"),
            ("dev", "DATABASE_URL"),
        ]
        
        self._check_development_indicators([self.database_url], development_indicators)
        
        if self.echo:
            logger.warning("DB_ECHO should be False in production for performance")
        
        if self.pool_size < 5:
            logger.warning("DB_POOL_SIZE should be at least 5 in production")
    
    def get_connection_params(self) -> dict:
        return {
            "url": self.database_url,
            "echo": self.echo,
            "future": True,
            "pool_pre_ping": True,
            "pool_recycle": 300,
            "pool_size": self.pool_size,
            "max_overflow": self.max_overflow,
            "pool_timeout": self.pool_timeout,
        }
    
    def get_config_summary(self) -> dict:
        base_summary = super().get_config_summary()
        return {
            **base_summary,
            "auto_seed_database": self.auto_seed_database,
            "pool_size": self.pool_size,
            "max_overflow": self.max_overflow,
            "pool_timeout": self.pool_timeout,
            "echo": self.echo,
            "database_url_masked": self._mask_database_url()
        }
    
    def _mask_database_url(self) -> str:
        try:
            from urllib.parse import urlparse
            parsed = urlparse(self.database_url)
            
            if parsed.password:
                masked_url = self.database_url.replace(parsed.password, "***")
            else:
                masked_url = self.database_url
            
            return masked_url
        except Exception:
            return "postgresql://***:***@***:****/***"


database_config = DatabaseConfig()


def get_database_config() -> DatabaseConfig:
    return database_config
