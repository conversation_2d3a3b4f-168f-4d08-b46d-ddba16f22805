import logging
import os
import re
from typing import Optional

from dotenv import load_dotenv

from src.app.core.constant.constants import User<PERSON><PERSON>, KeycloakConfig as KeycloakConstants, Environment, environment
from src.app.core.config.base_environment_config import BaseEnvironmenConfig, BaseConfigError

load_dotenv()

logger = logging.getLogger(__name__)


class KeycloakConfigError(BaseConfigError):
    pass


def _sanitize_realm_name(name: str) -> str:
    sanitized = name.lower()
    sanitized = re.sub(r'[^a-z0-9-]', '-', sanitized)
    sanitized = re.sub(r'-+', '-', sanitized)
    sanitized = sanitized.strip('-')

    if len(sanitized) > 20:
        sanitized = sanitized[:20].rstrip('-')

    return sanitized


def _get_required_env(key: str) -> str:
    value = os.getenv(key)

    if not value:
        raise KeycloakConfigError(f"{key} environment variable is required but not set")

    return value.strip()


def get_realm_name(tenant_id: str, organization_name: Optional[str] = None) -> str:
    if organization_name:
        sanitized_org = _sanitize_realm_name(organization_name)

        return f"{sanitized_org}-tenant-{tenant_id}"

    return f"tenant-{tenant_id}"


def _is_production_environment() -> bool:
    env = os.getenv(environment, Environment.DEVELOPMENT.value).lower()
    return env in Environment.PRODUCTION.value


class KeycloakConfig(BaseEnvironmenConfig):
    def __init__(self):
        self.keycloak_url = _get_required_env(KeycloakConstants.KEYCLOAK_URL.value)
        self.admin_user = _get_required_env(KeycloakConstants.KEYCLOAK_ADMIN_USER.value)
        self.admin_password = _get_required_env(KeycloakConstants.KEYCLOAK_ADMIN_PASSWORD.value)
        self.backend_url = _get_required_env(KeycloakConstants.BACKEND_URL.value)
        self.client_secret = _get_required_env(KeycloakConstants.KEYCLOAK_CLIENT_SECRET.value)

        self.master_realm = self._get_optional_env(KeycloakConstants.KEYCLOAK_MASTER_REALM.value)

        self.client_name = KeycloakConstants.KEYCLOAK_CLIENT_NAME.value
        self.realm_roles = [
            UserRole.TENANT_ADMIN.value,
            UserRole.DEVELOPER.value,
            UserRole.AUDITOR.value,
            UserRole.POLICY_EDITOR.value
        ]

        self._validate_config()
        self._log_config_loaded("KeycloakConfig")

        logger.info("Keycloak configuration loaded successfully")

    def _validate_config(self) -> None:
        self._validate_url(self.keycloak_url, KeycloakConstants.KEYCLOAK_URL.value)
        self._validate_url(self.backend_url, KeycloakConstants.BACKEND_URL.value)

        required_fields = {
            KeycloakConstants.KEYCLOAK_ADMIN_USER.value: self.admin_user,
            KeycloakConstants.KEYCLOAK_ADMIN_PASSWORD.value: self.admin_password,
            KeycloakConstants.KEYCLOAK_CLIENT_SECRET.value: self.client_secret,
            KeycloakConstants.KEYCLOAK_MASTER_REALM.value: self.master_realm
        }
        self._validate_non_empty_fields(required_fields)

        if _is_production_environment():
            self._validate_production_config()

    def _validate_production_config(self) -> None:
        super()._validate_production_config()

        development_indicators = [
            ("localhost", "KEYCLOAK_URL or BACKEND_URL"),
            ("admin", "KEYCLOAK_ADMIN_USER"),
            ("admin@1234", "KEYCLOAK_ADMIN_PASSWORD"),
        ]

        config_values = [
            self.keycloak_url,
            self.backend_url,
            self.admin_user,
            self.admin_password
        ]

        self._check_development_indicators(config_values, development_indicators)

    @property
    def redirect_uri(self) -> str:
        return f"{self.backend_url}/auth/callback"

    @property
    def get_jwk_set_uri(self) -> str:
        return f"{self.keycloak_url}/realms/{self.master_realm}/protocol/openid-connect/certs"

    def get_realm_jwk_set_uri(self, realm_name: str) -> str:
        return f"{self.keycloak_url}/realms/{realm_name}/protocol/openid-connect/certs"

    def get_realm_issuer(self, realm_name: str) -> str:
        return f"{self.keycloak_url}/realms/{realm_name}"

    def is_production_ready(self) -> bool:
        try:
            self._validate_config()
            return True
        except KeycloakConfigError:
            return False


keycloak_config = KeycloakConfig()
