import logging
import os
import sys
from typing import Dict, Optional
from pathlib import Path
from dotenv import load_dotenv

from src.app.core.constant.constants import LogLevel, LogType, LogConfig as LogConfigConstants

load_dotenv()

logger = logging.getLogger(__name__)


class LogConfigError(Exception):
    pass


class LogConfig:
    def __init__(self):
        self.log_level = self._get_log_level()
        self.log_format = os.getenv(LogConfigConstants.LOG_FORMAT.value)
        self.enable_file_logging = self._get_bool_env(LogConfigConstants.ENABLE_FILE_LOGGING.value)
        self.log_file_path = self._get_log_file_path()
        self.max_log_file_size = int(os.getenv(LogConfigConstants.MAX_LOG_FILE_SIZE_MB.value)) * 1024 * 1024
        self.log_file_backup_count = int(os.getenv(LogConfigConstants.LOG_FILE_BACKUP_COUNT.value))
        self.component_log_levels = self._get_component_log_levels()
        self._validate_config()

    def _get_log_level(self) -> int:
        level_str = os.getenv(LogConfigConstants.LOG_LEVEL.value).upper()
        level_mapping = {
            LogLevel.DEBUG.value: logging.DEBUG,
            LogLevel.INFO.value: logging.INFO,
            LogLevel.WARNING.value: logging.WARNING,
            LogLevel.ERROR.value: logging.ERROR,
            LogLevel.CRITICAL.value: logging.CRITICAL
        }
        if level_str not in level_mapping:
            raise LogConfigError(f"Invalid LOG_LEVEL: {level_str}")
        return level_mapping[level_str]

    def _get_bool_env(self, key: str) -> bool:
        value = os.getenv(key, "false").lower()
        return value in ("true", "1", "yes", "on")

    def _get_log_file_path(self) -> Optional[Path]:
        if not self.enable_file_logging:
            return None
        log_file = os.getenv(LogConfigConstants.LOG_FILE_PATH.value)
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        return log_path

    def _get_component_log_levels(self) -> Dict[str, int]:
        return {
            "sqlalchemy.engine": self._get_component_level(LogType.SQLALCHEMY_LOG_LEVEL.value),
            "sqlalchemy.pool": self._get_component_level(LogType.SQLALCHEMY_POOL_LOG_LEVEL.value),
            "sqlalchemy.dialects": self._get_component_level(LogType.SQLALCHEMY_DIALECTS_LOG_LEVEL.value),
            "uvicorn.access": self._get_component_level(LogType.UVICORN_ACCESS_LOG_LEVEL.value),
            "uvicorn.error": self._get_component_level(LogType.UVICORN_ERROR_LOG_LEVEL.value),
            "httpx": self._get_component_level(LogType.HTTPX_LOG_LEVEL.value),
            "httpcore": self._get_component_level(LogType.HTTPCORE_LOG_LEVEL.value),
            "src.app.api.service": self._get_component_level(LogType.APP_SERVICE_LOG_LEVEL.value),
            "src.app.core.service": self._get_component_level(LogType.CORE_SERVICE_LOG_LEVEL.value),
            "src.app.core.security": self._get_component_level(LogType.SECURITY_LOG_LEVEL.value),
            "src.app.core.database": self._get_component_level(LogType.DATABASE_LOG_LEVEL.value),
        }

    def _get_component_level(self, env_key: str) -> int:
        level_str = os.getenv(env_key).upper()
        level_mapping = {
            LogLevel.DEBUG.value: logging.DEBUG,
            LogLevel.INFO.value: logging.INFO,
            LogLevel.WARNING.value: logging.WARNING,
            LogLevel.ERROR.value: logging.ERROR,
            LogLevel.CRITICAL.value: logging.CRITICAL
        }
        return level_mapping.get(level_str, logging.INFO)

    def _validate_config(self) -> None:
        if self.enable_file_logging and not self.log_file_path:
            raise LogConfigError("File logging enabled but no log file path specified")
        if self.max_log_file_size <= 0:
            raise LogConfigError("Max log file size must be positive")
        if self.log_file_backup_count < 0:
            raise LogConfigError("Log file backup count must be non-negative")

    def configure_logging(self) -> None:
        try:
            root_logger = logging.getLogger()
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)

            root_logger.setLevel(self.log_level)
            formatter = logging.Formatter(self.log_format)

            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(self.log_level)
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)

            if self.enable_file_logging and self.log_file_path:
                from logging.handlers import RotatingFileHandler
                file_handler = RotatingFileHandler(
                    filename=self.log_file_path,
                    maxBytes=self.max_log_file_size,
                    backupCount=self.log_file_backup_count,
                    encoding='utf-8'
                )
                file_handler.setLevel(self.log_level)
                file_handler.setFormatter(formatter)
                root_logger.addHandler(file_handler)
                logger.info(f"File logging enabled: {self.log_file_path}")

            for logger_name, level in self.component_log_levels.items():
                component_logger = logging.getLogger(logger_name)
                component_logger.setLevel(level)

            logger.info(f"Logging configured - Level: {logging.getLevelName(self.log_level)}")
            logger.info(f"Console logging: Enabled")
            logger.info(f"File logging: {'Enabled' if self.enable_file_logging else 'Disabled'}")

            if self.log_level <= logging.DEBUG:
                logger.debug("Component log levels:")
                for component, level in self.component_log_levels.items():
                    logger.debug(f"  {component}: {logging.getLevelName(level)}")

        except Exception as e:
            raise LogConfigError(f"Failed to configure logging: {e}")

log_config = LogConfig()


def configure_application_logging() -> None:
    log_config.configure_logging()


def get_log_config() -> LogConfig:
    return log_config
