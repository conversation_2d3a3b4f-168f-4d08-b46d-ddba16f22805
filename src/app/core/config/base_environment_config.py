import os
import logging
from typing import Any, <PERSON>, Tuple, Union
from abc import ABC, abstractmethod

from src.app.core.constant.constants import Environment, environment

logger = logging.getLogger(__name__)


class BaseConfigError(Exception):
    pass


class BaseEnvironmenConfig(ABC):
    @staticmethod
    def get_required_env(key: str) -> str:
        value = os.getenv(key)
        if not value:
            raise BaseConfigError(f"{key} environment variable is required but not set")
        return value.strip()

    @staticmethod
    def get_optional_env(key: str, default: str = None) -> str:
        value = os.getenv(key, default)
        return value.strip() if value else default

    @staticmethod
    def get_bool_env(key: str, default: bool = False) -> bool:
        value = os.getenv(key, str(default)).lower()
        return value in ('true', '1', 'yes', 'on')

    @staticmethod
    def get_int_env(key: str, default: int = None) -> int:
        value = os.getenv(key)
        if value is None:
            if default is None:
                raise BaseConfigError(f"{key} environment variable is required but not set")
            return default

        try:
            return int(value)
        except ValueError:
            raise BaseConfigError(f"{key} must be a valid integer, got: {value}")

    @staticmethod
    def get_float_env(key: str, default: float = None) -> float:
        value = os.getenv(key)
        if value is None:
            if default is None:
                raise BaseConfigError(f"{key} environment variable is required but not set")
            return default

        try:
            return float(value)
        except ValueError:
            raise BaseConfigError(f"{key} must be a valid float, got: {value}")

    @staticmethod
    def is_production_environment() -> bool:
        env = os.getenv(environment, Environment.DEVELOPMENT.value).lower()
        return env == Environment.PRODUCTION.value

    @staticmethod
    def is_development_environment() -> bool:
        return not BaseEnvironmenConfig.is_production_environment()
    
    @staticmethod
    def validate_url(url: str, field_name: str) -> None:
        if not url.startswith(('http://', 'https://')):
            raise BaseConfigError(f"{field_name} must be a valid HTTP/HTTPS URL")

    @staticmethod
    def validate_non_empty_fields(fields: dict) -> None:
        for field_name, field_value in fields.items():
            if not field_value or (isinstance(field_value, str) and not field_value.strip()):
                raise BaseConfigError(f"{field_name} cannot be empty")

    @staticmethod
    def check_development_indicators(
        values: List[str],
        indicators: List[Tuple[str, str]]
    ) -> None:
        if not BaseEnvironmenConfig.is_production_environment():
            return

        for indicator, description in indicators:
            if any(indicator.lower() in str(value).lower() for value in values if value):
                logger.warning(
                    f"Production environment detected but {description} contains development values"
                )
    
    @staticmethod
    def validate_secret_strength(secret: str, field_name: str, min_length: int = 32) -> None:
        if not secret or len(secret) < min_length:
            raise BaseConfigError(f"{field_name} must be at least {min_length} characters long")

        if BaseEnvironmenConfig.is_production_environment():
            development_indicators = ['dev', 'test', 'local', 'development', 'demo']
            for indicator in development_indicators:
                if indicator in secret.lower():
                    logger.warning(f"Production environment detected but {field_name} contains '{indicator}'")

    @staticmethod
    def validate_range(
        value: Union[int, float],
        field_name: str,
        min_value: Union[int, float] = None,
        max_value: Union[int, float] = None
    ) -> None:
        if min_value is not None and value < min_value:
            raise BaseConfigError(f"{field_name} must be at least {min_value}, got: {value}")

        if max_value is not None and value > max_value:
            raise BaseConfigError(f"{field_name} must be at most {max_value}, got: {value}")

    def _log_config_loaded(self, config_name: str) -> None:
        env_type = "production" if BaseEnvironmenConfig.is_production_environment() else "development"
        logger.info(f"{config_name} configuration loaded successfully ({env_type} mode)")
    
    @abstractmethod
    def _validate_config(self) -> None:
        pass
    
    def _validate_production_config(self) -> None:
        if BaseEnvironmenConfig.is_production_environment():
            logger.info("Production environment detected - performing additional validation")

    def get_config_summary(self) -> dict:
        return {
            "environment": "production" if BaseEnvironmenConfig.is_production_environment() else "development",
            "config_class": self.__class__.__name__
        }

    def _get_required_env(self, key: str) -> str:
        return BaseEnvironmenConfig.get_required_env(key)

    def _get_optional_env(self, key: str, default: str = None) -> str:
        return BaseEnvironmenConfig.get_optional_env(key, default)

    def _get_bool_env(self, key: str, default: bool = False) -> bool:
        return BaseEnvironmenConfig.get_bool_env(key, default)

    def _get_int_env(self, key: str, default: int = None) -> int:
        return BaseEnvironmenConfig.get_int_env(key, default)

    def _get_float_env(self, key: str, default: float = None) -> float:
        return BaseEnvironmenConfig.get_float_env(key, default)

    def _is_production_environment(self) -> bool:
        return BaseEnvironmenConfig.is_production_environment()

    def _is_development_environment(self) -> bool:
        return BaseEnvironmenConfig.is_development_environment()

    def _validate_url(self, url: str, field_name: str) -> None:
        return BaseEnvironmenConfig.validate_url(url, field_name)

    def _validate_non_empty_fields(self, fields: dict) -> None:
        return BaseEnvironmenConfig.validate_non_empty_fields(fields)

    def _validate_secret_strength(self, secret: str, field_name: str, min_length: int = 32) -> None:
        return BaseEnvironmenConfig.validate_secret_strength(secret, field_name, min_length)

    def _validate_range(self, value: Union[int, float], field_name: str, min_value: Union[int, float] = None, max_value: Union[int, float] = None) -> None:
        return BaseEnvironmenConfig.validate_range(value, field_name, min_value, max_value)

    def _check_development_indicators(self, values: List[str], indicators: List[Tuple[str, str]]) -> None:
        return BaseEnvironmenConfig.check_development_indicators(values, indicators)


    @staticmethod
    def validate_required_env_vars(required_vars: List[str]) -> List[str]:
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        return missing_vars

    @staticmethod
    def mask_sensitive_env_value(key: str, value: str) -> str:
        sensitive_keys = [
            'password', 'secret', 'key', 'token', 'url', 'connection'
        ]

        if any(sensitive in key.lower() for sensitive in sensitive_keys):
            if len(value) <= 8:
                return "***"
            else:
                return f"{value[:3]}***{value[-3:]}"

        return value

    @staticmethod
    def get_env_summary(env_vars: List[str]) -> dict:
        summary = {}
        for var in env_vars:
            value = os.getenv(var)
            if value:
                summary[var] = BaseEnvironmenConfig.mask_sensitive_env_value(var, value)
            else:
                summary[var] = "NOT_SET"

        return summary

    @staticmethod
    def log_env_validation_results(
        required_vars: List[str],
        optional_vars: dict = None
    ) -> bool:
        logger.info("🔍 Validating environment variables...")

        missing_vars = BaseEnvironmenConfig.validate_required_env_vars(required_vars)

        if missing_vars:
            logger.error(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
            return False

        logger.info("📋 Required environment variables:")
        for var in required_vars:
            value = os.getenv(var)
            masked_value = BaseEnvironmenConfig.mask_sensitive_env_value(var, value)
            logger.info(f"  ✅ {var}: {masked_value}")

        if optional_vars:
            logger.info("📋 Optional environment variables:")
            for var, default in optional_vars.items():
                value = os.getenv(var, default)
                masked_value = BaseEnvironmenConfig.mask_sensitive_env_value(var, value)
                logger.info(f"  ℹ️  {var}: {masked_value}")

        logger.info("✅ All required environment variables are set")
        return True
