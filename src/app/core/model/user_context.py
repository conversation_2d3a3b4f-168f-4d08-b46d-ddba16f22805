from typing import List, Optional
from pydantic import BaseModel, ConfigDict


class UserContext(BaseModel):
    user_id: str
    username: str
    email: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    realm: str
    tenant_id: str
    roles: List[str] = []
    is_active: bool = True
    
    model_config = ConfigDict(
        from_attributes=True,
        extra="forbid",
        populate_by_name=True,
    )
