import asyncio
import logging
from datetime import datetime, timezone
from typing import List, Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from src.app.core.db_connection.db_connector import get_session
from src.app.dao.entity.tenant import Tenant
from src.app.dao.entity.user import User
from src.app.core.constant.constants import UserRole
from src.app.dao.entity.api_key import ApiKey
from src.app.dao.entity.notification_event import NotificationEvent
from src.app.dao.entity.policy import Policy
from src.app.dao.entity.project import Project
from src.app.dao.entity.rule import Rule
from src.app.dao.entity.provider_config import ProviderConfig
from src.app.dao.entity.log_entry import LogEntry

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseSeeder:
    """Database seeder for creating sample data."""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        
    async def seed_all(self) -> None:
        """Seed all entities with sample data."""
        logger.info("🌱 Starting database seeding...")
        
        try:
            # Seed in dependency order
            tenants = await self.seed_tenants()
            users = await self.seed_users(tenants)
            api_keys = await self.seed_api_keys(tenants, users)
            projects = await self.seed_projects(tenants)
            policies = await self.seed_policies(tenants, projects, users)
            rules = await self.seed_rules(tenants, policies)
            provider_configs = await self.seed_provider_configs(tenants)
            notifications = await self.seed_notifications(tenants, users, api_keys)
            log_entries = await self.seed_log_entries(tenants, users, api_keys)
            
            await self.session.commit()
            logger.info("✅ Database seeding completed successfully!")
            
        except Exception as e:
            await self.session.rollback()
            logger.error(f"❌ Database seeding failed: {e}")
            raise
            
    async def seed_tenants(self) -> List[Tenant]:
        """Seed tenant data."""
        logger.info("📊 Seeding tenants...")
        
        tenants_data = [
            {
                "organization_name": "Acme Corporation",
                "industry": "Technology",
                "region_preference": "US_EAST",
                "subscription_level": "ENTERPRISE"
            },
            {
                "organization_name": "Beta Industries",
                "industry": "Finance",
                "industry": "Finance",
                "region_preference": "EU_WEST",
                "subscription_level": "PROFESSIONAL"
            }
        ]
        
        tenants = []
        for tenant_data in tenants_data:
            existing = await self.session.scalar(
                select(Tenant).where(Tenant.organization_name == tenant_data["organization_name"])
            )

            if not existing:
                tenant = Tenant(**tenant_data)
                self.session.add(tenant)
                await self.session.flush()
                tenants.append(tenant)
                logger.info(f"  ➕ Created tenant: {tenant.organization_name}")
            else:
                tenants.append(existing)
                logger.info(f"  ✓ Tenant already exists: {existing.organization_name}")
                
        return tenants
        
    async def seed_users(self, tenants: List[Tenant]) -> List[User]:
        """Seed user data."""
        logger.info("👥 Seeding users...")
        
        users_data = [
            {
                "email": "<EMAIL>",
                "first_name": "Admin",
                "last_name": "User",
                "role": UserRole.PLATFORM_ADMIN,
                "tenant_id": tenants[0].id
            },
            {
                "email": "<EMAIL>",
                "first_name": "Developer",
                "last_name": "User",
                "role": UserRole.DEVELOPER,
                "tenant_id": tenants[0].id
            },
            {
                "email": "<EMAIL>",
                "first_name": "Support",
                "last_name": "Agent",
                "role": UserRole.SUPPORT_AGENT,
                "tenant_id": tenants[0].id
            },
            {
                "email": "<EMAIL>",
                "first_name": "Read",
                "last_name": "Only",
                "role": UserRole.AUDITOR,
                "tenant_id": tenants[1].id if len(tenants) > 1 else tenants[0].id
            }
        ]
        
        users = []
        for user_data in users_data:
            existing = await self.session.scalar(
                select(User).where(User.email == user_data["email"])
            )

            if not existing:
                user = User(**user_data)
                self.session.add(user)
                await self.session.flush()
                users.append(user)
                logger.info(f"  ➕ Created user: {user.email}")
            else:
                users.append(existing)
                logger.info(f"  ✓ User already exists: {existing.email}")
                
        return users
        
    async def seed_api_keys(self, tenants: List[Tenant], users: List[User]) -> List[ApiKey]:
        """Seed API key data."""
        logger.info("🔑 Seeding API keys...")
        
        api_keys_data = [
            {
                "label": "Development API Key",
                "hashed_token": "dev_key_12345abcdef_hashed",
                "status": "ACTIVE",
                "tenant_id": tenants[0].id,
                "user_id": users[0].id
            },
            {
                "label": "Testing API Key",
                "hashed_token": "test_key_67890ghijkl_hashed",
                "status": "ACTIVE",
                "tenant_id": tenants[0].id,
                "user_id": users[1].id
            },
            {
                "label": "Expired Legacy Key",
                "hashed_token": "expired_key_legacy_hashed",
                "status": "EXPIRED",
                "tenant_id": tenants[0].id,
                "user_id": users[0].id
            },
            {
                "label": "Revoked Security Key",
                "hashed_token": "revoked_security_key_hashed",
                "status": "REVOKED",
                "tenant_id": tenants[1].id if len(tenants) > 1 else tenants[0].id,
                "user_id": users[2].id if len(users) > 2 else users[0].id
            }
        ]
        
        api_keys = []
        for api_key_data in api_keys_data:
            existing = await self.session.scalar(
                select(ApiKey).where(ApiKey.label == api_key_data["label"])
            )

            if not existing:
                api_key = ApiKey(**api_key_data)
                self.session.add(api_key)
                await self.session.flush()
                api_keys.append(api_key)
                logger.info(f"  ➕ Created API key: {api_key.label}")
            else:
                api_keys.append(existing)
                logger.info(f"  ✓ API key already exists: {existing.label}")
                
        return api_keys
        
    async def seed_projects(self, tenants: List[Tenant]) -> List[Project]:
        """Seed project data."""
        logger.info("📁 Seeding projects...")
        
        projects_data = [
            {
                "name": "Web Application Security",
                "description": "Security policies for web applications",
                "tenant_id": tenants[0].id
            },
            {
                "name": "API Security Framework",
                "description": "Security framework for REST APIs",
                "tenant_id": tenants[0].id
            }
        ]
        
        projects = []
        for project_data in projects_data:
            existing = await self.session.scalar(
                select(Project).where(Project.name == project_data["name"])
            )
            
            if not existing:
                project = Project(**project_data)
                self.session.add(project)
                await self.session.flush()
                projects.append(project)
                logger.info(f"  ➕ Created project: {project.name}")
            else:
                projects.append(existing)
                logger.info(f"  ✓ Project already exists: {existing.name}")
                
        return projects

    async def seed_policies(self, tenants: List[Tenant], projects: List[Project], users: List[User]) -> List[Policy]:
        """Seed policy data."""
        logger.info("📋 Seeding policies...")

        sample_yaml_1 = b"""
rules:
  - id: "sql-injection-check"
    name: "SQL Injection Detection"
    severity: "high"
    pattern: "SELECT.*FROM.*WHERE.*="
  - id: "xss-prevention"
    name: "XSS Prevention"
    severity: "medium"
    pattern: "<script.*>"
"""

        sample_yaml_2 = b"""
rules:
  - id: "auth-required"
    name: "Authentication Required"
    severity: "critical"
    pattern: "public.*endpoint"
  - id: "rate-limiting"
    name: "Rate Limiting"
    severity: "medium"
    pattern: "requests.*per.*minute"
"""

        policies_data = [
            {
                "name": "Web Security Policy v1.0",
                "version": 1,
                "status": "ACTIVE",
                "yaml_blob": sample_yaml_1,
                "tenant_id": tenants[0].id,
                "project_id": projects[0].id,
                "last_edited_by_id": users[0].id
            },
            {
                "name": "API Security Policy v1.0",
                "version": 1,
                "status": "DRAFT",
                "yaml_blob": sample_yaml_2,
                "tenant_id": tenants[0].id,
                "project_id": projects[1].id,
                "last_edited_by_id": users[1].id
            },
            {
                "name": "Legacy Security Policy v0.9",
                "version": 1,
                "status": "ARCHIVED",
                "yaml_blob": b"# Archived legacy policy\nrules: []",
                "tenant_id": tenants[0].id,
                "project_id": projects[0].id,
                "last_edited_by_id": users[0].id
            }
        ]

        policies = []
        for policy_data in policies_data:
            # Check if policy already exists
            existing = await self.session.scalar(
                select(Policy).where(Policy.name == policy_data["name"])
            )

            if not existing:
                policy = Policy(**policy_data)
                self.session.add(policy)
                await self.session.flush()
                policies.append(policy)
                logger.info(f"  ➕ Created policy: {policy.name}")
            else:
                policies.append(existing)
                logger.info(f"  ✓ Policy already exists: {existing.name}")

        return policies

    async def seed_rules(self, tenants: List[Tenant], policies: List[Policy]) -> List[Rule]:
        logger.info("📏 Seeding rules...")

        rules_data = [
            {
                "rule_id": "sql-injection-check",
                "name": "SQL Injection Detection Rule",
                "policy_id": policies[0].id,
                "tenant_id": tenants[0].id
            },
            {
                "rule_id": "xss-prevention",
                "name": "XSS Prevention Rule",
                "policy_id": policies[0].id,
                "tenant_id": tenants[0].id
            },
            {
                "rule_id": "auth-required",
                "name": "Authentication Required Rule",
                "policy_id": policies[1].id,
                "tenant_id": tenants[0].id
            },
            {
                "rule_id": "rate-limiting",
                "name": "Rate Limiting Rule",
                "policy_id": policies[1].id,
                "tenant_id": tenants[0].id
            }
        ]

        rules = []
        for rule_data in rules_data:
            existing = await self.session.scalar(
                select(Rule).where(Rule.rule_id == rule_data["rule_id"])
            )

            if not existing:
                rule = Rule(**rule_data)
                self.session.add(rule)
                await self.session.flush()
                rules.append(rule)
                logger.info(f"  ➕ Created rule: {rule.name}")
            else:
                rules.append(existing)
                logger.info(f"  ✓ Rule already exists: {existing.name}")

        return rules

    async def seed_provider_configs(self, tenants: List[Tenant]) -> List[ProviderConfig]:
        """Seed provider config data."""
        logger.info("🔧 Seeding provider configs...")

        provider_configs_data = [
            {
                "provider_type": "OPENAI",
                "credentials": "sk-test-openai-key-12345",
                "default_model": "gpt-4",
                "tenant_id": tenants[0].id
            },
            {
                "provider_type": "ANTHROPIC",
                "credentials": "sk-ant-test-key-67890",
                "default_model": "claude-3-sonnet",
                "tenant_id": tenants[0].id
            },
            {
                "provider_type": "GOOGLE_AI",
                "credentials": "google-ai-key-test-12345",
                "default_model": "gemini-pro",
                "tenant_id": tenants[1].id if len(tenants) > 1 else tenants[0].id
            },
            {
                "provider_type": "SELF_HOSTED",
                "credentials": "http://localhost:8080/api/v1",
                "default_model": "llama-2-7b",
                "tenant_id": tenants[1].id if len(tenants) > 1 else tenants[0].id
            }
        ]

        provider_configs = []
        for config_data in provider_configs_data:
            existing = await self.session.scalar(
                select(ProviderConfig).where(
                    ProviderConfig.provider_type == config_data["provider_type"],
                    ProviderConfig.tenant_id == config_data["tenant_id"]
                )
            )

            if not existing:
                provider_config = ProviderConfig(**config_data)
                self.session.add(provider_config)
                await self.session.flush()
                provider_configs.append(provider_config)
                logger.info(f"  ➕ Created provider config: {provider_config.provider_type}")
            else:
                provider_configs.append(existing)
                logger.info(f"  ✓ Provider config already exists: {existing.provider_type}")

        return provider_configs

    async def seed_notifications(self, tenants: List[Tenant], users: List[User], api_keys: List[ApiKey]) -> List[NotificationEvent]:
        logger.info("🔔 Seeding notifications...")

        notifications_data = [
            {
                "event_type": "VIOLATION",
                "details": "SQL injection attempt detected in user input",
                "sent_to": "<EMAIL>",
                "tenant_id": tenants[0].id,
                "user_id": users[0].id,
                "api_key_id": None
            },
            {
                "event_type": "SYSTEM_ALERT",
                "details": "High CPU usage detected on security scanner",
                "sent_to": "<EMAIL>",
                "tenant_id": tenants[0].id,
                "user_id": None,
                "api_key_id": api_keys[0].id
            },
            {
                "event_type": "USAGE_THRESHOLD",
                "details": "Monthly API usage has exceeded 80% of allocated quota",
                "sent_to": "<EMAIL>",
                "tenant_id": tenants[0].id,
                "user_id": users[0].id,
                "api_key_id": api_keys[1].id
            }
        ]

        notifications = []
        for notification_data in notifications_data:
            existing = await self.session.scalar(
                select(NotificationEvent).where(
                    NotificationEvent.details == notification_data["details"]
                )
            )

            if not existing:
                notification = NotificationEvent(**notification_data)
                self.session.add(notification)
                await self.session.flush()
                notifications.append(notification)
                logger.info(f"  ➕ Created notification: {notification.event_type}")
            else:
                notifications.append(existing)
                logger.info(f"  ✓ Notification already exists: {existing.event_type}")

        return notifications

    async def seed_log_entries(self, tenants: List[Tenant], users: List[User], api_keys: List[ApiKey]) -> List[LogEntry]:
        logger.info("📝 Seeding log entries...")

        log_entries_data = [
            {
                "request_id": "req_12345_sql_injection_test",
                "api_key_id": api_keys[0].id,
                "user_id": users[0].id,
                "tenant_id": tenants[0].id,
                "model": "gpt-4-turbo",
                "prompt_text": "SELECT * FROM users WHERE id = 1; DROP TABLE users;",
                "outcome": "BLOCKED",
                "rule_triggers": ["sql-injection-check"],
                "provider_latency_ms": 150,
                "prompt_tokens": 25,
                "completion_tokens": 0,
                "masked_response": None
            },
            {
                "request_id": "req_67890_normal_query",
                "api_key_id": api_keys[1].id,
                "user_id": users[1].id,
                "tenant_id": tenants[0].id,
                "model": "claude-3-sonnet",
                "prompt_text": "What is the weather like today?",
                "outcome": "ALLOWED",
                "rule_triggers": None,
                "provider_latency_ms": 89,
                "prompt_tokens": 8,
                "completion_tokens": 42,
                "masked_response": "The weather varies by location. Please specify your city for accurate information."
            },
            {
                "request_id": "req_11111_sensitive_content",
                "api_key_id": api_keys[0].id,
                "user_id": users[2].id if len(users) > 2 else users[0].id,
                "tenant_id": tenants[0].id,
                "model": "gpt-4",
                "prompt_text": "Please provide personal information about John Doe including SSN and credit card details",
                "outcome": "REDACTED",
                "rule_triggers": ["pii-detection", "sensitive-data-check"],
                "provider_latency_ms": 120,
                "prompt_tokens": 18,
                "completion_tokens": 35,
                "masked_response": "I cannot provide personal information such as [REDACTED] or [REDACTED] for privacy and security reasons."
            }
        ]

        log_entries = []
        for log_entry_data in log_entries_data:
            existing = await self.session.scalar(
                select(LogEntry).where(
                    LogEntry.request_id == log_entry_data["request_id"]
                )
            )

            if not existing:
                log_entry = LogEntry(**log_entry_data)
                self.session.add(log_entry)
                await self.session.flush()
                log_entries.append(log_entry)
                logger.info(f"  ➕ Created log entry: {log_entry.request_id}")
            else:
                log_entries.append(existing)
                logger.info(f"  ✓ Log entry already exists: {existing.request_id}")

        return log_entries


async def seed_database() -> None:
    async for session in get_session():
        seeder = DatabaseSeeder(session)
        await seeder.seed_all()
        break


if __name__ == "__main__":
    asyncio.run(seed_database())
