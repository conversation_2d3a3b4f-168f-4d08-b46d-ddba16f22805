from fastapi import <PERSON><PERSON><PERSON>
from starlette.requests import Request
from starlette.status import HTTP_500_INTERNAL_SERVER_ERROR

from src.app.core.exception.exception_response import ExceptionResponse
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.core.exception.processing_exception import ProcessingException


def init_exception_handlers(app: FastAPI):
    @app.exception_handler(ProcessingException)
    async def processing_exception_handler(request: Request, exception: ProcessingException):
        return ExceptionResponse(
            status_code=exception.status_code,
            message=exception.message,
            cause=exception.cause
        )

    @app.exception_handler(NotFoundException)
    async def not_found_exception_handler(request: Request, exception: NotFoundException):
        return ExceptionResponse(
            status_code=exception.status_code,
            message=exception.message,
            cause=exception.cause
        )

    @app.exception_handler(Exception)
    async def not_found_exception_handler(request: Request, exception: Exception):
        return ExceptionResponse(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            message=str(exception),
            cause=repr(exception.__cause__)
        )
