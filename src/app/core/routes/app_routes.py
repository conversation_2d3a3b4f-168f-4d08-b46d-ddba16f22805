from enum import Enum

class ResourceTag(str, Enum):
    USER = "users"
    TENANT = "tenants"
    API_KEY = "api_keys"
    LOG_ENTRY = "log_entries"
    NOTIFICATION = "notifications"
    POLICY = "policies"
    PROJECT = "projects"
    RULE = "rules"
    PROVIDER_CONFIG = "provider_configs"
    KEYCLOAK = "keycloak"

class ResourceRoute(str, Enum):
    USER = "/users"
    TENANT = "/tenants"
    API_KEY = "/api-keys"
    LOG_ENTRY = "/log-entries"
    NOTIFICATION = "/notifications"
    POLICY = "/policies"
    PROJECT = "/projects"
    RULE = "/rules"
    PROVIDER_CONFIG = "/provider_configs"
    KEYCLOAK = "/keycloak"

class PathParam(str, Enum):
    USER_ID = "/{user_id}"
    TENANT_ID = "/{tenant_id}"
    API_KEY_ID = "/{api_key_id}"
    LOG_ENTRY_ID = "/{log_entry_id}"
    NOTIFICATION_ID = "/{notification_id}"
    POLICY_ID = "/{policy_id}"
    PROJECT_ID = "/{project_id}"
    RULE_ID = "/{rule_id}"
    PROVIDER_CONFIG_ID = "/{provider_config_id}"