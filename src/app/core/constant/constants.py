from enum import Enum


environment = "ENVIRONMENT"

class UserRole(str, Enum):
    PLATFORM_ADMIN   = "PLATFORM_ADMIN"
    TENANT_ADMIN     = "TENANT_ADMIN"
    POLICY_EDITOR    = "POLICY_EDITOR"
    DEVELOPER        = "DEVELOPER"
    AUDITOR          = "AUDITOR"
    SUPPORT_AGENT    = "SUPPORT_AGENT"

class Error(str, Enum):
    BAD_REQUEST = "bad request"
    UNAUTHORIZED = "not authorized"
    FORBIDDEN = "forbidden"
    NOT_FOUND = "not found"
    SERVICE_UNAVAILABLE = "service unavailable"

class KeycloakConfig(str, Enum):
    KEY<PERSON>OAK_URL = "KEYCLOAK_URL"
    KEYCLOAK_ADMIN_USER = "KEYCLOAK_ADMIN_USER"
    KEYCLOAK_ADMIN_PASSWORD = "KEYCLOAK_ADMIN_PASSWORD"
    BACKEND_URL = "BACKEND_URL"
    KEYCLOAK_CLIENT_SECRET = "KEYCLOAK_CLIENT_SECRET"
    KEYCLOAK_MASTER_REALM = "KEYCLOAK_MASTER_REALM"
    KEYCLOAK_CLIENT_NAME = "securinest"

class JWTConfig(str, Enum):
    JWT_ALGORITHM = "JWT_ALGORITHM"
    JWT_AUDIENCE = "JWT_AUDIENCE"
    JWT_ISSUER_BASE_URL = "JWT_ISSUER_BASE_URL"
    JWT_CACHE_TTL = "JWT_CACHE_TTL"

class EmailConfig(str, Enum):
    EMAIL_VERIFICATION_SECRET = "EMAIL_VERIFICATION_SECRET"
    EMAIL_SESSION_SECRET = "EMAIL_SESSION_SECRET"
    EMAIL_TOKEN_EXPIRY_HOURS = "EMAIL_TOKEN_EXPIRY_HOURS"
    
class Environment(str, Enum):
    DEVELOPMENT = "development"
    PRODUCTION = "production"

    
class LogConfig(str, Enum):
    LOG_LEVEL = "LOG_LEVEL"
    LOG_FORMAT = "LOG_FORMAT"
    ENABLE_FILE_LOGGING = "ENABLE_FILE_LOGGING"
    LOG_FILE_PATH = "LOG_FILE_PATH"
    MAX_LOG_FILE_SIZE_MB = "MAX_LOG_FILE_SIZE_MB"
    LOG_FILE_BACKUP_COUNT = "LOG_FILE_BACKUP_COUNT"
    
class LogType(str, Enum):
    SQLALCHEMY_LOG_LEVEL = "SQLALCHEMY_LOG_LEVEL"
    SQLALCHEMY_POOL_LOG_LEVEL = "SQLALCHEMY_POOL_LOG_LEVEL"
    SQLALCHEMY_DIALECTS_LOG_LEVEL = "SQLALCHEMY_DIALECTS_LOG_LEVEL"
    UVICORN_ACCESS_LOG_LEVEL = "UVICORN_ACCESS_LOG_LEVEL"
    UVICORN_ERROR_LOG_LEVEL = "UVICORN_ERROR_LOG_LEVEL"
    HTTPX_LOG_LEVEL = "HTTPX_LOG_LEVEL"
    HTTPCORE_LOG_LEVEL = "HTTPCORE_LOG_LEVEL"
    APP_SERVICE_LOG_LEVEL = "APP_SERVICE_LOG_LEVEL"
    CORE_SERVICE_LOG_LEVEL = "CORE_SERVICE_LOG_LEVEL"
    SECURITY_LOG_LEVEL = "SECURITY_LOG_LEVEL"
    DATABASE_LOG_LEVEL = "DATABASE_LOG_LEVEL"

class LogLevel(str, Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class DatabaseConfig(str, Enum):
    DATABASE_URL = "DATABASE_URL"
    AUTO_SEED_DATABASE = "AUTO_SEED_DATABASE"
    DB_POOL_SIZE = "DB_POOL_SIZE"
    DB_MAX_OVERFLOW = "DB_MAX_OVERFLOW"
    DB_POOL_TIMEOUT = "DB_POOL_TIMEOUT"
    DB_ECHO = "DB_ECHO"

