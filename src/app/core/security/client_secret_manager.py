import logging
import asyncio
from typing import Optional, Dict
from datetime import datetime, timezone, timedelta

import bcrypt

logger = logging.getLogger(__name__)


class ClientSecretError(Exception):
    pass


class ClientSecretManager:
    def __init__(self, cache_ttl_minutes: int = 60):
        self._cache: Dict[str, Dict[str, any]] = {}
        self._cache_lock = asyncio.Lock()
        self._cache_ttl = timedelta(minutes=cache_ttl_minutes)
        
        logger.info(f"Initialized ClientSecretManager with {cache_ttl_minutes}min cache TTL")
    
    def hash_client_secret(self, client_secret: str) -> str:
        try:
            if not client_secret or not client_secret.strip():
                raise ClientSecretError("Client secret cannot be empty")
            
            salt = bcrypt.gensalt(rounds=12)
            hashed = bcrypt.hashpw(client_secret.encode('utf-8'), salt)
            
            logger.debug("Successfully hashed client secret")
            return hashed.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Failed to hash client secret: {e}")
            raise ClientSecretError(f"Failed to hash client secret: {e}")
    
    def verify_client_secret(self, client_secret: str, hashed_secret: str) -> bool:
        try:
            if not client_secret or not hashed_secret:
                return False
            
            result = bcrypt.checkpw(
                client_secret.encode('utf-8'), 
                hashed_secret.encode('utf-8')
            )
            
            logger.debug(f"Client secret verification: {'success' if result else 'failed'}")
            return result
            
        except Exception as e:
            logger.error(f"Error verifying client secret: {e}")
            return False
    
    async def cache_client_secret(self, realm_name: str, client_secret: str) -> None:
        try:
            async with self._cache_lock:
                await self._cleanup_expired_cache()
                
                self._cache[realm_name] = {
                    'secret': client_secret,
                    'cached_at': datetime.now(timezone.utc)
                }
                
                logger.debug(f"Cached client secret for realm: {realm_name}")
                
        except Exception as e:
            logger.error(f"Failed to cache client secret for realm {realm_name}: {e}")
    
    async def get_cached_client_secret(self, realm_name: str) -> Optional[str]:
        try:
            async with self._cache_lock:
                cached_entry = self._cache.get(realm_name)
                
                if not cached_entry:
                    logger.debug(f"No cached secret found for realm: {realm_name}")
                    return None
                
                cached_at = cached_entry['cached_at']
                if datetime.now(timezone.utc) - cached_at > self._cache_ttl:
                    del self._cache[realm_name]
                    logger.debug(f"Expired cached secret removed for realm: {realm_name}")
                    return None
                
                logger.debug(f"Retrieved cached secret for realm: {realm_name}")
                return cached_entry['secret']
                
        except Exception as e:
            logger.error(f"Error retrieving cached secret for realm {realm_name}: {e}")
            return None
    
    async def clear_cache(self, realm_name: Optional[str] = None) -> None:
        try:
            async with self._cache_lock:
                if realm_name:
                    self._cache.pop(realm_name, None)
                    logger.debug(f"Cleared cache for realm: {realm_name}")
                else:
                    self._cache.clear()
                    logger.debug("Cleared all cached secrets")
                    
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
    
    async def _cleanup_expired_cache(self) -> None:
        try:
            current_time = datetime.now(timezone.utc)
            expired_realms = []
            
            for realm_name, cached_entry in self._cache.items():
                if current_time - cached_entry['cached_at'] > self._cache_ttl:
                    expired_realms.append(realm_name)
            
            for realm_name in expired_realms:
                del self._cache[realm_name]
            
            if expired_realms:
                logger.debug(f"Cleaned up {len(expired_realms)} expired cache entries")
                
        except Exception as e:
            logger.error(f"Error during cache cleanup: {e}")


client_secret_manager = ClientSecretManager()


def get_client_secret_manager() -> ClientSecretManager:
    return client_secret_manager
