import asyncio
import logging
import os
import time
from typing import Optional, Dict, Any
from functools import wraps
from collections import OrderedDict

import httpx
from fastapi import HTTPException, status, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt, jwk
from jose.utils import base64url_decode

from src.app.core.config.keycloak_config import keycloak_config
from src.app.core.constant.constants import JWTConfig
from src.app.core.model.user_context import UserContext

logger = logging.getLogger(__name__)

security = HTTPBearer()

class KeycloakAuthError(HTTPException):
    def __init__(self, detail: str, status_code: int = status.HTTP_401_UNAUTHORIZED):
        super().__init__(status_code=status_code, detail=detail)


class ThreadSafeCache:
    def __init__(self, max_size: int = 100):
        self.max_size = max_size
        self._cache: OrderedDict[str, Dict[str, Any]] = OrderedDict()
        self._timestamps: OrderedDict[str, float] = OrderedDict()
        self._lock = asyncio.Lock()

    async def get(self, key: str, ttl: int) -> Optional[Dict[str, Any]]:
        async with self._lock:
            if key not in self._cache:
                return None

            current_time = time.time()
            if current_time - self._timestamps[key] >= ttl:
                # Remove expired item
                del self._cache[key]
                del self._timestamps[key]
                return None

            self._cache.move_to_end(key)
            self._timestamps.move_to_end(key)

            return self._cache[key]

    async def set(self, key: str, value: Dict[str, Any]) -> None:
        async with self._lock:
            current_time = time.time()

            if key in self._cache:
                del self._cache[key]
                del self._timestamps[key]

            while len(self._cache) >= self.max_size:
                oldest_key = next(iter(self._cache))
                del self._cache[oldest_key]
                del self._timestamps[oldest_key]

            self._cache[key] = value
            self._timestamps[key] = current_time

    async def clear(self) -> None:
        async with self._lock:
            self._cache.clear()
            self._timestamps.clear()

    async def size(self) -> int:
        async with self._lock:
            return len(self._cache)


class KeycloakAuth:
    def __init__(self):
        self._cache_ttl = int(os.getenv(JWTConfig.JWT_CACHE_TTL.value))
        self._jwk_cache = ThreadSafeCache(max_size=50)

        self._fetch_locks: Dict[str, asyncio.Lock] = {}
        self._locks_lock = asyncio.Lock()
        self._lock_cleanup_threshold = 100

    async def _get_fetch_lock(self, realm_name: str) -> asyncio.Lock:
        async with self._locks_lock:
            if realm_name not in self._fetch_locks:
                self._fetch_locks[realm_name] = asyncio.Lock()

            if len(self._fetch_locks) > self._lock_cleanup_threshold:
                await self._cleanup_unused_locks()

            return self._fetch_locks[realm_name]

    async def _cleanup_unused_locks(self) -> None:
        try:
            locks_to_remove = []
            for realm_name, lock in self._fetch_locks.items():
                if not lock.locked():
                    locks_to_remove.append(realm_name)

            for realm_name in locks_to_remove[:50]:
                del self._fetch_locks[realm_name]

            if locks_to_remove:
                logger.debug(f"Cleaned up {len(locks_to_remove)} unused realm locks")

        except Exception as e:
            logger.warning(f"Error during lock cleanup: {e}")

    async def _fetch_jwk_set(self, realm_name: str) -> Dict[str, Any]:
        jwk_uri = keycloak_config.get_realm_jwk_set_uri(realm_name)

        try:
            timeout = httpx.Timeout(10.0, connect=5.0)
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.get(jwk_uri)
                response.raise_for_status()
                logger.debug(f"Fetched JWK set for realm {realm_name}")

                return response.json()

        except httpx.TimeoutException:
            logger.error(f"Timeout fetching JWK set for realm {realm_name}")
            raise KeycloakAuthError(f"Timeout fetching JWK set for realm {realm_name}")
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching JWK set for realm {realm_name}: {e.response.status_code}")
            raise KeycloakAuthError(f"Failed to fetch JWK set: HTTP {e.response.status_code}")
        except Exception as e:
            logger.error(f"Failed to fetch JWK set for realm {realm_name}: {e}")
            raise KeycloakAuthError(f"Failed to fetch JWK set: {e}")

    async def _get_jwk_set(self, realm_name: str) -> Dict[str, Any]:
        cached_jwk_set = await self._jwk_cache.get(realm_name, self._cache_ttl)
        if cached_jwk_set is not None:
            logger.debug(f"Using cached JWK set for realm {realm_name}")
            return cached_jwk_set

        fetch_lock = await self._get_fetch_lock(realm_name)

        async with fetch_lock:
            cached_jwk_set = await self._jwk_cache.get(realm_name, self._cache_ttl)
            if cached_jwk_set is not None:
                logger.debug(f"Using cached JWK set for realm {realm_name} (double-check)")
                return cached_jwk_set

            logger.debug(f"Fetching JWK set for realm {realm_name}")
            jwk_set = await self._fetch_jwk_set(realm_name)
            await self._jwk_cache.set(realm_name, jwk_set)

            return jwk_set

    async def _get_signing_key(self, token: str, realm_name: str) -> str:
        try:
            unverified_header = jwt.get_unverified_header(token)
            kid = unverified_header.get('kid')

            if not kid:
                raise KeycloakAuthError("Token header missing 'kid' field")

            jwk_set = await self._get_jwk_set(realm_name)

            for key in jwk_set.get('keys', []):
                if key.get('kid') == kid:
                    public_key = jwk.construct(key).to_pem()
                    return public_key.decode('utf-8')

            raise KeycloakAuthError(f"Unable to find signing key with kid: {kid}")

        except KeycloakAuthError:
            raise
        except Exception as e:
            logger.error(f"Error getting signing key: {e}")
            return await self._get_public_key_legacy(realm_name)

    async def _get_public_key_legacy(self, realm_name: str) -> str:
        try:
            from keycloak import KeycloakOpenID

            keycloak_openid = KeycloakOpenID(
                server_url=keycloak_config.keycloak_url,
                client_id=keycloak_config.client_name,
                realm_name=realm_name,
                verify=True
            )

            public_key = keycloak_openid.public_key()
            return f"-----BEGIN PUBLIC KEY-----\n{public_key}\n-----END PUBLIC KEY-----\n"

        except Exception as e:
            logger.error(f"Failed to get public key for realm {realm_name}: {e}")
            raise KeycloakAuthError(f"Failed to get public key: {e}")
    
    async def verify_token(self, token: str, realm_name: str) -> Dict[str, Any]:
        try:
            signing_key = await self._get_signing_key(token, realm_name)

            jwt_algorithm = os.getenv(JWTConfig.JWT_ALGORITHM.value)

            if not jwt_algorithm:
                raise KeycloakAuthError(f"{JWTConfig.JWT_ALGORITHM.value} environment variable not configured")

            payload = jwt.decode(
                token,
                signing_key,
                algorithms=[jwt_algorithm],
                audience=keycloak_config.client_name,
                issuer=keycloak_config.get_realm_issuer(realm_name)
            )

            import time
            current_time = int(time.time())
            if payload.get('exp', 0) <= current_time:
                raise KeycloakAuthError("Token has expired")

            return payload

        except JWTError as e:
            logger.warning(f"JWT verification failed: {e}")
            raise KeycloakAuthError("Invalid or expired token")
        except KeycloakAuthError:
            raise
        except Exception as e:
            logger.error(f"Token verification error: {e}")
            raise KeycloakAuthError("Token verification failed")
    
    async def extract_user_context(self, token: str) -> UserContext:
        try:
            unverified_payload = jwt.get_unverified_claims(token)
            
            issuer = unverified_payload.get("iss", "")
            if not issuer:
                raise KeycloakAuthError("Invalid token: missing issuer")
            
            realm_name = issuer.split("/realms/")[-1]
            if not realm_name or realm_name == issuer:
                raise KeycloakAuthError("Invalid token: cannot extract realm")
            
            payload = await self.verify_token(token, realm_name)
            
            tenant_id = self._extract_tenant_id_from_realm(realm_name)
            
            user_context = UserContext(
                user_id=payload.get("sub", ""),
                username=payload.get("preferred_username", ""),
                email=payload.get("email"),
                first_name=payload.get("given_name"),
                last_name=payload.get("family_name"),
                realm=realm_name,
                tenant_id=tenant_id,
                roles=payload.get("realm_access", {}).get("roles", []),
                is_active=payload.get("active", True)
            )
            
            return user_context
            
        except KeycloakAuthError:
            raise
        except Exception as e:
            logger.error(f"Failed to extract user context: {e}")
            raise KeycloakAuthError("Failed to extract user context")

    def _extract_tenant_id_from_realm(self, realm_name: str) -> str:
        import re

        match = re.search(r'-tenant-(\d+)$', realm_name)
        if match:
            return match.group(1)

        if realm_name.startswith("tenant-"):
            return realm_name.replace("tenant-", "")

        logger.warning(f"Could not extract tenant ID from realm name: {realm_name}")
        return realm_name

    async def clear_cache(self) -> None:
        await self._jwk_cache.clear()
        logger.info("JWK cache cleared")

    async def get_cache_stats(self) -> Dict[str, Any]:
        size = await self._jwk_cache.size()
        return {
            "cache_size": size,
            "max_cache_size": self._jwk_cache.max_size,
            "cache_ttl": self._cache_ttl
        }


keycloak_auth = KeycloakAuth()

def require_roles(*required_roles):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = None
            for value in kwargs.values():
                if isinstance(value, UserContext):
                    current_user = value
                    break

            if not current_user:
                raise KeycloakAuthError("Authentication required")

            from src.app.core.constant.constants import UserRole
            required_role_strings = []
            for role in required_roles:
                if isinstance(role, UserRole):
                    required_role_strings.append(role.value)
                else:
                    required_role_strings.append(str(role))

            user_roles = set(current_user.roles)
            required_roles_set = set(required_role_strings)

            if not user_roles.intersection(required_roles_set):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. Required roles: {', '.join(required_role_strings)}"
                )

            return await func(*args, **kwargs)
        return wrapper
    return decorator
