from src.app.core.constant.constants import Error
from src.app.core.exception.bad_request_exception import BadRequestException
from src.app.core.exception.forbidden_exception import ForbiddenException
from src.app.core.exception.not_found_exception import NotFoundException
from src.app.core.exception.processing_exception import ProcessingException
from src.app.core.exception.service_unavailable_exception import ServiceUnavailableException
from src.app.core.exception.unauthorized_exception import UnauthorizedException

TENANT = "tenant"
FOREIGN_KEY = "foreign key"
UNIQUE_CONSTRAINT = "unique constraint"


class ExceptionUtil:
    @staticmethod
    def process_exception(exception: Exception) -> Exception:
        message = str(exception)

        ExceptionUtil.handle_bad_request_exception(exception)

        if Error.BAD_REQUEST in message.lower():
            raise BadRequestException(message=message)

        if Error.UNAUTHORIZED in message.lower():
            raise UnauthorizedException(message=message)

        if Error.FORBIDDEN in message.lower():
            raise ForbiddenException(message=message)

        if Error.NOT_FOUND in message.lower():
            raise NotFoundException(message=message)

        if Error.SERVICE_UNAVAILABLE in message.lower():
            raise ServiceUnavailableException(message=message)

        raise ProcessingException(message=message)

    @staticmethod
    def handle_bad_request_exception(exception):
        error_message = str(exception).lower()

        if UNIQUE_CONSTRAINT in error_message:
            raise BadRequestException("Object already exists")
        elif FOREIGN_KEY in error_message and TENANT in error_message:
            raise BadRequestException("Invalid reference")
