import pytest
import asyncio
from datetime import datetime, timezone, timedelta

from src.app.core.util.session_state_manager import SessionS<PERSON><PERSON>anager, SessionStateError


class TestSessionStateManager:
    """Test suite for SessionStateManager."""
    
    @pytest.fixture
    def state_manager(self):
        """Create SessionStateManager instance for testing."""
        return SessionStateManager(max_size=5, ttl_minutes=1)
    
    @pytest.mark.asyncio
    async def test_store_and_retrieve_state(self, state_manager):
        """Test storing and retrieving state data."""
        # Arrange
        state = "test-state-123"
        realm_name = "test-realm"
        tenant_id = "test-tenant"
        nonce = "test-nonce"
        redirect_uri = "https://app.example.com/callback"
        
        # Act
        await state_manager.store_state(
            state=state,
            realm_name=realm_name,
            tenant_id=tenant_id,
            nonce=nonce,
            redirect_uri=redirect_uri
        )
        
        retrieved_data = await state_manager.retrieve_state(state)
        
        # Assert
        assert retrieved_data is not None
        assert retrieved_data['realm_name'] == realm_name
        assert retrieved_data['tenant_id'] == tenant_id
        assert retrieved_data['nonce'] == nonce
        assert retrieved_data['redirect_uri'] == redirect_uri
        assert 'created_at' in retrieved_data
        assert 'expires_at' in retrieved_data
    
    @pytest.mark.asyncio
    async def test_retrieve_nonexistent_state(self, state_manager):
        """Test retrieving non-existent state."""
        # Act
        result = await state_manager.retrieve_state("nonexistent-state")
        
        # Assert
        assert result is None
    
    @pytest.mark.asyncio
    async def test_state_one_time_use(self, state_manager):
        """Test that state can only be retrieved once."""
        # Arrange
        state = "one-time-state"
        
        await state_manager.store_state(
            state=state,
            realm_name="test-realm",
            tenant_id="test-tenant",
            nonce="test-nonce"
        )
        
        # Act
        first_retrieval = await state_manager.retrieve_state(state)
        second_retrieval = await state_manager.retrieve_state(state)
        
        # Assert
        assert first_retrieval is not None
        assert second_retrieval is None
    
    @pytest.mark.asyncio
    async def test_state_expiration(self):
        """Test state expiration functionality."""
        # Arrange - Use very short TTL for testing
        state_manager = SessionStateManager(max_size=5, ttl_minutes=0.01)  # ~0.6 seconds
        state = "expiring-state"
        
        await state_manager.store_state(
            state=state,
            realm_name="test-realm",
            tenant_id="test-tenant",
            nonce="test-nonce"
        )
        
        # Act - Wait for expiration
        await asyncio.sleep(1)
        result = await state_manager.retrieve_state(state)
        
        # Assert
        assert result is None
    
    @pytest.mark.asyncio
    async def test_cache_size_limit(self, state_manager):
        """Test cache size limit enforcement."""
        # Arrange - Store more states than max_size (5)
        states = [f"state-{i}" for i in range(7)]
        
        # Act
        for i, state in enumerate(states):
            await state_manager.store_state(
                state=state,
                realm_name=f"realm-{i}",
                tenant_id=f"tenant-{i}",
                nonce=f"nonce-{i}"
            )
        
        # Assert - Only the last 5 states should be present
        cache_stats = await state_manager.get_cache_stats()
        assert cache_stats['total_entries'] == 5
        
        # First two states should be evicted
        assert await state_manager.retrieve_state("state-0") is None
        assert await state_manager.retrieve_state("state-1") is None
        
        # Last states should still be present
        assert await state_manager.retrieve_state("state-6") is not None
    
    @pytest.mark.asyncio
    async def test_cleanup_expired_entries(self):
        """Test automatic cleanup of expired entries."""
        # Arrange
        state_manager = SessionStateManager(max_size=10, ttl_minutes=0.01)
        
        # Store some states
        for i in range(3):
            await state_manager.store_state(
                state=f"state-{i}",
                realm_name=f"realm-{i}",
                tenant_id=f"tenant-{i}",
                nonce=f"nonce-{i}"
            )
        
        # Wait for expiration
        await asyncio.sleep(1)
        
        # Store a new state (this should trigger cleanup)
        await state_manager.store_state(
            state="new-state",
            realm_name="new-realm",
            tenant_id="new-tenant",
            nonce="new-nonce"
        )
        
        # Act
        cache_stats = await state_manager.get_cache_stats()
        
        # Assert - Only the new state should remain
        assert cache_stats['total_entries'] == 1
    
    @pytest.mark.asyncio
    async def test_get_cache_stats(self, state_manager):
        """Test cache statistics functionality."""
        # Arrange
        await state_manager.store_state(
            state="test-state",
            realm_name="test-realm",
            tenant_id="test-tenant",
            nonce="test-nonce"
        )
        
        # Act
        stats = await state_manager.get_cache_stats()
        
        # Assert
        assert 'total_entries' in stats
        assert 'max_size' in stats
        assert 'ttl_minutes' in stats
        assert 'oldest_entry_age_seconds' in stats
        assert 'newest_entry_age_seconds' in stats
        
        assert stats['total_entries'] == 1
        assert stats['max_size'] == 5
        assert stats['ttl_minutes'] == 1
        assert isinstance(stats['oldest_entry_age_seconds'], int)
        assert isinstance(stats['newest_entry_age_seconds'], int)
    
    @pytest.mark.asyncio
    async def test_clear_all(self, state_manager):
        """Test clearing all state entries."""
        # Arrange
        for i in range(3):
            await state_manager.store_state(
                state=f"state-{i}",
                realm_name=f"realm-{i}",
                tenant_id=f"tenant-{i}",
                nonce=f"nonce-{i}"
            )
        
        # Act
        cleared_count = await state_manager.clear_all()
        
        # Assert
        assert cleared_count == 3
        
        cache_stats = await state_manager.get_cache_stats()
        assert cache_stats['total_entries'] == 0
    
    @pytest.mark.asyncio
    async def test_concurrent_operations(self, state_manager):
        """Test thread safety with concurrent operations."""
        # Arrange
        async def store_states(start_index, count):
            for i in range(start_index, start_index + count):
                await state_manager.store_state(
                    state=f"concurrent-state-{i}",
                    realm_name=f"realm-{i}",
                    tenant_id=f"tenant-{i}",
                    nonce=f"nonce-{i}"
                )
        
        async def retrieve_states(start_index, count):
            results = []
            for i in range(start_index, start_index + count):
                result = await state_manager.retrieve_state(f"concurrent-state-{i}")
                results.append(result)
            return results
        
        # Act - Run concurrent store and retrieve operations
        store_tasks = [
            store_states(0, 3),
            store_states(3, 3),
            store_states(6, 3)
        ]
        
        await asyncio.gather(*store_tasks)
        
        # Retrieve some states concurrently
        retrieve_tasks = [
            retrieve_states(0, 3),
            retrieve_states(3, 3)
        ]
        
        results = await asyncio.gather(*retrieve_tasks)
        
        # Assert
        # Some states should be retrieved successfully
        # (exact count depends on cache size limit and timing)
        retrieved_states = [item for sublist in results for item in sublist if item is not None]
        assert len(retrieved_states) > 0
        
        # Verify data integrity
        for state_data in retrieved_states:
            assert 'realm_name' in state_data
            assert 'tenant_id' in state_data
            assert 'nonce' in state_data
    
    @pytest.mark.asyncio
    async def test_store_state_with_optional_redirect_uri(self, state_manager):
        """Test storing state without redirect URI."""
        # Arrange
        state = "state-no-redirect"
        
        # Act
        await state_manager.store_state(
            state=state,
            realm_name="test-realm",
            tenant_id="test-tenant",
            nonce="test-nonce"
            # No redirect_uri provided
        )
        
        retrieved_data = await state_manager.retrieve_state(state)
        
        # Assert
        assert retrieved_data is not None
        assert retrieved_data['redirect_uri'] is None
    
    @pytest.mark.asyncio
    async def test_error_handling_in_stats(self):
        """Test error handling in cache statistics."""
        # Arrange
        state_manager = SessionStateManager()
        
        # Simulate an error by corrupting internal state
        original_cache = state_manager._cache
        state_manager._cache = None
        
        # Act
        stats = await state_manager.get_cache_stats()
        
        # Assert
        assert 'error' in stats
        
        # Restore for cleanup
        state_manager._cache = original_cache
    
    @pytest.mark.asyncio
    async def test_performance_with_large_cache(self):
        """Test performance with larger cache size."""
        # Arrange
        large_state_manager = SessionStateManager(max_size=1000, ttl_minutes=5)
        
        # Act - Store many states
        start_time = asyncio.get_event_loop().time()
        
        for i in range(100):
            await large_state_manager.store_state(
                state=f"perf-state-{i}",
                realm_name=f"realm-{i}",
                tenant_id=f"tenant-{i}",
                nonce=f"nonce-{i}"
            )
        
        store_time = asyncio.get_event_loop().time() - start_time
        
        # Retrieve states
        start_time = asyncio.get_event_loop().time()
        
        for i in range(50):
            await large_state_manager.retrieve_state(f"perf-state-{i}")
        
        retrieve_time = asyncio.get_event_loop().time() - start_time
        
        # Assert - Operations should complete in reasonable time
        assert store_time < 1.0  # Should complete in less than 1 second
        assert retrieve_time < 0.5  # Should complete in less than 0.5 seconds
        
        # Verify final state
        stats = await large_state_manager.get_cache_stats()
        assert stats['total_entries'] == 50  # 50 remaining after retrieval
