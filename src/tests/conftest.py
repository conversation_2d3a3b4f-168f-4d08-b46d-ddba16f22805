"""
Pytest configuration and shared fixtures.

This file provides robust async test infrastructure that properly handles
database connections and transactions to avoid connection pool conflicts.
"""

import pytest

# Import fixtures from test_config to make them available to all tests
from src.tests.test_config import (
    setup_test_database,
    db_session,
    async_client,
    setup_test_dependencies,
)


@pytest.fixture
def sample_tenant_data():
    """Sample tenant data for testing (updated for organization_name field)."""
    return {
        "organization_name": "Test Tenant",
        "industry": "Technology",
        "region_preference": "US_EAST",
        "subscription_level": "ENTERPRISE"
    }


@pytest.fixture
def sample_user_data():
    """Sample user data for testing."""
    import time
    unique_email = f"test{int(time.time())}@example.com"
    return {
        "email": unique_email,
        "first_name": "Test",
        "last_name": "User",
        "role": "DEVELOPER"
        # Note: tenant_id is hardcoded in service, not in request schema
    }


@pytest.fixture
def sample_project_data():
    """Sample project data for testing."""
    return {
        "name": "Test Project",
        "description": "A test project for API testing"
        # Removed tenant_id - not allowed in request schema
    }


@pytest.fixture
def sample_policy_data():
    """Sample policy data for testing."""
    return {
        "name": "Test Policy",
        "version": 1,
        "status": "DRAFT",
        "yaml_blob": "dGVzdDogcG9saWN5"
    }


@pytest.fixture
def sample_rule_data():
    """Sample rule data for testing."""
    return {
        "rule_id": "test-rule-001",
        "name": "Test Rule",
        "policy_id": 1
    }


@pytest.fixture
def sample_notification_data():
    """Sample notification data for testing."""
    return {
        "event_type": "VIOLATION",
        "details": "Test notification details",
        "sent_to": "<EMAIL>"
    }


@pytest.fixture
def sample_api_key_data():
    """Sample API key data for testing."""
    return {
        "hashed_token": "test_hashed_token_12345",
        "label": "Test API Key",
        "tenant_id": 1
    }


@pytest.fixture
def sample_provider_config_data():
    """Sample provider config data for testing."""
    return {
        "provider_type": "OPENAI",
        "credentials": "test-credentials",
        "default_model": "gpt-4"
    }


@pytest.fixture
def sample_log_entry_data():
    """Sample log entry data for testing."""
    return {
        "request_id": "test_req_12345",
        "tenant_id": 1,
        "model": "gpt-4-turbo",
        "prompt_text": "Test prompt for API testing",
        "outcome": "ALLOWED",
        "provider_latency_ms": 100,
        "prompt_tokens": 10,
        "completion_tokens": 20
    }


# Additional fixtures demonstrating new enum values

@pytest.fixture
def sample_support_user_data():
    """Sample support agent user data for testing new role."""
    import time
    unique_email = f"support{int(time.time())}@example.com"
    return {
        "email": unique_email,
        "first_name": "Support",
        "last_name": "Agent",
        "role": "SUPPORT_AGENT"
    }


@pytest.fixture
def sample_tenant_signup_data():
    """Sample tenant signup data for testing the new secure authentication flow."""
    import time
    import random
    unique_id = int(time.time() * 1000) + random.randint(1000, 9999)  # More unique ID
    return {
        "organization_name": f"Test Organization {unique_id}",
        "industry": "Technology",
        "region_preference": "US_EAST",
        "subscription_level": "ENTERPRISE",
        "admin_email": f"admin{unique_id}@example.com",
        "admin_password": "SecurePassword123!",
        "admin_first_name": "Test",
        "admin_last_name": "Admin"
    }


@pytest.fixture
def sample_archived_policy_data():
    """Sample archived policy data for testing new status."""
    return {
        "name": "Archived Test Policy",
        "version": 1,
        "status": "ARCHIVED",
        "yaml_blob": "dGVzdDogYXJjaGl2ZWQ="
    }


@pytest.fixture
def sample_expired_api_key_data():
    """Sample expired API key data for testing new status."""
    return {
        "hashed_token": "expired_test_token_12345",
        "label": "Expired Test API Key",
        "status": "EXPIRED",
        "tenant_id": 1
    }


@pytest.fixture
def sample_usage_notification_data():
    """Sample usage threshold notification for testing new type."""
    return {
        "event_type": "USAGE_THRESHOLD",
        "details": "API usage threshold exceeded",
        "sent_to": "<EMAIL>"
    }


@pytest.fixture
def sample_redacted_log_entry_data():
    """Sample redacted log entry for testing new outcome."""
    return {
        "request_id": "test_redacted_req_12345",
        "tenant_id": 1,
        "model": "gpt-4",
        "prompt_text": "Sensitive content request",
        "outcome": "REDACTED",
        "provider_latency_ms": 120,
        "prompt_tokens": 15,
        "completion_tokens": 25,
        "masked_response": "Content has been [REDACTED] for privacy."
    }


@pytest.fixture
def sample_google_ai_provider_data():
    """Sample Google AI provider config for testing new provider type."""
    return {
        "provider_type": "GOOGLE_AI",
        "credentials": "google-ai-test-key",
        "default_model": "gemini-pro"
    }


@pytest.fixture
def sample_self_hosted_provider_data():
    """Sample self-hosted provider config for testing new provider type."""
    return {
        "provider_type": "SELF_HOSTED",
        "credentials": "http://localhost:8080/api",
        "default_model": "llama-2-7b"
    }


@pytest.fixture
def sample_keycloak_provision_data():
    """Sample Keycloak provisioning data for testing."""
    import time
    unique_id = int(time.time())
    return {
        "tenant_id": "1",  # Use existing seeded tenant ID
        "organization_name": f"Test Organization {unique_id}",
        "admin_username": f"admin{unique_id}",
        "admin_email": f"admin{unique_id}@example.com",
        "admin_password": "SecurePassword123!",
        "admin_first_name": "Test",
        "admin_last_name": "Admin"
    }


@pytest.fixture
def mock_keycloak_auth():
    """Mock Keycloak authentication for tests that require it."""
    from src.app.core.model.user_context import UserContext
    from src.app.core.service.user_context_service import get_current_user
    from src.app.core.constant.constants import UserRole
    from src.main import app

    mock_user = UserContext(
        user_id="test-user-id-123",
        username="testuser",
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        realm="tenant-1",
        tenant_id="1",
        roles=[
            UserRole.TENANT_ADMIN.value,
            UserRole.DEVELOPER.value,
            UserRole.POLICY_EDITOR.value,
            UserRole.AUDITOR.value
        ],
        is_active=True
    )

    # Create a mock dependency function
    async def mock_get_current_user():
        return mock_user

    # Override the dependency in the FastAPI app
    app.dependency_overrides[get_current_user] = mock_get_current_user

    try:
        yield mock_user
    finally:
        # Clean up the override
        if get_current_user in app.dependency_overrides:
            del app.dependency_overrides[get_current_user]
