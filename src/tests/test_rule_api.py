"""
Test suite for Rule CRUD operations.

This file was auto-generated by test_generator.py
"""

# Apply patches BEFORE any other imports
from unittest.mock import patch, AsyncMock, MagicMock
from functools import wraps

# Mock the require_roles decorator to bypass authentication (though rule router doesn't use it)
def mock_require_roles(*roles):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Simply pass through - no authentication required in tests
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# Apply patches immediately
patch('src.app.core.security.keycloak_auth.require_roles', mock_require_roles).start()

import pytest

# Mock the Rule service to avoid database calls
class MockRuleService:
    # Class variable to track deleted IDs across all instances
    deleted_ids = set()

    def __init__(self, session=None):
        self.session = session

    async def create_rule(self, payload):
        from src.app.api.schema.rule.rule_create_response import RuleCreateResponse

        return RuleCreateResponse(
            id=1,
            rule_id=payload.rule_id,
            name=payload.name,
            policy_id=payload.policy_id,
            tenant_id=1
        )

    async def get_rule_by_id(self, rule_id):
        from src.app.api.schema.rule.rule_read_response import RuleReadResponse

        if rule_id == 99999 or rule_id in MockRuleService.deleted_ids:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"Rule with id {rule_id} not found.")

        return RuleReadResponse(
            id=rule_id,
            rule_id="test-rule-001",
            name="Test Rule",
            policy_id=1,
            tenant_id=1
        )

    async def get_rules(self):
        return []

    async def update_rule(self, rule_id, payload):
        from src.app.api.schema.rule.rule_update_response import RuleUpdateResponse

        if rule_id == 99999 or rule_id in MockRuleService.deleted_ids:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"Rule with id {rule_id} not found.")

        return RuleUpdateResponse(
            id=rule_id,
            rule_id=payload.rule_id or "test-rule-001",
            name=payload.name or "Test Rule",
            policy_id=payload.policy_id or 1,
            tenant_id=1
        )

    async def delete_rule(self, rule_id):
        if rule_id == 99999:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"Rule with id {rule_id} not found.")

        # Mark the rule as deleted
        MockRuleService.deleted_ids.add(rule_id)
        return None

# Patch the service before importing the router
patch('src.app.api.service.rule_service.RuleService', MockRuleService).start()


class TestRuleCRUD:
    """Test suite for Rule CRUD operations."""

    @pytest.mark.asyncio
    async def test_create_rule_success(self, async_client, sample_rule_data):
        """Test successful rule creation."""
        response = await async_client.post("/rules", json=sample_rule_data)

        assert response.status_code == 201
        data = response.json()

        assert "id" in data
        for key, value in sample_rule_data.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_create_rule_invalid_data(self, async_client):
        """Test rule creation with invalid data."""
        payload = {"invalid_field": "invalid_value"}

        response = await async_client.post("/rules", json=payload)

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_rule_success(self, async_client, sample_rule_data):
        """Test successful rule retrieval."""
        create_payload = {**sample_rule_data, "name": "Test Rule Get"}
        create_response = await async_client.post("/rules", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.get(f"/rules/{created_id}")

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == created_id

    @pytest.mark.asyncio
    async def test_get_rule_not_found(self, async_client):
        """Test rule retrieval with non-existent ID."""
        response = await async_client.get(f"/rules/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_rule_success(self, async_client, sample_rule_data):
        """Test successful rule update."""
        create_payload = {**sample_rule_data, "name": "Test Rule Update"}
        create_response = await async_client.post("/rules", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        update_payload = {"name": "Updated Test Rule"}
        response = await async_client.put(f"/rules/{created_id}", json=update_payload)

        assert response.status_code == 200
        data = response.json()

        for key, value in update_payload.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_update_rule_not_found(self, async_client):
        """Test rule update with non-existent ID."""
        update_payload = {"name": "Updated Test Rule"}
        response = await async_client.put(f"/rules/99999", json=update_payload)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_rule_success(self, async_client, sample_rule_data):
        """Test successful rule deletion."""
        create_payload = {**sample_rule_data, "name": "Test Rule Delete"}
        create_response = await async_client.post("/rules", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.delete(f"/rules/{created_id}")

        assert response.status_code == 204

        get_response = await async_client.get(f"/rules/{created_id}")
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_rule_not_found(self, async_client):
        """Test rule deletion with non-existent ID."""
        response = await async_client.delete(f"/rules/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_rules_success(self, async_client):
        """Test successful rule listing."""
        response = await async_client.get("/rules")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
