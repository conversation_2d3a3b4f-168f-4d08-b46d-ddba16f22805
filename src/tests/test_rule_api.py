"""
Test suite for Rule CRUD operations.

This file was auto-generated by test_generator.py
"""

import pytest


class TestRuleCRUD:
    """Test suite for Rule CRUD operations."""

    @pytest.mark.asyncio
    async def test_create_rule_success(self, async_client, sample_rule_data):
        """Test successful rule creation."""
        response = await async_client.post("/rules", json=sample_rule_data)

        assert response.status_code == 201
        data = response.json()

        assert "id" in data
        for key, value in sample_rule_data.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_create_rule_invalid_data(self, async_client):
        """Test rule creation with invalid data."""
        payload = {"invalid_field": "invalid_value"}

        response = await async_client.post("/rules", json=payload)

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_rule_success(self, async_client, sample_rule_data):
        """Test successful rule retrieval."""
        create_payload = {**sample_rule_data, "name": "Test Rule Get"}
        create_response = await async_client.post("/rules", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.get(f"/rules/{created_id}")

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == created_id

    @pytest.mark.asyncio
    async def test_get_rule_not_found(self, async_client):
        """Test rule retrieval with non-existent ID."""
        response = await async_client.get(f"/rules/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_rule_success(self, async_client, sample_rule_data):
        """Test successful rule update."""
        create_payload = {**sample_rule_data, "name": "Test Rule Update"}
        create_response = await async_client.post("/rules", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        update_payload = {"name": "Updated Test Rule"}
        response = await async_client.put(f"/rules/{created_id}", json=update_payload)

        assert response.status_code == 200
        data = response.json()

        for key, value in update_payload.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_update_rule_not_found(self, async_client):
        """Test rule update with non-existent ID."""
        update_payload = {"name": "Updated Test Rule"}
        response = await async_client.put(f"/rules/99999", json=update_payload)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_rule_success(self, async_client, sample_rule_data):
        """Test successful rule deletion."""
        create_payload = {**sample_rule_data, "name": "Test Rule Delete"}
        create_response = await async_client.post("/rules", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.delete(f"/rules/{created_id}")

        assert response.status_code == 204

        get_response = await async_client.get(f"/rules/{created_id}")
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_rule_not_found(self, async_client):
        """Test rule deletion with non-existent ID."""
        response = await async_client.delete(f"/rules/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_rules_success(self, async_client):
        """Test successful rule listing."""
        response = await async_client.get("/rules")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
