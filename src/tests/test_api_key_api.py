"""
Test suite for ApiKey CRUD operations.

This file was auto-generated by test_generator.py
"""

import pytest
from unittest.mock import patch

# Mock the require_roles decorator before any imports
def mock_require_roles(*roles):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Inject a mock user context if none exists
            from src.app.core.model.user_context import User<PERSON>ontext
            from src.app.core.constant.constants import UserRole

            has_user_context = any(isinstance(value, UserContext) for value in kwargs.values())
            if not has_user_context:
                mock_user = UserContext(
                    user_id="test-user-id-123",
                    username="testuser",
                    email="<EMAIL>",
                    first_name="Test",
                    last_name="User",
                    realm="tenant-1",
                    tenant_id="1",
                    roles=[
                        UserRole.TENANT_ADMIN.value,
                        UserRole.DEVELOPER.value,
                        UserRole.POLICY_EDITOR.value,
                        UserRole.AUDITOR.value
                    ],
                    is_active=True
                )
                kwargs['current_user'] = mock_user

            return await func(*args, **kwargs)
        return wrapper
    return decorator

from functools import wraps

# Patch the decorator before importing the router
patch('src.app.core.security.keycloak_auth.require_roles', mock_require_roles).start()
patch('src.app.api.router.api_key_router.require_roles', mock_require_roles).start()


class TestApiKeyCRUD:
    """Test suite for ApiKey CRUD operations."""

    @pytest.mark.asyncio
    async def test_create_api_key_success(self, async_client, sample_api_key_data, mock_keycloak_auth):
        """Test successful api_key creation."""
        import uuid
        unique_data = sample_api_key_data.copy()
        unique_data['hashed_token'] = f"test_hashed_token_{uuid.uuid4().hex[:8]}"

        response = await async_client.post("/api-keys", json=unique_data)

        assert response.status_code == 201
        data = response.json()

        assert "id" in data
        for key, value in unique_data.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_create_api_key_invalid_data(self, async_client, mock_keycloak_auth):
        """Test api_key creation with invalid data."""
        payload = {"invalid_field": "invalid_value"}

        response = await async_client.post("/api-keys", json=payload)

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_api_key_success(self, async_client, sample_api_key_data, mock_keycloak_auth):
        """Test successful api_key retrieval."""
        import uuid
        create_payload = sample_api_key_data.copy()
        create_payload['hashed_token'] = f"test_hashed_token_{uuid.uuid4().hex[:8]}"
        create_payload['label'] = "Test ApiKey Get"

        create_response = await async_client.post("/api-keys", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.get(f"/api-keys/{created_id}")

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == created_id

    @pytest.mark.asyncio
    async def test_get_api_key_not_found(self, async_client):
        """Test api_key retrieval with non-existent ID."""
        response = await async_client.get(f"/api-keys/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_api_key_success(self, async_client, sample_api_key_data, mock_keycloak_auth):
        """Test successful api_key update."""
        import uuid
        create_payload = sample_api_key_data.copy()
        create_payload['hashed_token'] = f"test_hashed_token_{uuid.uuid4().hex[:8]}"
        create_payload['label'] = "Test ApiKey Update"

        create_response = await async_client.post("/api-keys", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        update_payload = {
            "hashed_token": f"updated_hashed_token_{uuid.uuid4().hex[:8]}",
            "label": "Updated Test API Key",
            "tenant_id": 1
        }
        response = await async_client.put(f"/api-keys/{created_id}", json=update_payload)

        assert response.status_code == 200
        data = response.json()

        for key, value in update_payload.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_update_api_key_not_found(self, async_client):
        """Test api_key update with non-existent ID."""
        update_payload = {
            "hashed_token": "updated_test_token_99999",
            "label": "Updated Test API Key",
            "tenant_id": 1
        }
        response = await async_client.put(f"/api-keys/99999", json=update_payload)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_api_key_success(self, async_client, sample_api_key_data, mock_keycloak_auth):
        """Test successful api_key deletion."""
        import uuid
        create_payload = sample_api_key_data.copy()
        create_payload['hashed_token'] = f"test_hashed_token_{uuid.uuid4().hex[:8]}"
        create_payload['label'] = "Test ApiKey Delete"

        create_response = await async_client.post("/api-keys", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.delete(f"/api-keys/{created_id}")

        assert response.status_code == 204

        get_response = await async_client.get(f"/api-keys/{created_id}")
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_api_key_not_found(self, async_client):
        """Test api_key deletion with non-existent ID."""
        response = await async_client.delete(f"/api-keys/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_api_keys_success(self, async_client, mock_keycloak_auth):
        """Test successful api_key listing."""
        response = await async_client.get("/api-keys")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
