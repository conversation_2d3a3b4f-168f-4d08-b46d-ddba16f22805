"""
Authentication utilities for testing.

This module provides utilities for mocking Keycloak authentication in tests,
allowing tests to run without requiring a real Keycloak server.
"""

from unittest.mock import Mock, patch
from typing import List, Optional, Dict, Any
from contextlib import asynccontextmanager

from src.app.core.model.user_context import UserContext


"""
Create a mock UserContext for testing.

Args:
    user_id: User ID
    username: Username
    email: User email
    first_name: User first name
    last_name: User last name
    realm: Keycloak realm name
    tenant_id: Tenant ID
    roles: List of user roles
    is_active: Whether user is active

Returns:
    UserContext: Mock user context
"""
def create_mock_user(
    user_id: str = "test-user-123",
    username: str = "testuser",
    email: str = "<EMAIL>",
    first_name: str = "Test",
    last_name: str = "User",
    realm: str = "tenant-1",
    tenant_id: str = "1",
    roles: List[str] = None,
    is_active: bool = True
) -> UserContext:
    if roles is None:
        roles = ["tenant_admin"]
    
    return UserContext(
        user_id=user_id,
        username=username,
        email=email,
        first_name=first_name,
        last_name=last_name,
        realm=realm,
        tenant_id=tenant_id,
        roles=roles,
        is_active=is_active
    )


"""Create a mock admin user for testing."""
def create_admin_user(tenant_id: str = "1") -> UserContext:
    return create_mock_user(
        user_id="admin-123",
        username="admin",
        email="<EMAIL>",
        first_name="Admin",
        last_name="User",
        tenant_id=tenant_id,
        roles=["tenant_admin"]
    )


"""Create a mock developer user for testing."""
def create_developer_user(tenant_id: str = "1") -> UserContext:
    return create_mock_user(
        user_id="dev-123",
        username="developer",
        email="<EMAIL>",
        first_name="Developer",
        last_name="User",
        tenant_id=tenant_id,
        roles=["developer"]
    )


"""Create a mock auditor user for testing."""
def create_auditor_user(tenant_id: str = "1") -> UserContext:
    return create_mock_user(
        user_id="auditor-123",
        username="auditor",
        email="<EMAIL>",
        first_name="Auditor",
        last_name="User",
        tenant_id=tenant_id,
        roles=["auditor"]
    )


"""
Context manager to mock authentication for a specific user.

Usage:
    async with mock_authentication(create_admin_user()):
        response = await async_client.get("/protected-endpoint", headers={"Authorization": "Bearer fake-token"})
"""
@asynccontextmanager
async def mock_authentication(user: UserContext):
    with patch('src.app.core.security.keycloak_auth.keycloak_auth.extract_user_context') as mock_extract:
        mock_extract.return_value = user
        yield


"""
Context manager to mock optional authentication.

Usage:
    async with mock_optional_authentication(create_admin_user()):
        response = await async_client.get("/optional-auth-endpoint")
"""
@asynccontextmanager
async def mock_optional_authentication(user: Optional[UserContext] = None):
    with patch('src.app.core.security.keycloak_auth.get_optional_user') as mock_auth:
        mock_auth.return_value = user
        yield


"""
Create mock authentication headers for API requests.

Args:
    token: JWT token to use in Authorization header

Returns:
    Dict containing Authorization header
"""
def mock_auth_headers(token: str = "fake-jwt-token") -> Dict[str, str]:
    return {"Authorization": f"Bearer {token}"}


"""
Mixin class for test classes that need authentication utilities.

Usage:
    class TestMyAPI(AuthTestMixin):
        async def test_protected_endpoint(self, async_client):
            async with self.mock_admin_auth():
                response = await async_client.get("/protected")
                assert response.status_code == 200
"""
class AuthTestMixin:
    
    """Mock admin authentication."""
    @asynccontextmanager
    async def mock_admin_auth(self, tenant_id: str = "1"):
        async with mock_authentication(create_admin_user(tenant_id)):
            yield
    
    """Mock developer authentication."""
    @asynccontextmanager
    async def mock_developer_auth(self, tenant_id: str = "1"):
        async with mock_authentication(create_developer_user(tenant_id)):
            yield
    
    """Mock auditor authentication."""
    @asynccontextmanager
    async def mock_auditor_auth(self, tenant_id: str = "1"):
        async with mock_authentication(create_auditor_user(tenant_id)):
            yield
    
    """Mock no authentication (unauthenticated request)."""
    @asynccontextmanager
    async def mock_no_auth(self):
        with patch('src.app.core.security.keycloak_auth.keycloak_auth.extract_user_context') as mock_extract:
            from src.app.core.security.keycloak_auth import KeycloakAuthError
            mock_extract.side_effect = KeycloakAuthError("Authentication required")
            yield
    
    """Get authentication headers for requests."""
    def get_auth_headers(self, token: str = "fake-jwt-token") -> Dict[str, str]:
        return mock_auth_headers(token)


"""Make an authenticated request as admin user."""
async def with_admin_auth(async_client, method: str, url: str, **kwargs):
    if 'headers' not in kwargs:
        kwargs['headers'] = {}
    kwargs['headers'].update(mock_auth_headers())

    async with mock_authentication(create_admin_user()):
        if method.upper() == "GET":
            return await async_client.get(url, **kwargs)
        elif method.upper() == "POST":
            return await async_client.post(url, **kwargs)
        elif method.upper() == "PUT":
            return await async_client.put(url, **kwargs)
        elif method.upper() == "DELETE":
            return await async_client.delete(url, **kwargs)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")


"""Make an authenticated request as developer user."""
async def with_developer_auth(async_client, method: str, url: str, **kwargs):
    async with mock_authentication(create_developer_user()):
        if method.upper() == "GET":
            return await async_client.get(url, **kwargs)
        elif method.upper() == "POST":
            return await async_client.post(url, **kwargs)
        elif method.upper() == "PUT":
            return await async_client.put(url, **kwargs)
        elif method.upper() == "DELETE":
            return await async_client.delete(url, **kwargs)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")


"""Make an unauthenticated request."""
async def with_no_auth(async_client, method: str, url: str, **kwargs):
    with patch('src.app.core.security.keycloak_auth.get_current_user') as mock_auth:
        from src.app.core.security.keycloak_auth import KeycloakAuthError
        mock_auth.side_effect = KeycloakAuthError("Authentication required")
        
        if method.upper() == "GET":
            return await async_client.get(url, **kwargs)
        elif method.upper() == "POST":
            return await async_client.post(url, **kwargs)
        elif method.upper() == "PUT":
            return await async_client.put(url, **kwargs)
        elif method.upper() == "DELETE":
            return await async_client.delete(url, **kwargs)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
