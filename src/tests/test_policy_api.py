"""
Test suite for Policy CRUD operations.

This file was auto-generated by test_generator.py
"""

import pytest


class TestPolicyCRUD:
    """Test suite for Policy CRUD operations."""

    @pytest.mark.asyncio
    async def test_create_policy_success(self, async_client, sample_policy_data, mock_keycloak_auth):
        """Test successful policy creation."""
        response = await async_client.post("/policies", json=sample_policy_data)

        assert response.status_code == 201
        data = response.json()

        assert "id" in data
        for key, value in sample_policy_data.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_create_policy_invalid_data(self, async_client, mock_keycloak_auth):
        """Test policy creation with invalid data."""
        payload = {"invalid_field": "invalid_value"}

        response = await async_client.post("/policies", json=payload)

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_policy_success(self, async_client, sample_policy_data, mock_keycloak_auth):
        """Test successful policy retrieval."""
        create_payload = {**sample_policy_data, "name": "Test Policy Get"}
        create_response = await async_client.post("/policies", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.get(f"/policies/{created_id}")

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == created_id

    @pytest.mark.asyncio
    async def test_get_policy_not_found(self, async_client):
        """Test policy retrieval with non-existent ID."""
        response = await async_client.get(f"/policies/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_policy_success(self, async_client, sample_policy_data, mock_keycloak_auth):
        """Test successful policy update."""
        create_payload = {**sample_policy_data, "name": "Test Policy Update"}
        create_response = await async_client.post("/policies", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        update_payload = {"name": "Updated Test Policy", "version": 2}
        response = await async_client.put(f"/policies/{created_id}", json=update_payload)

        assert response.status_code == 200
        data = response.json()

        for key, value in update_payload.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_update_policy_not_found(self, async_client, mock_keycloak_auth):
        """Test policy update with non-existent ID."""
        update_payload = {"name": "Updated Test Policy", "version": 2}
        response = await async_client.put(f"/policies/99999", json=update_payload)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_policy_success(self, async_client, sample_policy_data, mock_keycloak_auth):
        """Test successful policy deletion."""
        create_payload = {**sample_policy_data, "name": "Test Policy Delete"}
        create_response = await async_client.post("/policies", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.delete(f"/policies/{created_id}")

        assert response.status_code == 204

        get_response = await async_client.get(f"/policies/{created_id}")
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_policy_not_found(self, async_client):
        """Test policy deletion with non-existent ID."""
        response = await async_client.delete(f"/policies/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_policys_success(self, async_client):
        """Test successful policy listing."""
        response = await async_client.get("/policies")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
