"""
Test suite for Policy CRUD operations.

This file was auto-generated by test_generator.py
"""

# Apply patches BEFORE any other imports
from unittest.mock import patch, AsyncMock, MagicMock
from functools import wraps

# Mock the require_roles decorator to bypass authentication
def mock_require_roles(*roles):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Simply pass through - no authentication required in tests
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# Apply patches immediately
patch('src.app.core.security.keycloak_auth.require_roles', mock_require_roles).start()

import pytest

# Mock the Policy service to avoid database calls
class MockPolicyService:
    # Class variable to track deleted IDs across all instances
    deleted_ids = set()

    def __init__(self, session=None):
        self.session = session

    async def create_policy(self, payload, current_user):
        from src.app.api.schema.policy.policy_create_response import PolicyCreateResponse
        from src.app.dao.entity.policy_status import PolicyStatus
        from datetime import datetime, timezone

        now = datetime.now(timezone.utc)
        return PolicyCreateResponse(
            id=1,
            name=payload.name,
            version=payload.version,
            status=payload.status,
            yaml_blob=payload.yaml_blob,
            tenant_id=1,
            project_id=payload.project_id,
            last_edited_by_id=payload.last_edited_by_id,
            created_at=now,
            updated_at=now
        )

    async def get_policy_by_id(self, policy_id):
        from src.app.api.schema.policy.policy_read_response import PolicyReadResponse
        from src.app.dao.entity.policy_status import PolicyStatus
        from datetime import datetime, timezone

        if policy_id == 99999 or policy_id in MockPolicyService.deleted_ids:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"Policy with id {policy_id} not found.")

        now = datetime.now(timezone.utc)
        return PolicyReadResponse(
            id=policy_id,
            name="Test Policy",
            version=1,
            status=PolicyStatus.DRAFT,
            yaml_blob=b"dGVzdDogcG9saWN5",
            tenant_id=1,
            project_id=None,
            last_edited_by_id=None,
            created_at=now,
            updated_at=now
        )

    async def get_policies(self):
        return []

    async def update_policy(self, policy_id, payload):
        from src.app.api.schema.policy.policy_update_response import PolicyUpdateResponse
        from src.app.dao.entity.policy_status import PolicyStatus
        from datetime import datetime, timezone

        if policy_id == 99999 or policy_id in MockPolicyService.deleted_ids:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"Policy with id {policy_id} not found.")

        now = datetime.now(timezone.utc)
        return PolicyUpdateResponse(
            id=policy_id,
            name=payload.name or "Test Policy",
            version=payload.version or 1,
            status=payload.status or PolicyStatus.DRAFT,
            yaml_blob=payload.yaml_blob or b"dGVzdDogcG9saWN5",
            tenant_id=1,
            project_id=payload.project_id,
            last_edited_by_id=payload.last_edited_by_id,
            created_at=now,
            updated_at=now
        )

    async def delete_policy(self, policy_id):
        if policy_id == 99999:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"Policy with id {policy_id} not found.")

        # Mark the policy as deleted
        MockPolicyService.deleted_ids.add(policy_id)
        return None

# Patch the service before importing the router
patch('src.app.api.service.policy_service.PolicyService', MockPolicyService).start()


class TestPolicyCRUD:
    """Test suite for Policy CRUD operations."""

    @pytest.mark.asyncio
    async def test_create_policy_success(self, async_client, sample_policy_data, mock_keycloak_auth):
        """Test successful policy creation."""
        response = await async_client.post("/policies", json=sample_policy_data)

        assert response.status_code == 201
        data = response.json()

        assert "id" in data
        for key, value in sample_policy_data.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_create_policy_invalid_data(self, async_client, mock_keycloak_auth):
        """Test policy creation with invalid data."""
        payload = {"invalid_field": "invalid_value"}

        response = await async_client.post("/policies", json=payload)

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_policy_success(self, async_client, sample_policy_data, mock_keycloak_auth):
        """Test policy retrieval - works because no @require_roles decorator."""
        # Skip creation since it requires authentication
        # Test retrieval directly with a known ID
        response = await async_client.get(f"/policies/1")

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == 1

    @pytest.mark.asyncio
    async def test_get_policy_not_found(self, async_client):
        """Test policy retrieval with non-existent ID."""
        response = await async_client.get(f"/policies/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_policy_success(self, async_client, sample_policy_data, mock_keycloak_auth):
        """Test policy update - works because no @require_roles decorator."""
        # Skip creation since it requires authentication
        # Test update directly with a known ID
        update_payload = {"name": "Updated Test Policy", "version": 2}
        response = await async_client.put(f"/policies/1", json=update_payload)

        assert response.status_code == 200
        data = response.json()

        for key, value in update_payload.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_update_policy_not_found(self, async_client, mock_keycloak_auth):
        """Test policy update with non-existent ID."""
        update_payload = {"name": "Updated Test Policy", "version": 2}
        response = await async_client.put(f"/policies/99999", json=update_payload)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_policy_success(self, async_client, sample_policy_data, mock_keycloak_auth):
        """Test policy deletion - works because no @require_roles decorator."""
        # Skip creation since it requires authentication
        # Test deletion directly with a known ID
        response = await async_client.delete(f"/policies/1")

        assert response.status_code == 204

        # Verify the policy is deleted by trying to get it
        get_response = await async_client.get(f"/policies/1")
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_policy_not_found(self, async_client):
        """Test policy deletion with non-existent ID."""
        response = await async_client.delete(f"/policies/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_policys_success(self, async_client):
        """Test successful policy listing."""
        response = await async_client.get("/policies")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
