import pytest
from unittest.mock import patch, Mock
from urllib.parse import urlparse, parse_qs

from src.app.core.util.auth_utils import AuthUtils, AuthUtilsError


class TestAuthUtils:
    """Test suite for AuthUtils."""
    
    def test_generate_state(self):
        """Test state generation."""
        # Act
        state = AuthUtils.generate_state()
        
        # Assert
        assert isinstance(state, str)
        assert len(state) > 0
        
        # Should be base64 encoded
        import base64
        try:
            decoded = base64.urlsafe_b64decode(state.encode('utf-8'))
            assert len(decoded) > 0
        except Exception:
            pytest.fail("Generated state is not valid base64")
    
    def test_generate_unique_states(self):
        """Test that generated states are unique."""
        # Act
        states = [AuthUtils.generate_state() for _ in range(10)]
        
        # Assert
        assert len(set(states)) == 10  # All states should be unique
    
    def test_verify_state_valid(self):
        """Test state verification with valid state."""
        # Arrange
        state = AuthUtils.generate_state()
        
        # Act
        is_valid = AuthUtils.verify_state(state)
        
        # Assert
        assert is_valid is True
    
    def test_verify_state_invalid_format(self):
        """Test state verification with invalid format."""
        # Arrange
        invalid_states = [
            "invalid-state",
            "",
            "not-base64!@#",
            "dGVzdA=="  # Valid base64 but wrong format
        ]
        
        # Act & Assert
        for invalid_state in invalid_states:
            is_valid = AuthUtils.verify_state(invalid_state)
            assert is_valid is False
    
    def test_verify_state_tampered(self):
        """Test state verification with tampered state."""
        # Arrange
        original_state = AuthUtils.generate_state()
        
        # Tamper with the state
        import base64
        decoded = base64.urlsafe_b64decode(original_state.encode('utf-8')).decode('utf-8')
        parts = decoded.split('.')
        if len(parts) == 2:
            # Change the signature
            tampered_decoded = f"{parts[0]}.tampered_signature"
            tampered_state = base64.urlsafe_b64encode(tampered_decoded.encode('utf-8')).decode('utf-8')
        else:
            tampered_state = "tampered_state"
        
        # Act
        is_valid = AuthUtils.verify_state(tampered_state)
        
        # Assert
        assert is_valid is False
    
    def test_generate_nonce(self):
        """Test nonce generation."""
        # Act
        nonce = AuthUtils.generate_nonce()
        
        # Assert
        assert isinstance(nonce, str)
        assert len(nonce) > 0
        
        # Should be base64 encoded
        import base64
        try:
            decoded = base64.urlsafe_b64decode(nonce.encode('utf-8'))
            assert len(decoded) > 0
        except Exception:
            pytest.fail("Generated nonce is not valid base64")
    
    def test_generate_unique_nonces(self):
        """Test that generated nonces are unique."""
        # Act
        nonces = [AuthUtils.generate_nonce() for _ in range(10)]
        
        # Assert
        assert len(set(nonces)) == 10  # All nonces should be unique
    
    def test_verify_nonce_valid(self):
        """Test nonce verification with valid nonce."""
        # Arrange
        nonce = AuthUtils.generate_nonce()
        
        # Act
        is_valid = AuthUtils.verify_nonce(nonce)
        
        # Assert
        assert is_valid is True
    
    def test_verify_nonce_invalid(self):
        """Test nonce verification with invalid nonce."""
        # Arrange
        invalid_nonces = [
            "invalid-nonce",
            "",
            "not-base64!@#"
        ]
        
        # Act & Assert
        for invalid_nonce in invalid_nonces:
            is_valid = AuthUtils.verify_nonce(invalid_nonce)
            assert is_valid is False
    
    def test_build_authorization_url(self):
        """Test building authorization URL."""
        # Arrange
        base_url = "https://keycloak.example.com/realms/test/protocol/openid-connect/auth"
        client_id = "test-client"
        redirect_uri = "https://app.example.com/callback"
        state = "test-state"
        nonce = "test-nonce"
        scope = "openid profile email"
        
        # Act
        auth_url = AuthUtils.build_authorization_url(
            base_url=base_url,
            client_id=client_id,
            redirect_uri=redirect_uri,
            state=state,
            nonce=nonce,
            scope=scope
        )
        
        # Assert
        assert auth_url.startswith(base_url)
        
        # Parse URL and check parameters
        parsed_url = urlparse(auth_url)
        query_params = parse_qs(parsed_url.query)
        
        assert query_params['client_id'][0] == client_id
        assert query_params['response_type'][0] == 'code'
        assert query_params['scope'][0] == scope
        assert query_params['redirect_uri'][0] == redirect_uri
        assert query_params['state'][0] == state
        assert query_params['nonce'][0] == nonce
    
    def test_build_authorization_url_default_scope(self):
        """Test building authorization URL with default scope."""
        # Arrange
        base_url = "https://keycloak.example.com/auth"
        client_id = "test-client"
        redirect_uri = "https://app.example.com/callback"
        state = "test-state"
        nonce = "test-nonce"
        
        # Act
        auth_url = AuthUtils.build_authorization_url(
            base_url=base_url,
            client_id=client_id,
            redirect_uri=redirect_uri,
            state=state,
            nonce=nonce
            # No scope provided - should use default
        )
        
        # Assert
        parsed_url = urlparse(auth_url)
        query_params = parse_qs(parsed_url.query)
        assert query_params['scope'][0] == "openid profile email"
    
    def test_extract_tenant_id_from_realm_with_org(self):
        """Test extracting tenant ID from realm with organization name."""
        # Arrange
        realm_name = "acme-corp-tenant-123"
        
        # Act
        tenant_id = AuthUtils.extract_tenant_id_from_realm(realm_name)
        
        # Assert
        assert tenant_id == "123"
    
    def test_extract_tenant_id_from_realm_simple(self):
        """Test extracting tenant ID from simple realm format."""
        # Arrange
        realm_name = "tenant-456"
        
        # Act
        tenant_id = AuthUtils.extract_tenant_id_from_realm(realm_name)
        
        # Assert
        assert tenant_id == "456"
    
    def test_extract_tenant_id_from_realm_unexpected_format(self):
        """Test extracting tenant ID from unexpected realm format."""
        # Arrange
        realm_name = "unexpected-format"
        
        # Act
        tenant_id = AuthUtils.extract_tenant_id_from_realm(realm_name)
        
        # Assert
        # Should return the original realm name as fallback
        assert tenant_id == "unexpected-format"
    
    def test_get_cookie_settings(self):
        """Test getting cookie settings."""
        # Act
        cookie_settings = AuthUtils.get_cookie_settings()
        
        # Assert
        assert isinstance(cookie_settings, dict)
        assert 'httponly' in cookie_settings
        assert cookie_settings['httponly'] is True
        assert 'secure' in cookie_settings
        assert 'samesite' in cookie_settings
        assert 'max_age' in cookie_settings
    
    @patch('src.app.core.util.auth_utils.auth_config')
    def test_get_cookie_settings_with_config(self, mock_auth_config):
        """Test cookie settings with specific configuration."""
        # Arrange
        mock_auth_config.cookie_domain = "example.com"
        mock_auth_config.cookie_secure = True
        mock_auth_config.cookie_samesite = "strict"
        mock_auth_config.cookie_max_age = 3600
        
        # Act
        cookie_settings = AuthUtils.get_cookie_settings()
        
        # Assert
        assert cookie_settings['domain'] == "example.com"
        assert cookie_settings['secure'] is True
        assert cookie_settings['samesite'] == "strict"
        assert cookie_settings['max_age'] == 3600
        assert cookie_settings['httponly'] is True
    
    def test_state_nonce_independence(self):
        """Test that state and nonce generation are independent."""
        # Act
        states = [AuthUtils.generate_state() for _ in range(5)]
        nonces = [AuthUtils.generate_nonce() for _ in range(5)]
        
        # Assert
        # States and nonces should be different
        assert len(set(states + nonces)) == 10
        
        # Each state should verify correctly
        for state in states:
            assert AuthUtils.verify_state(state) is True
        
        # Each nonce should verify correctly
        for nonce in nonces:
            assert AuthUtils.verify_nonce(nonce) is True
        
        # States should not verify as nonces and vice versa
        for state in states:
            assert AuthUtils.verify_nonce(state) is False
        
        for nonce in nonces:
            assert AuthUtils.verify_state(nonce) is False
    
    def test_error_handling_in_state_generation(self):
        """Test error handling in state generation."""
        with patch('src.app.core.util.auth_utils.secrets.token_urlsafe') as mock_token:
            mock_token.side_effect = Exception("Random generation failed")
            
            # Act & Assert
            with pytest.raises(AuthUtilsError, match="State generation failed"):
                AuthUtils.generate_state()
    
    def test_error_handling_in_nonce_generation(self):
        """Test error handling in nonce generation."""
        with patch('src.app.core.util.auth_utils.secrets.token_urlsafe') as mock_token:
            mock_token.side_effect = Exception("Random generation failed")
            
            # Act & Assert
            with pytest.raises(AuthUtilsError, match="Nonce generation failed"):
                AuthUtils.generate_nonce()
    
    def test_error_handling_in_url_building(self):
        """Test error handling in URL building."""
        with patch('src.app.core.util.auth_utils.urlencode') as mock_urlencode:
            mock_urlencode.side_effect = Exception("URL encoding failed")
            
            # Act & Assert
            with pytest.raises(AuthUtilsError, match="Authorization URL building failed"):
                AuthUtils.build_authorization_url(
                    base_url="https://example.com",
                    client_id="test",
                    redirect_uri="https://app.com",
                    state="state",
                    nonce="nonce"
                )
    
    def test_error_handling_in_tenant_extraction(self):
        """Test error handling in tenant ID extraction."""
        with patch('src.app.core.util.auth_utils.logger') as mock_logger:
            # This should not raise an exception but log a warning
            result = AuthUtils.extract_tenant_id_from_realm("weird-format")
            assert result == "weird-format"
            
            # Should handle None input gracefully
            with pytest.raises(AuthUtilsError):
                AuthUtils.extract_tenant_id_from_realm(None)
