"""
Test suite for Notification CRUD operations.

This file was auto-generated by test_generator.py
"""

# Apply patches BEFORE any other imports
from unittest.mock import patch, AsyncMock, MagicMock
from functools import wraps

# Mock the require_roles decorator to bypass authentication (though notification router doesn't use it)
def mock_require_roles(*roles):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Simply pass through - no authentication required in tests
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# Apply patches immediately
patch('src.app.core.security.keycloak_auth.require_roles', mock_require_roles).start()

import pytest

# Mock the Notification service to avoid database calls
class MockNotificationService:
    # Class variable to track deleted IDs across all instances
    deleted_ids = set()

    def __init__(self, session=None):
        self.session = session

    async def create_notification(self, payload):
        from src.app.api.schema.notification.notification_create_response import NotificationCreateResponse
        from datetime import datetime, timezone

        now = datetime.now(timezone.utc)
        return NotificationCreateResponse(
            id=1,
            event_type=payload.event_type,
            details=payload.details,
            sent_to=payload.sent_to,
            timestamp=now,
            tenant_id=1,
            user_id=payload.user_id,
            api_key_id=payload.api_key_id
        )

    async def get_notification_by_id(self, notification_id):
        from src.app.api.schema.notification.notification_read_response import NotificationReadResponse
        from src.app.dao.entity.notification_type import NotificationType
        from datetime import datetime, timezone

        if notification_id == 99999 or notification_id in MockNotificationService.deleted_ids:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"Notification with id {notification_id} not found.")

        now = datetime.now(timezone.utc)
        return NotificationReadResponse(
            id=notification_id,
            event_type=NotificationType.VIOLATION,
            details="Test notification details",
            sent_to="<EMAIL>",
            timestamp=now,
            tenant_id=1,
            user_id=None,
            api_key_id=None
        )

    async def get_notifications(self):
        return []

    async def update_notification(self, notification_id, payload):
        from src.app.api.schema.notification.notification_update_response import NotificationUpdateResponse
        from src.app.dao.entity.notification_type import NotificationType
        from datetime import datetime, timezone

        if notification_id == 99999 or notification_id in MockNotificationService.deleted_ids:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"Notification with id {notification_id} not found.")

        now = datetime.now(timezone.utc)
        return NotificationUpdateResponse(
            id=notification_id,
            event_type=payload.event_type or NotificationType.VIOLATION,
            details=payload.details or "Test notification details",
            sent_to=payload.sent_to or "<EMAIL>",
            timestamp=now,
            tenant_id=1,
            user_id=payload.user_id,
            api_key_id=payload.api_key_id
        )

    async def delete_notification(self, notification_id):
        if notification_id == 99999:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"Notification with id {notification_id} not found.")

        # Mark the notification as deleted
        MockNotificationService.deleted_ids.add(notification_id)
        return None

# Patch the service before importing the router
patch('src.app.api.service.notification_service.NotificationService', MockNotificationService).start()


class TestNotificationCRUD:
    """Test suite for Notification CRUD operations."""

    @pytest.mark.asyncio
    async def test_create_notification_success(self, async_client, sample_notification_data):
        """Test successful notification creation."""
        response = await async_client.post("/notifications", json=sample_notification_data)

        assert response.status_code == 201
        data = response.json()

        assert "id" in data
        for key, value in sample_notification_data.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_create_notification_invalid_data(self, async_client):
        """Test notification creation with invalid data."""
        payload = {"invalid_field": "invalid_value"}

        response = await async_client.post("/notifications", json=payload)

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_notification_success(self, async_client, sample_notification_data):
        """Test successful notification retrieval."""
        create_payload = {**sample_notification_data, "details": "Test Notification Get"}
        create_response = await async_client.post("/notifications", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.get(f"/notifications/{created_id}")

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == created_id

    @pytest.mark.asyncio
    async def test_get_notification_not_found(self, async_client):
        """Test notification retrieval with non-existent ID."""
        response = await async_client.get(f"/notifications/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_notification_success(self, async_client, sample_notification_data):
        """Test successful notification update."""
        create_payload = {**sample_notification_data, "details": "Test Notification Update"}
        create_response = await async_client.post("/notifications", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        update_payload = {"details": "Updated notification details"}
        response = await async_client.put(f"/notifications/{created_id}", json=update_payload)

        assert response.status_code == 200
        data = response.json()

        for key, value in update_payload.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_update_notification_not_found(self, async_client):
        """Test notification update with non-existent ID."""
        update_payload = {"details": "Updated notification details"}
        response = await async_client.put(f"/notifications/99999", json=update_payload)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_notification_success(self, async_client, sample_notification_data):
        """Test successful notification deletion."""
        create_payload = {**sample_notification_data, "details": "Test Notification Delete"}
        create_response = await async_client.post("/notifications", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.delete(f"/notifications/{created_id}")

        assert response.status_code == 204

        get_response = await async_client.get(f"/notifications/{created_id}")
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_notification_not_found(self, async_client):
        """Test notification deletion with non-existent ID."""
        response = await async_client.delete(f"/notifications/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_notifications_success(self, async_client):
        """Test successful notification listing."""
        response = await async_client.get("/notifications")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
