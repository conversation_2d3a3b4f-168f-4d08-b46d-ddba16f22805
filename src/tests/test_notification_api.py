"""
Test suite for Notification CRUD operations.

This file was auto-generated by test_generator.py
"""

import pytest


class TestNotificationCRUD:
    """Test suite for Notification CRUD operations."""

    @pytest.mark.asyncio
    async def test_create_notification_success(self, async_client, sample_notification_data):
        """Test successful notification creation."""
        response = await async_client.post("/notifications", json=sample_notification_data)

        assert response.status_code == 201
        data = response.json()

        assert "id" in data
        for key, value in sample_notification_data.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_create_notification_invalid_data(self, async_client):
        """Test notification creation with invalid data."""
        payload = {"invalid_field": "invalid_value"}

        response = await async_client.post("/notifications", json=payload)

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_notification_success(self, async_client, sample_notification_data):
        """Test successful notification retrieval."""
        create_payload = {**sample_notification_data, "details": "Test Notification Get"}
        create_response = await async_client.post("/notifications", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.get(f"/notifications/{created_id}")

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == created_id

    @pytest.mark.asyncio
    async def test_get_notification_not_found(self, async_client):
        """Test notification retrieval with non-existent ID."""
        response = await async_client.get(f"/notifications/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_notification_success(self, async_client, sample_notification_data):
        """Test successful notification update."""
        create_payload = {**sample_notification_data, "details": "Test Notification Update"}
        create_response = await async_client.post("/notifications", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        update_payload = {"details": "Updated notification details"}
        response = await async_client.put(f"/notifications/{created_id}", json=update_payload)

        assert response.status_code == 200
        data = response.json()

        for key, value in update_payload.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_update_notification_not_found(self, async_client):
        """Test notification update with non-existent ID."""
        update_payload = {"details": "Updated notification details"}
        response = await async_client.put(f"/notifications/99999", json=update_payload)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_notification_success(self, async_client, sample_notification_data):
        """Test successful notification deletion."""
        create_payload = {**sample_notification_data, "details": "Test Notification Delete"}
        create_response = await async_client.post("/notifications", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.delete(f"/notifications/{created_id}")

        assert response.status_code == 204

        get_response = await async_client.get(f"/notifications/{created_id}")
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_notification_not_found(self, async_client):
        """Test notification deletion with non-existent ID."""
        response = await async_client.delete(f"/notifications/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_notifications_success(self, async_client):
        """Test successful notification listing."""
        response = await async_client.get("/notifications")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
