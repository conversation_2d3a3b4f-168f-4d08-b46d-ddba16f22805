from unittest.mock import Mock, patch, AsyncMock

import pytest
from keycloak.exceptions import KeycloakGetError, KeycloakPostError

from src.app.api.schema.keycloak.keycloak_provision_request import KeycloakProvisionRequest
from src.app.api.service.keycloak_service import (
    KeycloakService,
    provision_tenant,
    create_admin_client,
)
from src.app.core.config.keycloak_config import get_realm_name
from src.app.core.model.user_context import UserContext


class TestKeycloakService:

    """Mock Keycloak admin client."""
    @pytest.fixture
    def mock_keycloak_admin(self):
        mock_admin = Mock()
        mock_admin.server_url = "http://localhost:8080"
        mock_admin.create_realm = Mock()
        mock_admin.create_client = Mock(return_value="client-uuid")
        mock_admin.get_client_secrets = Mock(return_value={"value": "test-secret"})
        mock_admin.create_realm_role = Mock()
        mock_admin.create_user = Mock(return_value={"id": "user-uuid"})
        mock_admin.get_realm_role = Mock(return_value={"id": "role-id", "name": "tenant_admin"})
        mock_admin.assign_realm_roles = Mock()
        mock_admin.delete_realm = Mock()
        mock_admin.get_realm = Mock(return_value={"realm": "test-realm", "enabled": True})
        mock_admin.get_realms = Mock(return_value=[{"realm": "master"}])

        return mock_admin

    
    """Create KeycloakService instance with mocked admin client."""
    @pytest.fixture
    def keycloak_service(self, db_session, mock_keycloak_admin):
        service = KeycloakService(db_session)
        return service

    """Create a mock user context for testing."""

    @pytest.fixture
    def mock_user_context(self):
        user_context = Mock(spec=UserContext)
        user_context.tenant_id = "test-tenant-123"
        user_context.user_id = "test-user-123"
        user_context.role = "TENANT_ADMIN"
        return user_context


    """Test successful tenant provisioning."""
    @pytest.mark.asyncio
    async def test_provision_tenant_success(self, mock_keycloak_admin, sample_keycloak_provision_data):
        request = KeycloakProvisionRequest(**sample_keycloak_provision_data)

        # Mock the create_admin_client function and keycloak_config.get_realm_name
        with patch('src.app.api.service.keycloak_service.create_admin_client', return_value=mock_keycloak_admin), \
                patch('src.app.api.service.keycloak_service.keycloak_config') as mock_config:
            expected_realm_name = get_realm_name(request.tenant_id, request.organization_name)
            mock_config.get_realm_name.return_value = expected_realm_name
            mock_config.client_name = "securinest"
            mock_config.realm_roles = ["TENANT_ADMIN", "DEVELOPER"]
            mock_config.redirect_uri = "http://localhost:3000/callback"

            result = await provision_tenant(request)

        assert result.success is True
        assert result.tenant_id == request.tenant_id
        assert result.realm_name == expected_realm_name

        assert result.client_id == "securinest"
        assert result.client_secret == "test-secret"
        assert result.admin_user_id == "user-uuid"
        assert "TENANT_ADMIN" in result.realm_roles
        assert "Successfully provisioned" in result.message


    """Test tenant provisioning with realm creation failure."""
    @pytest.mark.asyncio
    async def test_provision_tenant_realm_creation_failure(self, mock_keycloak_admin, sample_keycloak_provision_data):
        request = KeycloakProvisionRequest(**sample_keycloak_provision_data)

        # Set up the failure
        mock_keycloak_admin.create_realm.side_effect = KeycloakPostError("Realm creation failed")

        # Mock the create_admin_client function and keycloak_config.get_realm_name
        with patch('src.app.api.service.keycloak_service.create_admin_client', return_value=mock_keycloak_admin), \
                patch('src.app.api.service.keycloak_service.keycloak_config') as mock_config:
            expected_realm_name = get_realm_name(request.tenant_id, request.organization_name)
            mock_config.get_realm_name.return_value = expected_realm_name
            mock_config.master_realm = "master"

            result = await provision_tenant(request)

        assert result.success is False
        assert "Failed to provision tenant" in result.message


    """Test tenant provisioning with client creation failure."""
    @pytest.mark.asyncio
    async def test_provision_tenant_client_creation_failure(self, mock_keycloak_admin, sample_keycloak_provision_data):
        request = KeycloakProvisionRequest(**sample_keycloak_provision_data)

        # Set up the failure
        mock_keycloak_admin.create_client.side_effect = Exception("Client creation failed")

        # Mock the create_admin_client function and keycloak_config.get_realm_name
        with patch('src.app.api.service.keycloak_service.create_admin_client', return_value=mock_keycloak_admin), \
                patch('src.app.api.service.keycloak_service.keycloak_config') as mock_config:
            expected_realm_name = get_realm_name(request.tenant_id, request.organization_name)
            mock_config.get_realm_name.return_value = expected_realm_name
            mock_config.master_realm = "master"

            result = await provision_tenant(request)

        assert result.success is False
        assert "Failed to provision tenant" in result.message


    """Test tenant provisioning when realm already exists."""
    @pytest.mark.asyncio
    async def test_provision_tenant_existing_realm(self, mock_keycloak_admin, sample_keycloak_provision_data):
        request = KeycloakProvisionRequest(**sample_keycloak_provision_data)

        # Set up the conflict
        mock_keycloak_admin.create_realm.side_effect = KeycloakPostError("409 Conflict")

        # Mock the create_admin_client function and keycloak_config.get_realm_name
        with patch('src.app.api.service.keycloak_service.create_admin_client', return_value=mock_keycloak_admin), \
                patch('src.app.api.service.keycloak_service.keycloak_config') as mock_config:
            expected_realm_name = get_realm_name(request.tenant_id, request.organization_name)
            mock_config.get_realm_name.return_value = expected_realm_name
            mock_config.client_name = "test-client"
            mock_config.realm_roles = ["TENANT_ADMIN", "DEVELOPER"]
            mock_config.redirect_uri = "http://localhost:3000/callback"

            result = await provision_tenant(request)

        assert result.success is True

    
    """Test successful tenant realm deletion."""
    @pytest.mark.asyncio
    async def test_delete_tenant_realm_success(self, keycloak_service, mock_user_context, mock_keycloak_admin):
        tenant_id = "test-tenant-123"

        # Mock the _get_organization_name method
        keycloak_service._get_organization_name = AsyncMock(return_value="Test Organization")

        # Mock the create_admin_client function and keycloak_config.get_realm_name
        with patch('src.app.api.service.keycloak_service.create_admin_client', return_value=mock_keycloak_admin), \
                patch('src.app.api.service.keycloak_service.keycloak_config') as mock_config:
            expected_realm_name = get_realm_name(tenant_id, "Test Organization")
            mock_config.get_realm_name.return_value = expected_realm_name
            mock_config.master_realm = "master"

            result = await keycloak_service.delete_tenant_realm(tenant_id, mock_user_context)

        assert result is True
        # Verify the mock was called
        mock_keycloak_admin.delete_realm.assert_called_once()


    """Test tenant realm deletion when realm doesn't exist."""
    @pytest.mark.asyncio
    async def test_delete_tenant_realm_not_found(self, keycloak_service, mock_user_context, mock_keycloak_admin):
        tenant_id = "nonexistent-tenant"

        # Mock the _get_organization_name method
        keycloak_service._get_organization_name = AsyncMock(return_value="Test Organization")

        # Set up the error
        mock_keycloak_admin.delete_realm.side_effect = KeycloakGetError("Realm not found")

        # Mock the create_admin_client function and keycloak_config.get_realm_name
        with patch('src.app.api.service.keycloak_service.create_admin_client', return_value=mock_keycloak_admin), \
                patch('src.app.api.service.keycloak_service.keycloak_config') as mock_config:
            expected_realm_name = get_realm_name(tenant_id, "Test Organization")
            mock_config.get_realm_name.return_value = expected_realm_name
            mock_config.master_realm = "master"

            result = await keycloak_service.delete_tenant_realm(tenant_id, mock_user_context)

        assert result is True


    """Test tenant realm deletion failure."""
    @pytest.mark.asyncio
    async def test_delete_tenant_realm_failure(self, keycloak_service, mock_user_context, mock_keycloak_admin):
        tenant_id = "test-tenant-123"

        # Mock the _get_organization_name method
        keycloak_service._get_organization_name = AsyncMock(return_value="Test Organization")

        # Set up the failure
        mock_keycloak_admin.delete_realm.side_effect = Exception("Deletion failed")

        # Mock the create_admin_client function and keycloak_config.get_realm_name
        with patch('src.app.api.service.keycloak_service.create_admin_client', return_value=mock_keycloak_admin), \
                patch('src.app.api.service.keycloak_service.keycloak_config') as mock_config:
            expected_realm_name = get_realm_name(tenant_id, "Test Organization")
            mock_config.get_realm_name.return_value = expected_realm_name
            mock_config.master_realm = "master"

            result = await keycloak_service.delete_tenant_realm(tenant_id, mock_user_context)

        assert result is False

    
    """Test successful realm info retrieval."""
    @pytest.mark.asyncio
    async def test_get_realm_info_success(self, keycloak_service, mock_user_context, mock_keycloak_admin):
        tenant_id = "test-tenant-123"
        expected_info = {"realm": "tenant-test-tenant-123", "enabled": True}

        # Mock the _get_organization_name method
        keycloak_service._get_organization_name = AsyncMock(return_value="Test Organization")

        # Set up the return value
        mock_keycloak_admin.get_realm.return_value = expected_info

        # Mock the create_admin_client function and keycloak_config.get_realm_name
        with patch('src.app.api.service.keycloak_service.create_admin_client', return_value=mock_keycloak_admin), \
                patch('src.app.api.service.keycloak_service.keycloak_config') as mock_config:
            expected_realm_name = get_realm_name(tenant_id, "Test Organization")
            mock_config.get_realm_name.return_value = expected_realm_name
            mock_config.master_realm = "master"

            result = await keycloak_service.get_realm_info(tenant_id, mock_user_context)

        # The result should be filtered, so we need to check the filtered version
        assert result is not None
        # Verify the mock was called
        mock_keycloak_admin.get_realm.assert_called_once()


    """Test realm info retrieval when realm doesn't exist."""
    @pytest.mark.asyncio
    async def test_get_realm_info_not_found(self, keycloak_service, mock_keycloak_admin):
        tenant_id = "nonexistent-tenant"

        # Create a mock user context with matching tenant_id to avoid ForbiddenException
        mock_user_context = Mock(spec=UserContext)
        mock_user_context.tenant_id = tenant_id

        # Mock the _get_organization_name method
        keycloak_service._get_organization_name = AsyncMock(return_value="Test Organization")

        # Set up the error
        mock_keycloak_admin.get_realm.side_effect = KeycloakGetError("Realm not found")

        # Mock the create_admin_client function and keycloak_config.get_realm_name
        with patch('src.app.api.service.keycloak_service.create_admin_client', return_value=mock_keycloak_admin), \
                patch('src.app.api.service.keycloak_service.keycloak_config') as mock_config:
            expected_realm_name = get_realm_name(tenant_id, "Test Organization")
            mock_config.get_realm_name.return_value = expected_realm_name
            mock_config.master_realm = "master"

            # This should raise NotFoundException, not return None
            from src.app.core.exception.not_found_exception import NotFoundException
            with pytest.raises(NotFoundException):
                await keycloak_service.get_realm_info(tenant_id, mock_user_context)


    """Test Keycloak admin client connection failure."""
    @pytest.mark.asyncio
    async def test_admin_client_connection_failure(self, db_session):
        with patch('src.app.api.service.keycloak_service.KeycloakAdmin') as mock_keycloak_admin:
            mock_keycloak_admin.side_effect = Exception("Connection failed")

            from src.app.core.exception.processing_exception import ProcessingException
            with pytest.raises(ProcessingException):
                _ = create_admin_client()
