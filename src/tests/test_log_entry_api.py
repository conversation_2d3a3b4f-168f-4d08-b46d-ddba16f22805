"""
Test suite for LogEntry CRUD operations.

This file was auto-generated by test_generator.py
"""

import pytest


class TestLogEntryCRUD:
    """Test suite for LogEntry CRUD operations."""

    @pytest.mark.asyncio
    async def test_create_log_entry_success(self, async_client, sample_log_entry_data):
        """Test successful log_entry creation."""
        response = await async_client.post("/log-entries", json=sample_log_entry_data)

        assert response.status_code == 201
        data = response.json()

        assert "id" in data
        for key, value in sample_log_entry_data.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_create_log_entry_invalid_data(self, async_client):
        """Test log_entry creation with invalid data."""
        payload = {"invalid_field": "invalid_value"}

        response = await async_client.post("/log-entries", json=payload)

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_log_entry_success(self, async_client, sample_log_entry_data):
        """Test successful log_entry retrieval."""
        create_payload = {**sample_log_entry_data, "name": "Test LogEntry Get"}
        create_response = await async_client.post("/log-entries", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.get(f"/log-entries/{created_id}")

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == created_id

    @pytest.mark.asyncio
    async def test_get_log_entry_not_found(self, async_client):
        """Test log_entry retrieval with non-existent ID."""
        response = await async_client.get(f"/log-entries/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_log_entry_success(self, async_client, sample_log_entry_data):
        """Test successful log_entry update."""
        create_payload = {**sample_log_entry_data, "request_id": "test_req_update_12345"}
        create_response = await async_client.post("/log-entries", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        update_payload = {
            "request_id": "updated_req_12345",
            "tenant_id": 1,
            "model": "gpt-4-turbo",
            "outcome": "BLOCKED",
            "provider_latency_ms": 150
        }
        response = await async_client.put(f"/log-entries/{created_id}", json=update_payload)

        assert response.status_code == 200
        data = response.json()

        for key, value in update_payload.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_update_log_entry_not_found(self, async_client):
        """Test log_entry update with non-existent ID."""
        update_payload = {
            "request_id": "updated_req_12345",
            "tenant_id": 1,
            "model": "gpt-4-turbo",
            "outcome": "BLOCKED",
            "provider_latency_ms": 150
        }
        response = await async_client.put(f"/log-entries/99999", json=update_payload)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_log_entry_success(self, async_client, sample_log_entry_data):
        """Test successful log_entry deletion."""
        create_payload = {**sample_log_entry_data, "request_id": "test_req_delete_12345"}
        create_response = await async_client.post("/log-entries", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.delete(f"/log-entries/{created_id}")

        assert response.status_code == 204

        get_response = await async_client.get(f"/log-entries/{created_id}")
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_log_entry_not_found(self, async_client):
        """Test log_entry deletion with non-existent ID."""
        response = await async_client.delete(f"/log-entries/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_log_entrys_success(self, async_client):
        """Test successful log_entry listing."""
        response = await async_client.get("/log-entries")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
