"""
Test suite for LogEntry CRUD operations.

This file was auto-generated by test_generator.py
"""

# Apply patches BEFORE any other imports
from unittest.mock import patch, AsyncMock, MagicMock
from functools import wraps

# Mock the require_roles decorator to bypass authentication (though log entry router doesn't use it)
def mock_require_roles(*roles):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Simply pass through - no authentication required in tests
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# Apply patches immediately
patch('src.app.core.security.keycloak_auth.require_roles', mock_require_roles).start()

import pytest

# Mock the LogEntry service to avoid database calls
class MockLogEntryService:
    # Class variable to track deleted IDs across all instances
    deleted_ids = set()

    def __init__(self, session=None):
        self.session = session

    async def save_log_entry(self, payload):
        from src.app.api.schema.log_entry.create_log_entry_response import CreateLogEntryResponse

        return CreateLogEntryResponse(
            id=1,
            request_id=payload.request_id,
            api_key_id=payload.api_key_id,
            user_id=payload.user_id,
            tenant_id=payload.tenant_id,
            model=payload.model,
            prompt_text=payload.prompt_text,
            outcome=payload.outcome,
            rule_triggers=payload.rule_triggers,
            provider_latency_ms=payload.provider_latency_ms,
            prompt_tokens=payload.prompt_tokens,
            completion_tokens=payload.completion_tokens,
            masked_response=getattr(payload, 'masked_response', None)
        )

    async def get_log_entry_by_id(self, log_entry_id):
        from src.app.api.schema.log_entry.read_log_entry_response import ReadLogEntryResponse
        from src.app.dao.entity.outcome import Outcome

        if log_entry_id == 99999 or log_entry_id in MockLogEntryService.deleted_ids:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"LogEntry with id {log_entry_id} not found.")

        return ReadLogEntryResponse(
            id=log_entry_id,
            request_id="test_req_12345",
            api_key_id=None,
            user_id=None,
            tenant_id=1,
            model="gpt-4-turbo",
            prompt_text="Test prompt for API testing",
            outcome=Outcome.ALLOWED,
            rule_triggers=None,
            provider_latency_ms=100,
            prompt_tokens=10,
            completion_tokens=20,
            masked_response=None
        )

    async def get_all_log_entries(self):
        return []

    async def update_log_entry(self, log_entry_id, payload):
        from src.app.api.schema.log_entry.update_log_entry_response import UpdateLogEntryResponse
        from src.app.dao.entity.outcome import Outcome

        if log_entry_id == 99999 or log_entry_id in MockLogEntryService.deleted_ids:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"LogEntry with id {log_entry_id} not found.")

        return UpdateLogEntryResponse(
            request_id=payload.request_id,
            api_key_id=payload.api_key_id,
            user_id=payload.user_id,
            tenant_id=payload.tenant_id,
            model=payload.model,
            prompt_text=payload.prompt_text,
            outcome=payload.outcome,
            rule_triggers=payload.rule_triggers,
            provider_latency_ms=payload.provider_latency_ms,
            prompt_tokens=payload.prompt_tokens,
            completion_tokens=payload.completion_tokens,
            masked_response=getattr(payload, 'masked_response', None)
        )

    async def delete_log_entry_by_id(self, log_entry_id):
        if log_entry_id == 99999:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"LogEntry with id {log_entry_id} not found.")

        # Mark the log entry as deleted
        MockLogEntryService.deleted_ids.add(log_entry_id)
        return None

# Patch the service before importing the router
patch('src.app.api.service.log_entry_service.LogEntryService', MockLogEntryService).start()


class TestLogEntryCRUD:
    """Test suite for LogEntry CRUD operations."""

    @pytest.mark.asyncio
    async def test_create_log_entry_success(self, async_client, sample_log_entry_data):
        """Test successful log_entry creation."""
        response = await async_client.post("/log-entries", json=sample_log_entry_data)

        assert response.status_code == 201
        data = response.json()

        assert "id" in data
        for key, value in sample_log_entry_data.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_create_log_entry_invalid_data(self, async_client):
        """Test log_entry creation with invalid data."""
        payload = {"invalid_field": "invalid_value"}

        response = await async_client.post("/log-entries", json=payload)

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_log_entry_success(self, async_client, sample_log_entry_data):
        """Test successful log_entry retrieval."""
        create_payload = {**sample_log_entry_data, "name": "Test LogEntry Get"}
        create_response = await async_client.post("/log-entries", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.get(f"/log-entries/{created_id}")

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == created_id

    @pytest.mark.asyncio
    async def test_get_log_entry_not_found(self, async_client):
        """Test log_entry retrieval with non-existent ID."""
        response = await async_client.get(f"/log-entries/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_log_entry_success(self, async_client, sample_log_entry_data):
        """Test successful log_entry update."""
        create_payload = {**sample_log_entry_data, "request_id": "test_req_update_12345"}
        create_response = await async_client.post("/log-entries", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        update_payload = {
            "request_id": "updated_req_12345",
            "tenant_id": 1,
            "model": "gpt-4-turbo",
            "outcome": "BLOCKED",
            "provider_latency_ms": 150
        }
        response = await async_client.put(f"/log-entries/{created_id}", json=update_payload)

        assert response.status_code == 200
        data = response.json()

        for key, value in update_payload.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_update_log_entry_not_found(self, async_client):
        """Test log_entry update with non-existent ID."""
        update_payload = {
            "request_id": "updated_req_12345",
            "tenant_id": 1,
            "model": "gpt-4-turbo",
            "outcome": "BLOCKED",
            "provider_latency_ms": 150
        }
        response = await async_client.put(f"/log-entries/99999", json=update_payload)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_log_entry_success(self, async_client, sample_log_entry_data):
        """Test successful log_entry deletion."""
        create_payload = {**sample_log_entry_data, "request_id": "test_req_delete_12345"}
        create_response = await async_client.post("/log-entries", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.delete(f"/log-entries/{created_id}")

        assert response.status_code == 204

        get_response = await async_client.get(f"/log-entries/{created_id}")
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_log_entry_not_found(self, async_client):
        """Test log_entry deletion with non-existent ID."""
        response = await async_client.delete(f"/log-entries/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_log_entrys_success(self, async_client):
        """Test successful log_entry listing."""
        response = await async_client.get("/log-entries")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
