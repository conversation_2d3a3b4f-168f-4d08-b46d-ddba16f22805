import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timezone, timedelta

from src.app.api.service.auth_service import AuthService
from src.app.api.service.keycloak_service import KeycloakService
from src.app.api.schema.auth.login_request import <PERSON><PERSON><PERSON>equest
from src.app.api.schema.auth.callback_request import CallbackRequest
from src.app.api.schema.auth.logout_request import LogoutRequest
from src.app.core.exception.processing_exception import ProcessingException
from src.app.core.model.user_context import UserContext


class TestAuthService:
    """Test suite for AuthService."""
    
    @pytest.fixture
    def mock_session(self):
        """Mock database session."""
        return Mock()
    
    @pytest.fixture
    def mock_keycloak_service(self):
        """Mock KeycloakService."""
        service = Mock(spec=KeycloakService)
        service.get_realm_name_by_tenant_id = AsyncMock()
        service.build_authorization_url = Mock()
        service.exchange_code_for_tokens = AsyncMock()
        service.refresh_access_token = AsyncMock()
        service.logout_user = AsyncMock()
        service.build_logout_url = Mock()
        service.validate_token_and_extract_realm = AsyncMock()
        return service
    
    @pytest.fixture
    def auth_service(self, mock_session, mock_keycloak_service):
        """Create AuthService instance with mocked dependencies."""
        return AuthService(mock_session, mock_keycloak_service)
    
    @pytest.fixture
    def mock_response(self):
        """Mock FastAPI Response object."""
        response = Mock()
        response.set_cookie = Mock()
        response.delete_cookie = Mock()
        return response
    
    @pytest.fixture
    def mock_request(self):
        """Mock FastAPI Request object."""
        request = Mock()
        request.cookies = {}
        return request
    
    @pytest.mark.asyncio
    async def test_initiate_login_success(self, auth_service, mock_keycloak_service):
        """Test successful login initiation."""
        # Arrange
        tenant_id = "test-tenant-123"
        realm_name = "test-realm"
        
        mock_keycloak_service.get_realm_name_by_tenant_id.return_value = realm_name
        mock_keycloak_service.build_authorization_url.return_value = "https://keycloak.example.com/auth"
        
        login_request = LoginRequest(tenant_id=tenant_id)
        
        with patch('src.app.api.service.auth_service.session_state_manager') as mock_state_manager:
            mock_state_manager.store_state = AsyncMock()
            
            with patch('src.app.core.util.auth_utils.AuthUtils.generate_state') as mock_generate_state, \
                 patch('src.app.core.util.auth_utils.AuthUtils.generate_nonce') as mock_generate_nonce:
                
                mock_generate_state.return_value = "test-state"
                mock_generate_nonce.return_value = "test-nonce"
                
                # Act
                result = await auth_service.initiate_login(login_request)
                
                # Assert
                assert result.authorization_url == "https://keycloak.example.com/auth"
                assert result.state == "test-state"
                assert result.realm_name == realm_name
                
                mock_keycloak_service.get_realm_name_by_tenant_id.assert_called_once_with(tenant_id)
                mock_state_manager.store_state.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_initiate_login_invalid_tenant(self, auth_service, mock_keycloak_service):
        """Test login initiation with invalid tenant."""
        # Arrange
        mock_keycloak_service.get_realm_name_by_tenant_id.side_effect = ProcessingException("Tenant not found")
        
        login_request = LoginRequest(tenant_id="invalid-tenant")
        
        # Act & Assert
        with pytest.raises(ProcessingException) as exc_info:
            await auth_service.initiate_login(login_request)
        assert exc_info.value.message == "Tenant not found"
    
    @pytest.mark.asyncio
    async def test_handle_callback_success(self, auth_service, mock_keycloak_service, mock_response):
        """Test successful callback handling."""
        # Arrange
        callback_request = CallbackRequest(
            code="auth-code-123",
            state="test-state"
        )
        
        state_data = {
            'realm_name': 'test-realm',
            'tenant_id': 'test-tenant',
            'nonce': 'test-nonce',
            'redirect_uri': None
        }
        
        token_data = {
            'access_token': 'access-token-123',
            'refresh_token': 'refresh-token-123',
            'expires_in': 1800,
            'token_type': 'Bearer'
        }
        
        user_context = UserContext(
            user_id="user-123",
            username="testuser",
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            realm="test-realm",
            tenant_id="test-tenant",
            roles=["TENANT_ADMIN"],
            is_active=True
        )
        
        mock_keycloak_service.exchange_code_for_tokens.return_value = token_data
        
        with patch('src.app.api.service.auth_service.session_state_manager') as mock_state_manager, \
             patch('src.app.core.util.auth_utils.AuthUtils.verify_state') as mock_verify_state, \
             patch('src.app.api.service.auth_service.keycloak_auth') as mock_keycloak_auth, \
             patch('src.app.core.util.auth_utils.AuthUtils.get_cookie_settings') as mock_cookie_settings:
            
            mock_verify_state.return_value = True
            mock_state_manager.retrieve_state = AsyncMock(return_value=state_data)
            mock_keycloak_auth.extract_user_context = AsyncMock(return_value=user_context)
            mock_cookie_settings.return_value = {'secure': True, 'httponly': True}
            
            # Act
            result = await auth_service.handle_callback(callback_request, mock_response)
            
            # Assert
            assert result.access_token == "access-token-123"
            assert result.user_id == "user-123"
            assert result.username == "testuser"
            assert result.tenant_id == "test-tenant"
            
            mock_response.set_cookie.assert_called_once()
            mock_keycloak_service.exchange_code_for_tokens.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_handle_callback_invalid_state(self, auth_service, mock_response):
        """Test callback handling with invalid state."""
        # Arrange
        callback_request = CallbackRequest(
            code="auth-code-123",
            state="invalid-state"
        )
        
        with patch('src.app.core.util.auth_utils.AuthUtils.verify_state') as mock_verify_state:
            mock_verify_state.return_value = False
            
            # Act & Assert
            with pytest.raises(ProcessingException) as exc_info:
                await auth_service.handle_callback(callback_request, mock_response)
            assert exc_info.value.message == "Invalid state parameter"
    
    @pytest.mark.asyncio
    async def test_handle_callback_with_error(self, auth_service, mock_response):
        """Test callback handling with OAuth2 error."""
        # Arrange
        callback_request = CallbackRequest(
            code="dummy-code",  # Required parameter even in error cases
            state="test-state",
            error="access_denied",
            error_description="User denied access"
        )
        
        with patch('src.app.core.util.auth_utils.AuthUtils.verify_state') as mock_verify_state:
            mock_verify_state.return_value = True
            
            # Act & Assert
            with pytest.raises(ProcessingException) as exc_info:
                await auth_service.handle_callback(callback_request, mock_response)
            assert "Authentication failed: User denied access" in exc_info.value.message
    
    @pytest.mark.asyncio
    async def test_refresh_token_success(self, auth_service, mock_keycloak_service, mock_request, mock_response):
        """Test successful token refresh."""
        # Arrange
        refresh_token = "refresh-token-123"
        realm_name = "test-realm"
        
        mock_request.cookies = {'securinest_refresh': refresh_token}
        
        refresh_data = {
            'access_token': 'new-access-token',
            'expires_in': 1800,
            'token_type': 'Bearer'
        }
        
        mock_keycloak_service.validate_token_and_extract_realm.return_value = realm_name
        mock_keycloak_service.refresh_access_token.return_value = refresh_data
        
        with patch('src.app.core.util.auth_utils.AuthUtils.get_cookie_settings') as mock_cookie_settings:
            mock_cookie_settings.return_value = {'secure': True, 'httponly': True}
            
            # Act
            result = await auth_service.refresh_token(mock_request, mock_response)
            
            # Assert
            assert result.access_token == "new-access-token"
            assert result.expires_in == 1800
            assert result.token_type == "Bearer"
            
            mock_response.set_cookie.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_refresh_token_no_cookie(self, auth_service, mock_request, mock_response):
        """Test token refresh with no refresh token cookie."""
        # Arrange
        mock_request.cookies = {}
        
        # Act & Assert
        with pytest.raises(Exception):  # Should raise HTTPException
            await auth_service.refresh_token(mock_request, mock_response)
    
    @pytest.mark.asyncio
    async def test_logout_user_success(self, auth_service, mock_keycloak_service, mock_request, mock_response):
        """Test successful user logout."""
        # Arrange
        refresh_token = "refresh-token-123"
        realm_name = "test-realm"
        
        mock_request.cookies = {'securinest_refresh': refresh_token}
        mock_keycloak_service.validate_token_and_extract_realm.return_value = realm_name
        mock_keycloak_service.logout_user.return_value = True
        mock_keycloak_service.build_logout_url.return_value = "https://keycloak.example.com/logout"
        
        logout_request = LogoutRequest(redirect_uri="https://app.example.com/login")
        
        with patch('src.app.core.util.auth_utils.AuthUtils.get_cookie_settings') as mock_cookie_settings:
            mock_cookie_settings.return_value = {'secure': True, 'httponly': True}
            
            # Act
            result = await auth_service.logout_user(mock_request, mock_response, logout_request)
            
            # Assert
            assert result.success is True
            assert result.message == "Successfully logged out"
            assert "keycloak.example.com" in result.logout_url
            
            mock_response.delete_cookie.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_logout_user_no_cookie(self, auth_service, mock_request, mock_response):
        """Test logout with no refresh token cookie."""
        # Arrange
        mock_request.cookies = {}
        logout_request = LogoutRequest()
        
        with patch('src.app.core.util.auth_utils.AuthUtils.get_cookie_settings') as mock_cookie_settings:
            mock_cookie_settings.return_value = {'secure': True, 'httponly': True}
            
            # Act
            result = await auth_service.logout_user(mock_request, mock_response, logout_request)
            
            # Assert
            assert result.success is True
            mock_response.delete_cookie.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_concurrent_login_requests(self, auth_service, mock_keycloak_service):
        """Test concurrent login requests for thread safety."""
        # Arrange
        tenant_ids = [f"tenant-{i}" for i in range(10)]
        mock_keycloak_service.get_realm_name_by_tenant_id.side_effect = lambda tid: f"realm-{tid}"
        mock_keycloak_service.build_authorization_url.return_value = "https://keycloak.example.com/auth"
        
        with patch('src.app.api.service.auth_service.session_state_manager') as mock_state_manager:
            mock_state_manager.store_state = AsyncMock()
            
            with patch('src.app.core.util.auth_utils.AuthUtils.generate_state') as mock_generate_state, \
                 patch('src.app.core.util.auth_utils.AuthUtils.generate_nonce') as mock_generate_nonce:
                
                mock_generate_state.side_effect = lambda: f"state-{asyncio.current_task().get_name()}"
                mock_generate_nonce.side_effect = lambda: f"nonce-{asyncio.current_task().get_name()}"
                
                # Act
                tasks = [
                    auth_service.initiate_login(LoginRequest(tenant_id=tid))
                    for tid in tenant_ids
                ]
                results = await asyncio.gather(*tasks)
                
                # Assert
                assert len(results) == 10
                for i, result in enumerate(results):
                    assert f"tenant-{i}" in str(mock_keycloak_service.get_realm_name_by_tenant_id.call_args_list[i])
                    assert result.authorization_url == "https://keycloak.example.com/auth"
