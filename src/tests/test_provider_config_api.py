"""
Test suite for ProviderConfig CRUD operations.

This file was auto-generated by test_generator.py
"""

import pytest


class TestProviderConfigCRUD:
    """Test suite for ProviderConfig CRUD operations."""

    @pytest.mark.asyncio
    async def test_create_provider_config_success(self, async_client, sample_provider_config_data):
        """Test successful provider_config creation."""
        response = await async_client.post("/provider_configs", json=sample_provider_config_data)

        assert response.status_code == 201
        data = response.json()

        assert "id" in data
        for key, value in sample_provider_config_data.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_create_provider_config_invalid_data(self, async_client):
        """Test provider_config creation with invalid data."""
        payload = {"invalid_field": "invalid_value"}

        response = await async_client.post("/provider_configs", json=payload)

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_provider_config_success(self, async_client, sample_provider_config_data):
        """Test successful provider_config retrieval."""
        create_payload = {**sample_provider_config_data, "default_model": "Test ProviderConfig Get"}
        create_response = await async_client.post("/provider_configs", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.get(f"/provider_configs/{created_id}")

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == created_id

    @pytest.mark.asyncio
    async def test_get_provider_config_not_found(self, async_client):
        """Test provider_config retrieval with non-existent ID."""
        response = await async_client.get(f"/provider_configs/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_provider_config_success(self, async_client, sample_provider_config_data):
        """Test successful provider_config update."""
        create_payload = {**sample_provider_config_data, "default_model": "Test ProviderConfig Update"}
        create_response = await async_client.post("/provider_configs", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        update_payload = {"default_model": "gpt-4-turbo"}
        response = await async_client.put(f"/provider_configs/{created_id}", json=update_payload)

        assert response.status_code == 200
        data = response.json()

        for key, value in update_payload.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_update_provider_config_not_found(self, async_client):
        """Test provider_config update with non-existent ID."""
        update_payload = {"default_model": "gpt-4-turbo"}
        response = await async_client.put(f"/provider_configs/99999", json=update_payload)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_provider_config_success(self, async_client, sample_provider_config_data):
        """Test successful provider_config deletion."""
        create_payload = {**sample_provider_config_data, "default_model": "Test ProviderConfig Delete"}
        create_response = await async_client.post("/provider_configs", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.delete(f"/provider_configs/{created_id}")

        assert response.status_code == 204

        get_response = await async_client.get(f"/provider_configs/{created_id}")
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_provider_config_not_found(self, async_client):
        """Test provider_config deletion with non-existent ID."""
        response = await async_client.delete(f"/provider_configs/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_provider_configs_success(self, async_client):
        """Test successful provider_config listing."""
        response = await async_client.get("/provider_configs")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
