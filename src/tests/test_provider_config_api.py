"""
Test suite for ProviderConfig CRUD operations.

This file was auto-generated by test_generator.py
"""

# Apply patches BEFORE any other imports
from unittest.mock import patch, AsyncMock, MagicMock
from functools import wraps

# Mock the require_roles decorator to bypass authentication (though provider config router doesn't use it)
def mock_require_roles(*roles):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Simply pass through - no authentication required in tests
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# Apply patches immediately
patch('src.app.core.security.keycloak_auth.require_roles', mock_require_roles).start()

import pytest

# Mock the ProviderConfig service to avoid database calls
class MockProviderConfigService:
    # Class variable to track deleted IDs across all instances
    deleted_ids = set()

    def __init__(self, session=None):
        self.session = session

    async def create_provider_config(self, payload):
        from src.app.api.schema.provider_config.provider_config_create_response import ProviderConfigCreateResponse

        return ProviderConfigCreateResponse(
            id=1,
            provider_type=payload.provider_type,
            credentials=payload.credentials,
            default_model=payload.default_model,
            tenant_id=1
        )

    async def get_provider_config_by_id(self, provider_config_id):
        from src.app.api.schema.provider_config.provider_config_read_response import ProviderConfigReadResponse
        from src.app.dao.entity.provider_type import ProviderType

        if provider_config_id == 99999 or provider_config_id in MockProviderConfigService.deleted_ids:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"ProviderConfig with id {provider_config_id} not found.")

        return ProviderConfigReadResponse(
            id=provider_config_id,
            provider_type=ProviderType.OPENAI,
            credentials="test-credentials",
            default_model="gpt-3.5-turbo",
            tenant_id=1
        )

    async def get_provider_configs(self):
        return []

    async def update_provider_config(self, provider_config_id, payload):
        from src.app.api.schema.provider_config.provider_config_update_response import ProviderConfigUpdateResponse
        from src.app.dao.entity.provider_type import ProviderType

        if provider_config_id == 99999 or provider_config_id in MockProviderConfigService.deleted_ids:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"ProviderConfig with id {provider_config_id} not found.")

        return ProviderConfigUpdateResponse(
            id=provider_config_id,
            provider_type=payload.provider_type or ProviderType.OPENAI,
            credentials=payload.credentials or "test-credentials",
            default_model=payload.default_model or "gpt-3.5-turbo",
            tenant_id=1
        )

    async def delete_provider_config(self, provider_config_id):
        if provider_config_id == 99999:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"ProviderConfig with id {provider_config_id} not found.")

        # Mark the provider config as deleted
        MockProviderConfigService.deleted_ids.add(provider_config_id)
        return None

# Patch the service before importing the router
patch('src.app.api.service.provider_config_service.ProviderConfigService', MockProviderConfigService).start()


class TestProviderConfigCRUD:
    """Test suite for ProviderConfig CRUD operations."""

    @pytest.mark.asyncio
    async def test_create_provider_config_success(self, async_client, sample_provider_config_data):
        """Test successful provider_config creation."""
        response = await async_client.post("/provider_configs", json=sample_provider_config_data)

        assert response.status_code == 201
        data = response.json()

        assert "id" in data
        for key, value in sample_provider_config_data.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_create_provider_config_invalid_data(self, async_client):
        """Test provider_config creation with invalid data."""
        payload = {"invalid_field": "invalid_value"}

        response = await async_client.post("/provider_configs", json=payload)

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_provider_config_success(self, async_client, sample_provider_config_data):
        """Test successful provider_config retrieval."""
        create_payload = {**sample_provider_config_data, "default_model": "Test ProviderConfig Get"}
        create_response = await async_client.post("/provider_configs", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.get(f"/provider_configs/{created_id}")

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == created_id

    @pytest.mark.asyncio
    async def test_get_provider_config_not_found(self, async_client):
        """Test provider_config retrieval with non-existent ID."""
        response = await async_client.get(f"/provider_configs/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_provider_config_success(self, async_client, sample_provider_config_data):
        """Test successful provider_config update."""
        create_payload = {**sample_provider_config_data, "default_model": "Test ProviderConfig Update"}
        create_response = await async_client.post("/provider_configs", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        update_payload = {"default_model": "gpt-4-turbo"}
        response = await async_client.put(f"/provider_configs/{created_id}", json=update_payload)

        assert response.status_code == 200
        data = response.json()

        for key, value in update_payload.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_update_provider_config_not_found(self, async_client):
        """Test provider_config update with non-existent ID."""
        update_payload = {"default_model": "gpt-4-turbo"}
        response = await async_client.put(f"/provider_configs/99999", json=update_payload)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_provider_config_success(self, async_client, sample_provider_config_data):
        """Test successful provider_config deletion."""
        create_payload = {**sample_provider_config_data, "default_model": "Test ProviderConfig Delete"}
        create_response = await async_client.post("/provider_configs", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.delete(f"/provider_configs/{created_id}")

        assert response.status_code == 204

        get_response = await async_client.get(f"/provider_configs/{created_id}")
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_provider_config_not_found(self, async_client):
        """Test provider_config deletion with non-existent ID."""
        response = await async_client.delete(f"/provider_configs/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_provider_configs_success(self, async_client):
        """Test successful provider_config listing."""
        response = await async_client.get("/provider_configs")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
