import pytest
import httpx
from unittest.mock import Mock, AsyncMock, patch
from fastapi import status

from src.app.api.schema.auth.login_request import LoginRequest
from src.app.api.schema.auth.login_response import LoginResponse
from src.app.api.schema.auth.callback_response import CallbackResponse
from src.app.api.schema.auth.refresh_token_response import RefreshTokenResponse
from src.app.api.schema.auth.logout_request import LogoutRequest
from src.app.api.schema.auth.logout_response import LogoutResponse
from src.app.core.exception.processing_exception import ProcessingException


class TestAuthRouter:
    """Test suite for authentication router endpoints."""
    
    @pytest.mark.asyncio
    async def test_initiate_login_success(self, async_client):
        """Test successful login initiation."""
        from src.main import app
        from src.app.api.service.auth_service import get_auth_service

        # Arrange
        login_request = {
            "tenant_id": "test-tenant-123",
            "redirect_uri": "https://app.example.com/dashboard"
        }

        expected_response = LoginResponse(
            authorization_url="http://localhost:8080/realms/test-realm/protocol/openid-connect/auth?client_id=securinest&response_type=code&scope=openid+profile+email&redirect_uri=http%3A//localhost%3A8000/auth/callback&state=test-state&nonce=test-nonce",
            state="test-state",
            realm_name="test-realm"
        )

        # Mock the auth service using FastAPI dependency override
        mock_service = AsyncMock()
        mock_service.initiate_login.return_value = expected_response

        app.dependency_overrides[get_auth_service] = lambda: mock_service

        try:
            # Act
            response = await async_client.post("/auth/login", json=login_request)

            # Assert
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["authorization_url"] == expected_response.authorization_url
            assert data["state"] == expected_response.state
            assert data["realm_name"] == expected_response.realm_name
        finally:
            # Clean up dependency override
            if get_auth_service in app.dependency_overrides:
                del app.dependency_overrides[get_auth_service]
    
    @pytest.mark.asyncio
    async def test_initiate_login_invalid_tenant(self, async_client):
        """Test login initiation with invalid tenant."""
        from src.main import app
        from src.app.api.service.auth_service import get_auth_service

        # Arrange
        login_request = {
            "tenant_id": "invalid-tenant"
        }

        # Mock the auth service using FastAPI dependency override
        mock_service = AsyncMock()
        mock_service.initiate_login.side_effect = ProcessingException("Tenant not found")

        app.dependency_overrides[get_auth_service] = lambda: mock_service

        try:
            # Act
            response = await async_client.post("/auth/login", json=login_request)

            # Assert
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            assert "Tenant not found" in response.json()["detail"]
        finally:
            # Clean up dependency override
            if get_auth_service in app.dependency_overrides:
                del app.dependency_overrides[get_auth_service]
    
    @pytest.mark.asyncio
    async def test_initiate_login_validation_error(self, async_client):
        """Test login initiation with validation error."""
        # Arrange
        invalid_request = {
            "tenant_id": ""  # Empty tenant ID should fail validation
        }
        
        # Act
        response = await async_client.post("/auth/login", json=invalid_request)
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_handle_callback_success(self, async_client):
        """Test successful callback handling."""
        from src.main import app
        from src.app.api.service.auth_service import get_auth_service

        # Arrange
        callback_params = {
            "code": "auth-code-123",
            "state": "test-state"
        }

        expected_response = CallbackResponse(
            access_token="access-token-123",
            token_type="Bearer",
            expires_in=1800,
            user_id="user-123",
            username="testuser",
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            realm="test-realm",
            tenant_id="test-tenant",
            roles=["TENANT_ADMIN"]
        )

        # Mock the auth service using FastAPI dependency override
        mock_service = AsyncMock()
        mock_service.handle_callback.return_value = expected_response

        app.dependency_overrides[get_auth_service] = lambda: mock_service

        try:
            # Act
            response = await async_client.get("/auth/callback", params=callback_params)

            # Assert
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["access_token"] == expected_response.access_token
            assert data["user_id"] == expected_response.user_id
            assert data["username"] == expected_response.username
            assert data["tenant_id"] == expected_response.tenant_id
        finally:
            # Clean up dependency override
            if get_auth_service in app.dependency_overrides:
                del app.dependency_overrides[get_auth_service]
    
    @pytest.mark.asyncio
    async def test_handle_callback_with_error(self, async_client):
        """Test callback handling with OAuth2 error."""
        from src.main import app
        from src.app.api.service.auth_service import get_auth_service

        # Arrange
        callback_params = {
            "code": "dummy-code",  # Required parameter even in error cases
            "error": "access_denied",
            "error_description": "User denied access",
            "state": "test-state"
        }

        # Mock the auth service using FastAPI dependency override
        mock_service = AsyncMock()
        mock_service.handle_callback.side_effect = ProcessingException("Authentication failed: User denied access")

        app.dependency_overrides[get_auth_service] = lambda: mock_service

        try:
            # Act
            response = await async_client.get("/auth/callback", params=callback_params)

            # Assert
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            assert "Authentication failed" in response.json()["detail"]
        finally:
            # Clean up dependency override
            if get_auth_service in app.dependency_overrides:
                del app.dependency_overrides[get_auth_service]
    
    @pytest.mark.asyncio
    async def test_handle_callback_missing_params(self, async_client):
        """Test callback handling with missing required parameters."""
        # Act
        response = await async_client.get("/auth/callback")
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_refresh_token_success(self, async_client):
        """Test successful token refresh."""
        from src.main import app
        from src.app.api.service.auth_service import get_auth_service

        # Arrange
        expected_response = RefreshTokenResponse(
            access_token="new-access-token",
            token_type="Bearer",
            expires_in=1800
        )

        # Mock the auth service using FastAPI dependency override
        mock_service = AsyncMock()
        mock_service.refresh_token.return_value = expected_response

        app.dependency_overrides[get_auth_service] = lambda: mock_service

        try:
            # Act
            response = await async_client.post("/auth/refresh", json={})

            # Assert
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["access_token"] == expected_response.access_token
            assert data["token_type"] == expected_response.token_type
            assert data["expires_in"] == expected_response.expires_in
        finally:
            # Clean up dependency override
            if get_auth_service in app.dependency_overrides:
                del app.dependency_overrides[get_auth_service]
    
    @pytest.mark.asyncio
    async def test_refresh_token_no_cookie(self, async_client):
        """Test token refresh with no refresh token cookie."""
        from src.main import app
        from src.app.api.service.auth_service import get_auth_service
        from fastapi import HTTPException

        # Mock the auth service using FastAPI dependency override
        mock_service = AsyncMock()
        mock_service.refresh_token.side_effect = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No refresh token available"
        )

        app.dependency_overrides[get_auth_service] = lambda: mock_service

        try:
            # Act
            response = await async_client.post("/auth/refresh", json={})

            # Assert
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            assert "No refresh token available" in response.json()["detail"]
        finally:
            # Clean up dependency override
            if get_auth_service in app.dependency_overrides:
                del app.dependency_overrides[get_auth_service]
    
    @pytest.mark.asyncio
    async def test_logout_user_success(self, async_client):
        """Test successful user logout."""
        from src.main import app
        from src.app.api.service.auth_service import get_auth_service

        # Arrange
        logout_request = {
            "redirect_uri": "https://app.example.com/login"
        }

        expected_response = LogoutResponse(
            success=True,
            message="Successfully logged out",
            logout_url="http://localhost:8080/realms/test-realm/protocol/openid-connect/logout?redirect_uri=https%3A//app.example.com/login"
        )

        # Mock the auth service using FastAPI dependency override
        mock_service = AsyncMock()
        mock_service.logout_user.return_value = expected_response

        app.dependency_overrides[get_auth_service] = lambda: mock_service

        try:
            # Act
            response = await async_client.post("/auth/logout", json=logout_request)

            # Assert
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["success"] is True
            assert data["message"] == expected_response.message
            assert data["logout_url"] == expected_response.logout_url
        finally:
            # Clean up dependency override
            if get_auth_service in app.dependency_overrides:
                del app.dependency_overrides[get_auth_service]
    
    @pytest.mark.asyncio
    async def test_logout_user_no_request_body(self, async_client):
        """Test logout without request body."""
        from src.main import app
        from src.app.api.service.auth_service import get_auth_service

        # Arrange
        expected_response = LogoutResponse(
            success=True,
            message="Successfully logged out",
            logout_url="https://keycloak.example.com/logout"
        )

        # Mock the auth service using FastAPI dependency override
        mock_service = AsyncMock()
        mock_service.logout_user.return_value = expected_response

        app.dependency_overrides[get_auth_service] = lambda: mock_service

        try:
            # Act
            response = await async_client.post("/auth/logout", json={})

            # Assert
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["success"] is True
        finally:
            # Clean up dependency override
            if get_auth_service in app.dependency_overrides:
                del app.dependency_overrides[get_auth_service]
    
    @pytest.mark.asyncio
    async def test_logout_user_with_error_fallback(self, async_client):
        """Test logout with error but successful fallback."""
        from src.main import app
        from src.app.api.service.auth_service import get_auth_service

        # Mock the auth service using FastAPI dependency override
        mock_service = AsyncMock()
        mock_service.logout_user.side_effect = Exception("Unexpected error")

        app.dependency_overrides[get_auth_service] = lambda: mock_service

        try:
            # Act
            response = await async_client.post("/auth/logout", json={})

            # Assert
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["success"] is False
            assert "errors" in data["message"]
        finally:
            # Clean up dependency override
            if get_auth_service in app.dependency_overrides:
                del app.dependency_overrides[get_auth_service]
    
    @pytest.mark.asyncio
    async def test_auth_health_check_success(self, async_client):
        """Test authentication health check."""
        # Arrange
        mock_cache_stats = {
            "total_entries": 5,
            "max_size": 10000,
            "ttl_minutes": 10,
            "oldest_entry_age_seconds": 120,
            "newest_entry_age_seconds": 30
        }
        
        with patch('src.app.core.util.session_state_manager.session_state_manager') as mock_manager:
            mock_manager.get_cache_stats = AsyncMock(return_value=mock_cache_stats)
            
            # Act
            response = await async_client.get("/auth/health")
            
            # Assert
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] == "healthy"
            assert data["service"] == "authentication"
            assert data["session_state_manager"] == mock_cache_stats
            assert "operational" in data["message"]
    
    @pytest.mark.asyncio
    async def test_auth_health_check_failure(self, async_client):
        """Test authentication health check failure."""
        # Arrange
        with patch('src.app.core.util.session_state_manager.session_state_manager') as mock_manager:
            mock_manager.get_cache_stats.side_effect = Exception("Cache error")
            
            # Act
            response = await async_client.get("/auth/health")
            
            # Assert
            assert response.status_code == status.HTTP_503_SERVICE_UNAVAILABLE
            assert "health check failed" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_endpoint_security_headers(self, async_client):
        """Test that endpoints don't expose sensitive information in headers."""
        # Act
        response = await async_client.post("/auth/login", json={"tenant_id": "test"})
        
        # Assert
        # Should not expose internal server information
        assert "X-Powered-By" not in response.headers
        assert "Server" not in response.headers or "FastAPI" not in response.headers.get("Server", "")
    
    @pytest.mark.asyncio
    async def test_cors_and_security_considerations(self, async_client):
        """Test CORS and security considerations for auth endpoints."""
        # This test would be more comprehensive with actual CORS middleware
        # For now, just verify basic response structure
        
        # Act
        response = await async_client.options("/auth/login")
        
        # Assert
        # Should handle OPTIONS requests (for CORS preflight)
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_405_METHOD_NOT_ALLOWED]
    
    @pytest.mark.asyncio
    async def test_rate_limiting_considerations(self, async_client):
        """Test considerations for rate limiting on auth endpoints."""
        # This is a placeholder for rate limiting tests
        # In a real implementation, you'd test rate limiting middleware
        
        # Make multiple requests rapidly
        responses = []
        for _ in range(5):
            response = await async_client.post("/auth/login", json={"tenant_id": "test"})
            responses.append(response.status_code)
        
        # All should be processed (no rate limiting in test environment)
        # In production, you'd expect some 429 responses after a threshold
        assert all(status_code in [200, 400, 422, 500] for status_code in responses)
