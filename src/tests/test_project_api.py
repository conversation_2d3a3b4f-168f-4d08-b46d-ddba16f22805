"""
Test suite for Project CRUD operations.

This file was auto-generated by test_generator.py
"""

import pytest


class TestProjectCRUD:
    """Test suite for Project CRUD operations."""

    @pytest.mark.asyncio
    async def test_create_project_success(self, async_client, sample_project_data):
        """Test successful project creation."""
        response = await async_client.post("/projects", json=sample_project_data)

        assert response.status_code == 201
        data = response.json()

        assert "id" in data
        for key, value in sample_project_data.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_create_project_invalid_data(self, async_client):
        """Test project creation with invalid data."""
        payload = {"invalid_field": "invalid_value"}

        response = await async_client.post("/projects", json=payload)

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_project_success(self, async_client, sample_project_data):
        """Test successful project retrieval."""
        create_payload = {**sample_project_data, "name": "Test Project Get"}
        create_response = await async_client.post("/projects", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.get(f"/projects/{created_id}")

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == created_id

    @pytest.mark.asyncio
    async def test_get_project_not_found(self, async_client):
        """Test project retrieval with non-existent ID."""
        response = await async_client.get(f"/projects/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_project_success(self, async_client, sample_project_data):
        """Test successful project update."""
        create_payload = {**sample_project_data, "name": "Test Project Update"}
        create_response = await async_client.post("/projects", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        update_payload = {"name": "Updated Test Project", "description": "Updated description"}
        response = await async_client.put(f"/projects/{created_id}", json=update_payload)

        assert response.status_code == 200
        data = response.json()

        for key, value in update_payload.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_update_project_not_found(self, async_client):
        """Test project update with non-existent ID."""
        update_payload = {"name": "Updated Test Project", "description": "Updated description"}
        response = await async_client.put(f"/projects/99999", json=update_payload)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_project_success(self, async_client, sample_project_data):
        """Test successful project deletion."""
        create_payload = {**sample_project_data, "name": "Test Project Delete"}
        create_response = await async_client.post("/projects", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.delete(f"/projects/{created_id}")

        assert response.status_code == 204

        get_response = await async_client.get(f"/projects/{created_id}")
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_project_not_found(self, async_client):
        """Test project deletion with non-existent ID."""
        response = await async_client.delete(f"/projects/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_projects_success(self, async_client):
        """Test successful project listing."""
        response = await async_client.get("/projects")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
