"""
Test suite for Project CRUD operations.

This file was auto-generated by test_generator.py
"""

# Apply patches BEFORE any other imports
from unittest.mock import patch, AsyncMock, MagicMock
from functools import wraps

# Mock the require_roles decorator to bypass authentication (though project router doesn't use it)
def mock_require_roles(*roles):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Simply pass through - no authentication required in tests
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# Apply patches immediately
patch('src.app.core.security.keycloak_auth.require_roles', mock_require_roles).start()

import pytest

# Mock the Project service to avoid database calls
class MockProjectService:
    # Class variable to track deleted IDs across all instances
    deleted_ids = set()

    def __init__(self, session=None):
        self.session = session

    async def create_project(self, payload):
        from src.app.api.schema.project.project_create_response import ProjectCreateResponse

        return ProjectCreateResponse(
            id=1,
            name=payload.name,
            description=payload.description,
            tenant_id=1
        )

    async def get_project_by_id(self, project_id):
        from src.app.api.schema.project.project_read_response import ProjectReadResponse

        if project_id == 99999 or project_id in MockProjectService.deleted_ids:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"Project with id {project_id} not found.")

        return ProjectReadResponse(
            id=project_id,
            name="Test Project",
            description="A test project for API testing",
            tenant_id=1
        )

    async def get_projects(self):
        return []

    async def update_project(self, project_id, payload):
        from src.app.api.schema.project.project_update_response import ProjectUpdateResponse

        if project_id == 99999 or project_id in MockProjectService.deleted_ids:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"Project with id {project_id} not found.")

        return ProjectUpdateResponse(
            id=project_id,
            name=payload.name or "Test Project",
            description=payload.description or "A test project for API testing",
            tenant_id=1
        )

    async def delete_project(self, project_id):
        if project_id == 99999:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"Project with id {project_id} not found.")

        # Mark the project as deleted
        MockProjectService.deleted_ids.add(project_id)
        return None

# Patch the service before importing the router
patch('src.app.api.service.project_service.ProjectService', MockProjectService).start()


class TestProjectCRUD:
    """Test suite for Project CRUD operations."""

    @pytest.mark.asyncio
    async def test_create_project_success(self, async_client, sample_project_data):
        """Test successful project creation."""
        response = await async_client.post("/projects", json=sample_project_data)

        assert response.status_code == 201
        data = response.json()

        assert "id" in data
        for key, value in sample_project_data.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_create_project_invalid_data(self, async_client):
        """Test project creation with invalid data."""
        payload = {"invalid_field": "invalid_value"}

        response = await async_client.post("/projects", json=payload)

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_project_success(self, async_client, sample_project_data):
        """Test successful project retrieval."""
        create_payload = {**sample_project_data, "name": "Test Project Get"}
        create_response = await async_client.post("/projects", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.get(f"/projects/{created_id}")

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == created_id

    @pytest.mark.asyncio
    async def test_get_project_not_found(self, async_client):
        """Test project retrieval with non-existent ID."""
        response = await async_client.get(f"/projects/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_project_success(self, async_client, sample_project_data):
        """Test successful project update."""
        create_payload = {**sample_project_data, "name": "Test Project Update"}
        create_response = await async_client.post("/projects", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        update_payload = {"name": "Updated Test Project", "description": "Updated description"}
        response = await async_client.put(f"/projects/{created_id}", json=update_payload)

        assert response.status_code == 200
        data = response.json()

        for key, value in update_payload.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_update_project_not_found(self, async_client):
        """Test project update with non-existent ID."""
        update_payload = {"name": "Updated Test Project", "description": "Updated description"}
        response = await async_client.put(f"/projects/99999", json=update_payload)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_project_success(self, async_client, sample_project_data):
        """Test successful project deletion."""
        create_payload = {**sample_project_data, "name": "Test Project Delete"}
        create_response = await async_client.post("/projects", json=create_payload)
        assert create_response.status_code == 201
        created_id = create_response.json()["id"]

        response = await async_client.delete(f"/projects/{created_id}")

        assert response.status_code == 204

        get_response = await async_client.get(f"/projects/{created_id}")
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_project_not_found(self, async_client):
        """Test project deletion with non-existent ID."""
        response = await async_client.delete(f"/projects/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_projects_success(self, async_client):
        """Test successful project listing."""
        response = await async_client.get("/projects")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
