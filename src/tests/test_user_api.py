"""
Test suite for User CRUD operations.

This file was auto-generated by test_generator.py
"""

import pytest


class TestUserCRUD:
    """Test suite for User CRUD operations."""

    @pytest.mark.asyncio
    async def test_create_user_success(self, async_client, sample_user_data, mock_keycloak_auth):
        """Test successful user creation."""
        response = await async_client.post("/users", json=sample_user_data)

        assert response.status_code == 201
        data = response.json()

        assert "id" in data
        for key, value in sample_user_data.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_create_user_invalid_data(self, async_client, mock_keycloak_auth):
        """Test user creation with invalid data."""
        payload = {"invalid_field": "invalid_value"}

        response = await async_client.post("/users", json=payload)

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_user_success(self, async_client, sample_user_data, mock_keycloak_auth):
        """Test successful user retrieval."""
        create_payload = {**sample_user_data}
        if "name" in sample_user_data:
            create_payload["name"] = "Test User Get"
        if "email" in create_payload:
            import time
            create_payload["email"] = "get_test_" + str(int(time.time())) + "@example.com"
        create_response = await async_client.post("/users", json=create_payload)
        assert create_response.status_code == 201

        created_id = create_response.json()["id"]

        response = await async_client.get(f"/users/{created_id}")

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == created_id

    @pytest.mark.asyncio
    async def test_get_user_not_found(self, async_client, mock_keycloak_auth):
        """Test user retrieval with non-existent ID."""
        response = await async_client.get(f"/users/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_user_success(self, async_client, sample_user_data, mock_keycloak_auth):
        """Test successful user update."""
        create_payload = {**sample_user_data}
        if "name" in sample_user_data:
            create_payload["name"] = "Test User Update"
        if "email" in create_payload:
            import time
            create_payload["email"] = "update_test_" + str(int(time.time())) + "@example.com"
        create_response = await async_client.post("/users", json=create_payload)
        assert create_response.status_code == 201

        created_id = create_response.json()["id"]

        update_payload = {"first_name": "Updated", "last_name": "User"}
        response = await async_client.patch(f"/users/{created_id}", json=update_payload)

        assert response.status_code == 200
        data = response.json()

        for key, value in update_payload.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_update_user_not_found(self, async_client, mock_keycloak_auth):
        """Test user update with non-existent ID."""
        update_payload = {"first_name": "Updated", "last_name": "User"}
        response = await async_client.patch(f"/users/99999", json=update_payload)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_user_success(self, async_client, sample_user_data, mock_keycloak_auth):
        """Test successful user deletion."""
        create_payload = {**sample_user_data}
        if "name" in sample_user_data:
            create_payload["name"] = "Test User Delete"
        if "email" in create_payload:
            import time
            create_payload["email"] = "delete_test_" + str(int(time.time())) + "@example.com"
        create_response = await async_client.post("/users", json=create_payload)
        assert create_response.status_code == 201

        created_id = create_response.json()["id"]

        response = await async_client.delete(f"/users/{created_id}")

        assert response.status_code == 204

        get_response = await async_client.get(f"/users/{created_id}")
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_user_not_found(self, async_client, mock_keycloak_auth):
        """Test user deletion with non-existent ID."""
        response = await async_client.delete(f"/users/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_users_success(self, async_client, mock_keycloak_auth):
        """Test successful user listing."""
        response = await async_client.get("/users")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
