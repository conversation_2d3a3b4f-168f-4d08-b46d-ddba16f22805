"""
Test suite for User CRUD operations.

This file was auto-generated by test_generator.py
"""

# Apply patches BEFORE any other imports
from unittest.mock import patch, AsyncMock, MagicMock
from functools import wraps

# Mock the require_roles decorator to bypass authentication
def mock_require_roles(*roles):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Simply pass through - no authentication required in tests
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# Apply patches immediately
patch('src.app.core.security.keycloak_auth.require_roles', mock_require_roles).start()

import pytest

# Mock the User service to avoid database calls
class MockUserService:
    # Class variable to track deleted IDs across all instances
    deleted_ids = set()

    def __init__(self, session=None):
        self.session = session

    async def create_user(self, payload, current_user):
        from src.app.api.schema.user.user_create_response import UserCreateResponse
        from src.app.core.constant.constants import User<PERSON>ole
        from datetime import datetime, timezone

        now = datetime.now(timezone.utc)
        return UserCreateResponse(
            id=1,
            email=payload.email,
            first_name=payload.first_name,
            last_name=payload.last_name,
            role=payload.role,
            is_active=payload.is_active,
            created_at=now,
            updated_at=now
        )

    async def get_user_by_id(self, user_id, current_user):
        from src.app.api.schema.user.user_read_response import UserReadResponse
        from src.app.core.constant.constants import UserRole
        from datetime import datetime, timezone

        if user_id == 99999 or user_id in MockUserService.deleted_ids:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"User with id {user_id} not found.")

        now = datetime.now(timezone.utc)
        return UserReadResponse(
            id=user_id,
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            role=UserRole.DEVELOPER,
            is_active=True,
            tenant_id=1,
            created_at=now,
            updated_at=now
        )

    async def get_users(self):
        return []

    async def update_user(self, user_id, payload):
        from src.app.api.schema.user.user_update_response import UserUpdateResponse
        from src.app.core.constant.constants import UserRole
        from datetime import datetime, timezone

        if user_id == 99999 or user_id in MockUserService.deleted_ids:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"User with id {user_id} not found.")

        now = datetime.now(timezone.utc)
        return UserUpdateResponse(
            email=payload.email or "<EMAIL>",
            first_name=payload.first_name or "Test",
            last_name=payload.last_name or "User",
            role=payload.role or UserRole.DEVELOPER,
            is_active=payload.is_active if payload.is_active is not None else True,
            created_at=now,
            updated_at=now
        )

    async def delete_user(self, user_id):
        if user_id == 99999:
            from src.app.core.exception.not_found_exception import NotFoundException
            raise NotFoundException(f"User with id {user_id} not found.")

        # Mark the user as deleted
        MockUserService.deleted_ids.add(user_id)
        return None

# Patch the service before importing the router
patch('src.app.api.service.user_service.UserService', MockUserService).start()


class TestUserCRUD:
    """Test suite for User CRUD operations."""

    @pytest.mark.asyncio
    async def test_create_user_success(self, async_client, sample_user_data, mock_keycloak_auth):
        """Test successful user creation."""
        response = await async_client.post("/users", json=sample_user_data)

        assert response.status_code == 201
        data = response.json()

        assert "id" in data
        for key, value in sample_user_data.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_create_user_invalid_data(self, async_client, mock_keycloak_auth):
        """Test user creation with invalid data."""
        payload = {"invalid_field": "invalid_value"}

        response = await async_client.post("/users", json=payload)

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_user_success(self, async_client, sample_user_data, mock_keycloak_auth):
        """Test user retrieval - works because it has current_user parameter."""
        # Skip creation since it requires authentication
        # Test retrieval directly with a known ID
        response = await async_client.get(f"/users/1")

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == 1

    @pytest.mark.asyncio
    async def test_get_user_not_found(self, async_client, mock_keycloak_auth):
        """Test user retrieval with non-existent ID."""
        response = await async_client.get(f"/users/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_user_success(self, async_client, sample_user_data, mock_keycloak_auth):
        """Test user update - works because no @require_roles decorator."""
        # Skip creation since it requires authentication
        # Test update directly with a known ID
        update_payload = {"first_name": "Updated", "last_name": "User"}
        response = await async_client.patch(f"/users/1", json=update_payload)

        assert response.status_code == 200
        data = response.json()

        for key, value in update_payload.items():
            if key in data:
                assert data[key] == value

    @pytest.mark.asyncio
    async def test_update_user_not_found(self, async_client, mock_keycloak_auth):
        """Test user update with non-existent ID."""
        update_payload = {"first_name": "Updated", "last_name": "User"}
        response = await async_client.patch(f"/users/99999", json=update_payload)

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_user_success(self, async_client, sample_user_data, mock_keycloak_auth):
        """Test user deletion - works because no @require_roles decorator."""
        # Skip creation since it requires authentication
        # Test deletion directly with a known ID
        response = await async_client.delete(f"/users/1")

        assert response.status_code == 204

        # Verify the user is deleted by trying to get it
        get_response = await async_client.get(f"/users/1")
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_user_not_found(self, async_client, mock_keycloak_auth):
        """Test user deletion with non-existent ID."""
        response = await async_client.delete(f"/users/99999")

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_users_success(self, async_client, mock_keycloak_auth):
        """Test successful user listing."""
        response = await async_client.get("/users")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
