"""
Test configuration and utilities for async database testing.

This module provides robust async test infrastructure that properly handles
database connections and transactions to avoid connection pool conflicts.
"""

import os
from typing import AsyncGenerator
from contextlib import asynccontextmanager

import pytest
import pytest_asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, AsyncEngine, async_sessionmaker

from src.main import app
from src.app.core.db_connection.db_connector import get_session
from src.app.core.database.seeder import seed_database


class DatabaseTestManager:
    """Manages test database connections and transactions."""

    def __init__(self):
        self._seeded = False

    def get_test_engine(self) -> AsyncEngine:
        """Get or create a test-specific database engine for each test."""
        # Create a new engine for each test to avoid connection sharing issues
        database_url = os.getenv("DATABASE_URL")
        if not database_url:
            raise RuntimeError("DATABASE_URL not set")

        from sqlalchemy.ext.asyncio import create_async_engine
        return create_async_engine(
            database_url,
            echo=False,  # Reduce noise in tests
            future=True,
            pool_pre_ping=False,  # Disable ping to avoid event loop issues
            pool_recycle=-1,  # Disable connection recycling
            # Use minimal connection pool for tests
            pool_size=1,
            max_overflow=0,
            connect_args={
                "server_settings": {
                    "application_name": "securinest_test",
                }
            }
        )

    @asynccontextmanager
    async def get_test_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get a test database session with automatic rollback."""
        engine = self.get_test_engine()
        session_maker = async_sessionmaker(
            bind=engine,
            expire_on_commit=False,
            class_=AsyncSession,
        )

        async with session_maker() as session:
            # Start a transaction
            transaction = await session.begin()
            try:
                yield session
            finally:
                # Always rollback to keep tests isolated
                if transaction.is_active:
                    await transaction.rollback()
                # Close the engine to prevent connection leaks
                await engine.dispose()

    async def seed_test_database(self):
        """Seed the test database if not already seeded."""
        if not self._seeded:
            try:
                await seed_database()
                self._seeded = True
            except Exception as e:
                pytest.fail(f"Failed to seed test database: {e}")

    async def cleanup(self):
        """Clean up test database resources."""
        self._seeded = False


# Global test database manager
test_db_manager = DatabaseTestManager()


@pytest_asyncio.fixture(scope="session")
async def setup_test_database():
    """Set up test database with seeded data."""
    try:
        await test_db_manager.seed_test_database()
        yield test_db_manager
    finally:
        await test_db_manager.cleanup()


@pytest_asyncio.fixture
async def db_session(setup_test_database):
    """Provide an isolated database session for tests."""
    async with setup_test_database.get_test_session() as session:
        yield session


@pytest_asyncio.fixture
async def async_client():
    """Provide an async HTTP client for testing FastAPI endpoints."""
    from httpx import ASGITransport

    # Ensure database dependency is overridden before creating client
    override_get_session()

    try:
        transport = ASGITransport(app=app)
        async with AsyncClient(transport=transport, base_url="http://test") as client:
            yield client
    finally:
        # Clean up dependency override
        restore_get_session()


# Override the app's database dependency for testing
async def get_test_session_dependency():
    """Database dependency override for testing."""
    async with test_db_manager.get_test_session() as session:
        yield session


def override_get_session():
    """Override the get_session dependency for testing."""
    app.dependency_overrides[get_session] = get_test_session_dependency


def restore_get_session():
    """Restore the original get_session dependency."""
    if get_session in app.dependency_overrides:
        del app.dependency_overrides[get_session]


@pytest.fixture(autouse=True)
def setup_test_dependencies():
    """Automatically override dependencies for all tests."""
    override_get_session()
    yield
    restore_get_session()
