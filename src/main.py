from fastapi import FastAPI

from src.app.core.config.log_config import configure_application_logging
configure_application_logging()

from src.app.api.router import (
    tenant_router, api_key_router, log_entry_router, user_router, notification_router,
    policy_router, project_router, rule_router, provider_config_router, keycloak_router,
    auth_router, payment_router, plan_router, subscription_router, usage_record_router
)
from src.app.core.exception_handler.exception_handler import init_exception_handlers

app = FastAPI()
app.include_router(auth_router.router)
app.include_router(api_key_router.router)
app.include_router(log_entry_router.router)
app.include_router(user_router.router)
app.include_router(notification_router.router)
app.include_router(payment_router.router)
app.include_router(plan_router.router)
app.include_router(policy_router.router)
app.include_router(subscription_router.router)
app.include_router(tenant_router.router)
app.include_router(project_router.router)
app.include_router(rule_router.router)
app.include_router(usage_record_router.router)
app.include_router(provider_config_router.router)
app.include_router(keycloak_router.router)
init_exception_handlers(app)


@app.get("/")
def root():
    return {"message": "Hello, World!"}
