# Database Management Guide

This guide covers database schema management, migrations, and seeding for the SecuriNest FastAPI application.

## 🎯 Overview

Our database management system provides:
- **Alembic migrations** for schema changes
- **Idempotent seeding** for sample data
- **Management utilities** for common operations
- **Development workflow** integration

## 📋 Quick Start

### 1. Run Migrations
```bash
# Apply all pending migrations
python scripts/db_manager.py migrate

# Check migration status
python scripts/db_manager.py status
```

### 2. Seed Database
```bash
# Seed with sample data (idempotent)
python scripts/db_manager.py seed

# Or use the simple script
python scripts/seed_database.py
```

### 3. Reset Database (Development)
```bash
# ⚠️ WARNING: This deletes ALL data!
python scripts/db_manager.py reset
```

## 🔧 Database Manager Commands

The `scripts/db_manager.py` script provides these commands:

| Command | Description | Example | Use Case |
|---------|-------------|---------|----------|
| `migrate` | Apply all pending Alembic migrations to update database schema | `python scripts/db_manager.py migrate` | After pulling schema changes from git |
| `seed` | Populate database with sample data (idempotent - safe to run multiple times) | `python scripts/db_manager.py seed` | Setting up development environment |
| `reset` | Drop all tables, run migrations, and seed fresh data | `python scripts/db_manager.py reset` | Starting completely fresh (⚠️ DELETES ALL DATA) |
| `status` | Display current migration status and pending migrations | `python scripts/db_manager.py status` | Checking if database is up to date |
| `create` | Generate new migration file from entity model changes | `python scripts/db_manager.py create "add user field"` | After modifying entity models |
| `help` | Display detailed help information and usage examples | `python scripts/db_manager.py help` | Learning available commands |

## 🌱 Database Seeding

### Sample Data Created

The seeding system creates 2 sample records for each entity:

#### **Tenants**
- Acme Corporation (Main enterprise tenant)
- Beta Industries (Secondary test tenant)

#### **Users**
- admin_user (Admin role)
- dev_user (Moderator role)

#### **API Keys**
- Development API Key
- Testing API Key

#### **Projects**
- Web Application Security
- API Security Framework

#### **Policies**
- Web Security Policy v1.0 (Active)
- API Security Policy v1.0 (Draft)

#### **Rules**
- SQL Injection Detection Rule
- XSS Prevention Rule
- Authentication Required Rule
- Rate Limiting Rule

#### **Provider Configs**
- OpenAI Configuration (GPT-4)
- Anthropic Configuration (Claude-3-Sonnet)

#### **Notifications**
- Security violation alert
- System alert notification

#### **Log Entries**
- SQL injection attempt (blocked)
- Normal query processing (allowed)

### Idempotent Design

The seeding system is **idempotent** - you can run it multiple times safely:
- Checks for existing records before creating
- Uses unique identifiers to prevent duplicates
- Logs what was created vs. what already existed

## 🔄 Development Workflow

### Making Schema Changes

1. **Modify Entity Models**
   ```python
   # Example: Add field to User entity
   class User(SQLModel, table=True):
       # ... existing fields ...
       new_field: Optional[str] = None
   ```

2. **Generate Migration**
   ```bash
   python scripts/db_manager.py create "add new_field to user"
   ```

3. **Review Migration**
   - Check the generated migration file in `src/app/migrations/versions/`
   - Verify the upgrade/downgrade logic

4. **Apply Migration**
   ```bash
   python scripts/db_manager.py migrate
   ```

5. **Update Seeding (if needed)**
   - Modify `src/app/core/database/seeder.py` if new fields need sample data

6. **Test with Fresh Data**
   ```bash
   python scripts/db_manager.py reset  # ⚠️ Development only!
   ```

### Practical Examples

#### **Daily Development Workflow**
```bash
# 1. Start your day - check if database is current
python scripts/db_manager.py status

# 2. If migrations are pending, apply them
python scripts/db_manager.py migrate

# 3. If you need fresh test data
python scripts/db_manager.py seed
```

#### **After Pulling Code Changes**
```bash
# Check what changed
python scripts/db_manager.py status

# Apply any new migrations
python scripts/db_manager.py migrate

# Refresh test data if needed
python scripts/db_manager.py seed
```

#### **Working on New Features**
```bash
# 1. Modify your entity models (e.g., add field to User)
# 2. Generate migration
python scripts/db_manager.py create "add phone_number to user"

# 3. Review the generated migration file
# 4. Apply the migration
python scripts/db_manager.py migrate

# 5. Update seeding if new fields need sample data
# 6. Test with fresh data
python scripts/db_manager.py reset  # ⚠️ Only in development!
```

#### **Troubleshooting Database Issues**
```bash
# Check current state
python scripts/db_manager.py status

# If database is corrupted or you want to start fresh
python scripts/db_manager.py reset

# If you just want to refresh test data
python scripts/db_manager.py seed
```

### Best Practices

✅ **DO:**
- Always review generated migrations before applying
- Test migrations on a copy of production data
- Use descriptive migration messages
- Keep seeding data realistic but safe
- Run seeding after schema changes

❌ **DON'T:**
- Use `reset` command in production
- Modify existing migration files after they're applied
- Include sensitive data in seeding
- Skip migration reviews

## 🚀 Production Deployment

### Migration Strategy

1. **Backup Database**
   ```bash
   pg_dump securinest > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **Test Migration**
   ```bash
   # On staging environment
   alembic upgrade head
   ```

3. **Deploy to Production**
   ```bash
   # In production environment
   alembic upgrade head
   ```

### Seeding in Production

**⚠️ Important:** Never run seeding scripts in production!

For production data:
- Use proper data migration scripts
- Import real data through APIs
- Use database-specific import tools

## 🔧 Configuration

### Environment Variables

```bash
# Optional: Auto-seed on application startup (development only)
AUTO_SEED_DATABASE=true

# Database connection (already configured in alembic.ini)
DATABASE_URL=postgresql+asyncpg://user:pass@localhost:5432/securinest
```

### Application Integration

To enable auto-seeding on startup (development only):

```python
# In main.py
from src.app.core.startup import configure_startup_seeding

app = FastAPI()

# Add startup event
startup_func = configure_startup_seeding()
if startup_func:
    app.add_event_handler("startup", startup_func)
```

## 🐛 Troubleshooting

### Common Issues

**Migration Conflicts**
```bash
# If you have migration conflicts
alembic merge heads -m "merge migrations"
```

**Seeding Failures**
```bash
# Check database connection
python scripts/db_manager.py status

# Check logs for specific errors
python scripts/db_manager.py seed
```

**Schema Out of Sync**
```bash
# Generate new migration to sync
python scripts/db_manager.py create "sync schema"
```

### Getting Help

```bash
# Show available commands
python scripts/db_manager.py help

# Check Alembic help
alembic --help
```

## 📁 File Structure

```
├── scripts/
│   ├── db_manager.py          # Main database management utility
│   └── seed_database.py       # Simple seeding script
├── src/app/
│   ├── core/
│   │   ├── database/
│   │   │   └── seeder.py      # Database seeding logic
│   │   └── startup.py         # Optional startup integration
│   ├── migrations/            # Alembic migrations
│   └── dao/entity/           # Database entities
└── docs/
    └── database_management.md # This guide
```

This system provides a robust, developer-friendly approach to database management that scales from development to production! 🚀
