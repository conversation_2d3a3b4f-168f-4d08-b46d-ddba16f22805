# Development Utilities Guide

This guide covers the advanced development utilities that extend your existing workflow and integrate seamlessly with your SecuriNest architecture.

## 🎯 Overview

Three powerful utilities have been implemented to streamline your development workflow:

1. **Enhanced Database Manager** (`scripts/enhanced_db_manager.py`) - Advanced database operations
2. **API Test Generator** (`scripts/test_generator.py`) - Automated test suite creation
3. **Development Environment Manager** (`scripts/dev_manager.py`) - One-command environment management

## 🔧 Enhanced Database Manager

### Integration with Existing Architecture
- **Extends your existing `db_manager.py`** - Builds on the same command patterns and structure
- **Uses your database connection patterns** - Leverages `db_connector.py` and environment variables
- **Integrates with your seeding system** - Works with your idempotent seeding approach
- **Follows your logging patterns** - Uses consistent logging format and error handling

### Key Features & Time Savings

#### Backup & Restore Operations
```bash
# Create timestamped backup
python scripts/enhanced_db_manager.py backup
# Time saved: 5-10 minutes of manual pg_dump commands

# Create named backup before major changes
python scripts/enhanced_db_manager.py backup "before_auth_feature"

# Restore from backup
python scripts/enhanced_db_manager.py restore backup_20241201_143022.sql
# Time saved: 10-15 minutes of manual restoration process
```

#### Schema Analysis & Drift Detection
```bash
# Compare current DB schema with your SQLModel definitions
python scripts/enhanced_db_manager.py compare-schema
# Time saved: 15-20 minutes of manual schema inspection
```

#### Data Export & Analysis
```bash
# Export table data for analysis or migration
python scripts/enhanced_db_manager.py export-data users
# Time saved: 10-15 minutes of manual SQL queries and formatting
```

#### Health Monitoring
```bash
# Comprehensive database health check
python scripts/enhanced_db_manager.py health-check
# Time saved: 20-30 minutes of manual health verification

# Performance analysis
python scripts/enhanced_db_manager.py performance
# Time saved: 15-25 minutes of manual performance queries
```

### **Total Time Savings: 1-2 hours per week** for database management tasks

## 🧪 API Test Generator

### Integration with Existing Architecture
- **Follows your Pydantic schema patterns** - Uses your `*_create_request.py`, `*_create_response.py` naming conventions
- **Leverages your seeding data** - Creates test fixtures using your existing `seed_database()` function
- **Uses your TestClient patterns** - Follows the same structure as your existing `test_main.py`
- **Integrates with your route constants** - Uses `ResourceRoute`, `ResourceTag`, and `PathParam` enums
- **Respects your service patterns** - Tests follow your dependency injection and service layer architecture

### Key Features & Time Savings

#### Automated Test Generation
```bash
# Generate tests for all entities
python scripts/test_generator.py generate-all
# Time saved: 3-4 hours of manual test writing for 8 entities

# Generate tests for specific entity
python scripts/test_generator.py generate users
# Time saved: 20-30 minutes per entity

# Generate tests with authentication
python scripts/test_generator.py generate policies --include-auth
# Time saved: Additional 10-15 minutes for auth test cases
```

#### Comprehensive Test Coverage
Each generated test file includes:
- ✅ **CRUD Operations**: Create, Read, Update, Delete tests
- ✅ **Validation Testing**: Invalid data and edge cases
- ✅ **Error Handling**: 404, 422, and other error scenarios
- ✅ **Schema Validation**: Response structure verification
- ✅ **Integration with Seeding**: Uses your existing test data

#### Test Infrastructure
```bash
# Generate pytest configuration with shared fixtures
python scripts/test_generator.py generate-conftest
# Time saved: 30-45 minutes of pytest setup and fixture creation
```

### **Total Time Savings: 4-6 hours** for comprehensive test suite creation

## 🚀 Development Environment Manager

### Integration with Existing Architecture
- **Uses your database management patterns** - Integrates with your `db_manager.py` and Alembic setup
- **Leverages your seeding system** - Uses your idempotent `seed_database()` function
- **Follows your environment patterns** - Respects your `.env` configuration and `DATABASE_URL` setup
- **Integrates with your FastAPI app** - Can check if your server is running and healthy
- **Follows your service dependency injection patterns** - Uses consistent `get_*_service()` functions across all services

### Key Features & Time Savings

#### One-Command Environment Setup
```bash
# Complete fresh environment setup
python scripts/dev_manager.py setup
# Time saved: 15-20 minutes of manual migration + seeding + verification
```

This single command:
1. Validates environment configuration
2. Runs database migrations
3. Seeds database with sample data
4. Performs health checks
5. Verifies everything is working

#### Environment Validation
```bash
# Validate development environment
python scripts/dev_manager.py validate-env
# Time saved: 10-15 minutes of manual environment checking
```

Checks:
- Required environment variables
- Python dependencies
- Database connectivity
- File permissions
- System resources

#### Quick Data Inspection
```bash
# Inspect data in any table
python scripts/dev_manager.py inspect users
# Time saved: 5-10 minutes of manual SQL queries for data verification
```

#### Health Monitoring
```bash
# Comprehensive health check
python scripts/dev_manager.py health
# Time saved: 15-20 minutes of manual system and database checks
```

#### Development Reset
```bash
# Reset to clean development state
python scripts/dev_manager.py reset-dev
# Time saved: 10-15 minutes of manual cleanup and re-setup
```

### **Total Time Savings: 1-2 hours per day** for environment management tasks

## 📊 Combined Workflow Benefits

### Daily Development Workflow
```bash
# Morning setup (2 minutes instead of 20)
python scripts/dev_manager.py setup

# Quick health check (30 seconds instead of 5 minutes)
python scripts/dev_manager.py health

# Data inspection during development (1 minute instead of 5)
python scripts/dev_manager.py inspect policies

# Backup before major changes (1 minute instead of 10)
python scripts/enhanced_db_manager.py backup "before_new_feature"
```

### Testing Workflow
```bash
# Generate comprehensive tests (5 minutes instead of 4 hours)
python scripts/test_generator.py generate-all

# Run tests with pytest
pytest src/tests/
```

### Database Management Workflow
```bash
# Schema analysis (2 minutes instead of 20)
python scripts/enhanced_db_manager.py compare-schema

# Performance monitoring (1 minute instead of 15)
python scripts/enhanced_db_manager.py performance

# Data export for analysis (1 minute instead of 10)
python scripts/enhanced_db_manager.py export-data users
```

## 🎯 Integration Points with Existing Codebase

### Database Layer Integration
- **Uses your `db_connector.py`** for database sessions
- **Leverages your `seeder.py`** for test data generation
- **Respects your Alembic configuration** for migrations
- **Follows your entity models** in `src/app/dao/entity/`

### API Layer Integration
- **Uses your route constants** from `app_routes.py`
- **Follows your schema patterns** from `src/app/api/schema/`
- **Respects your service layer** architecture
- **Integrates with your dependency injection** patterns

## 🔧 Service Dependency Injection Patterns

### Overview
All services now follow a consistent dependency injection pattern for better testability and maintainability.

### Service Dependency Functions
Each service has a corresponding dependency injection function:

```python
# User Service (existing pattern)
from src.app.api.service.user_service import get_user_service, UserService

# API Key Service (newly implemented)
from src.app.api.service.api_key_service import get_api_key_service, ApiKeyService

# Tenant Service (newly implemented)
from src.app.api.service.tenant_service import get_tenant_service, TenantService

# Log Entry Service (newly implemented)
from src.app.api.service.log_entry_service import get_log_entry_service, LogEntryService

# Policy Service (existing)
from src.app.api.service.policy_service import get_policy_service, PolicyService

# Rule Service (existing)
from src.app.api.service.rule_service import get_rule_service, RuleService
```

### Router Implementation Pattern
All routers follow the same dependency injection pattern:

```python
@router.post(
    "",
    response_model=CreateResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_entity(
        *,
        service: EntityService = Depends(get_entity_service),
        payload: CreateRequest
):
    try:
        result = await service.create_entity(payload)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
```

### Benefits of This Pattern
- **Consistent Architecture**: All services follow the same injection pattern
- **Easy Testing**: Services can be easily mocked in tests
- **Better Separation**: Clear separation between routing and business logic
- **Database Session Management**: Automatic session lifecycle management
- **Error Handling**: Consistent error handling across all endpoints

### Usage Examples

#### **Creating a New Service**
```python
# 1. In your service file (e.g., my_service.py)
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from src.app.core.db_connection.db_connector import get_session

async def get_my_service(
    session: AsyncSession = Depends(get_session)
) -> "MyService":
    return MyService(session)

class MyService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def my_method(self):
        # Your business logic here
        pass
```

#### **Using Service in Router**
```python
# 2. In your router file (e.g., my_router.py)
from fastapi import APIRouter, Depends
from src.app.api.service.my_service import get_my_service, MyService

@router.get("/my-endpoint")
async def my_endpoint(
    *,
    service: MyService = Depends(get_my_service),
):
    return await service.my_method()
```

#### **Testing with Dependency Override**
```python
# 3. In your test file
def test_my_endpoint():
    # Mock the service
    mock_service = Mock()
    app.dependency_overrides[get_my_service] = lambda: mock_service

    # Test your endpoint
    response = client.get("/my-endpoint")
    assert response.status_code == 200
```

### Testing Integration
- **Extends your existing `test_main.py`** patterns
- **Uses your TestClient setup** from FastAPI
- **Leverages your seeding data** for fixtures
- **Follows your pytest configuration**

## 📈 Estimated Total Time Savings

| Utility | Daily Savings | Weekly Savings | Monthly Savings |
|---------|---------------|----------------|-----------------|
| Enhanced DB Manager | 30-45 min | 3-5 hours | 12-20 hours |
| API Test Generator | 15-30 min | 2-3 hours | 8-12 hours |
| Dev Environment Manager | 45-60 min | 5-7 hours | 20-28 hours |
| **Total** | **1.5-2.25 hours** | **10-15 hours** | **40-60 hours** |

## 🚀 Getting Started

1. **Start with Environment Setup**:
   ```bash
   python scripts/dev_manager.py setup
   ```

2. **Generate Tests for Your Entities**:
   ```bash
   python scripts/test_generator.py generate-all
   ```

3. **Create Your First Backup**:
   ```bash
   python scripts/enhanced_db_manager.py backup "initial_setup"
   ```

4. **Run Health Checks**:
   ```bash
   python scripts/dev_manager.py health
   ```

These utilities are designed to integrate seamlessly with your existing patterns while providing significant time savings and improved development workflow efficiency! 🎉
