# SecuriNest Authentication System

## Overview

The SecuriNest authentication system implements a secure, stateless JWT-based authentication flow using Key<PERSON>loak as the identity provider. The system follows OAuth2 Authorization Code Flow with PKCE-like security measures and provides maximum security through httpOnly cookies and short-lived access tokens.

## Architecture

### Components

1. **Keycloak Integration**: Multi-tenant identity provider
2. **JWT Token Management**: Stateless authentication with RS256 signatures
3. **Session State Manager**: Secure OAuth2 state management
4. **Authentication Service**: Core business logic
5. **Authentication Router**: REST API endpoints
6. **Security Utilities**: Cryptographic operations

### Security Features

- ✅ **OAuth2 Authorization Code Flow**: Industry standard authentication
- ✅ **CSRF Protection**: Cryptographically signed state parameters
- ✅ **XSS Protection**: httpOnly cookies for refresh tokens
- ✅ **Token Rotation**: Short-lived access tokens (30 minutes)
- ✅ **Secure Cookies**: SameSite, Secure, httpOnly flags
- ✅ **Multi-tenant Support**: Tenant-specific Keycloak realms
- ✅ **Thread Safety**: Async-safe concurrent operations
- ✅ **Stateless Architecture**: No database sessions

## Authentication Flow

```mermaid
sequenceDiagram
    participant Frontend
    participant Backend
    participant Keycloak
    participant StateManager

    Note over Frontend,StateManager: Login Initiation
    Frontend->>Backend: POST /auth/login {tenant_id}
    Backend->>StateManager: Store state-realm mapping
    Backend->>Backend: Generate state & nonce
    Backend->>Frontend: Return authorization URL

    Note over Frontend,StateManager: User Authentication
    Frontend->>Keycloak: Redirect to authorization URL
    Keycloak->>Keycloak: User authenticates
    Keycloak->>Backend: GET /auth/callback?code=...&state=...

    Note over Frontend,StateManager: Token Exchange
    Backend->>StateManager: Retrieve & validate state
    Backend->>Keycloak: Exchange code for tokens
    Keycloak->>Backend: Access + refresh tokens
    Backend->>Backend: Set httpOnly cookie
    Backend->>Frontend: Return user info + access token

    Note over Frontend,StateManager: API Requests
    Frontend->>Backend: API calls with Bearer token
    Backend->>Backend: Validate JWT (stateless)
    Backend->>Frontend: API responses

    Note over Frontend,StateManager: Token Refresh
    Frontend->>Backend: POST /auth/refresh (httpOnly cookie)
    Backend->>Keycloak: Refresh token exchange
    Backend->>Frontend: New access token

    Note over Frontend,StateManager: Logout
    Frontend->>Backend: POST /auth/logout
    Backend->>Keycloak: Invalidate session
    Backend->>Backend: Clear cookies
    Backend->>Frontend: Logout confirmation
```

## API Endpoints

### POST /auth/login
Initiate OAuth2 login flow for a specific tenant.

**Request:**
```json
{
  "tenant_id": "tenant-123",
  "redirect_uri": "https://app.example.com/dashboard"
}
```

**Response:**
```json
{
  "authorization_url": "https://keycloak.example.com/realms/tenant-123/protocol/openid-connect/auth?...",
  "state": "encrypted-state-parameter",
  "realm_name": "tenant-123"
}
```

### GET /auth/callback
Handle OAuth2 callback and exchange authorization code for tokens.

**Query Parameters:**
- `code`: Authorization code from Keycloak
- `state`: OAuth2 state parameter
- `error`: Optional error code
- `error_description`: Optional error description

**Response:**
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIs...",
  "token_type": "Bearer",
  "expires_in": 1800,
  "user_id": "user-123",
  "username": "john.doe",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "realm": "tenant-123",
  "tenant_id": "123",
  "roles": ["TENANT_ADMIN", "DEVELOPER"]
}
```

### POST /auth/refresh
Refresh access token using refresh token from httpOnly cookie.

**Response:**
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIs...",
  "token_type": "Bearer",
  "expires_in": 1800
}
```

### POST /auth/logout
Logout user and invalidate all tokens.

**Request:**
```json
{
  "redirect_uri": "https://app.example.com/login"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully logged out",
  "logout_url": "https://keycloak.example.com/realms/tenant-123/protocol/openid-connect/logout?..."
}
```

### GET /auth/health
Check authentication service health.

**Response:**
```json
{
  "status": "healthy",
  "service": "authentication",
  "session_state_manager": {
    "total_entries": 42,
    "max_size": 10000,
    "ttl_minutes": 10
  },
  "message": "Authentication service is operational"
}
```

## Configuration

### Required Environment Variables

```bash
# Keycloak Configuration
KEYCLOAK_URL=https://keycloak.example.com
KEYCLOAK_ADMIN_USER=admin
KEYCLOAK_ADMIN_PASSWORD=secure-password
KEYCLOAK_CLIENT_SECRET=client-secret
KEYCLOAK_MASTER_REALM=master
BACKEND_URL=https://api.example.com

# JWT Configuration
JWT_ALGORITHM=RS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_HOURS=24

# Authentication Security
AUTH_STATE_SECRET=32-character-secret
AUTH_NONCE_SECRET=32-character-secret

# Cookie Configuration
AUTH_COOKIE_SECURE=true
AUTH_COOKIE_SAMESITE=strict
AUTH_COOKIE_DOMAIN=example.com
```

### Security Recommendations

1. **Secrets Management**:
   - Use 32+ character random secrets
   - Rotate secrets regularly
   - Store in secure environment variables

2. **Cookie Security**:
   - Set `AUTH_COOKIE_SECURE=true` in production
   - Use `AUTH_COOKIE_SAMESITE=strict` for maximum security
   - Set appropriate domain restrictions

3. **Token Expiration**:
   - Keep access tokens short-lived (15-30 minutes)
   - Refresh tokens can be longer (hours to days)
   - Implement token rotation for enhanced security

4. **Network Security**:
   - Use HTTPS in production
   - Implement proper CORS policies
   - Consider rate limiting on auth endpoints

## Frontend Integration

### Login Flow
```javascript
// 1. Initiate login
const loginResponse = await fetch('/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ tenant_id: 'tenant-123' })
});
const { authorization_url } = await loginResponse.json();

// 2. Redirect user to Keycloak
window.location.href = authorization_url;

// 3. Handle callback (automatic)
// Backend processes callback and sets cookies

// 4. Store access token (memory/sessionStorage)
const accessToken = callbackResponse.access_token;
```

### API Requests
```javascript
// Use access token for API requests
const response = await fetch('/api/protected-endpoint', {
  headers: {
    'Authorization': `Bearer ${accessToken}`
  }
});
```

### Token Refresh
```javascript
// Automatic refresh when access token expires
const refreshResponse = await fetch('/auth/refresh', {
  method: 'POST',
  credentials: 'include' // Include httpOnly cookies
});
const { access_token } = await refreshResponse.json();
```

### Logout
```javascript
// Logout user
const logoutResponse = await fetch('/auth/logout', {
  method: 'POST',
  credentials: 'include',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ redirect_uri: '/login' })
});
const { logout_url } = await logoutResponse.json();

// Redirect to complete logout
window.location.href = logout_url;
```

## Error Handling

### Common Error Scenarios

1. **Invalid Tenant**: 400 Bad Request
2. **Expired State**: 400 Bad Request
3. **Invalid Authorization Code**: 400 Bad Request
4. **No Refresh Token**: 401 Unauthorized
5. **Expired Refresh Token**: 401 Unauthorized
6. **Keycloak Unavailable**: 503 Service Unavailable

### Error Response Format
```json
{
  "detail": "Error description",
  "type": "error_type",
  "code": "ERROR_CODE"
}
```

## Monitoring and Logging

### Key Metrics
- Authentication success/failure rates
- Token refresh frequency
- Session state manager cache hit rates
- Average authentication flow duration

### Log Events
- User login attempts
- Token exchanges
- Refresh token usage
- Logout events
- Security violations (invalid state, etc.)

### Health Checks
- Keycloak connectivity
- Session state manager status
- JWT validation capability
- Database connectivity (for tenant lookup)

## Testing

### Unit Tests
- Authentication service methods
- Session state manager operations
- Auth utilities (state/nonce generation)
- Token validation logic

### Integration Tests
- Complete authentication flows
- Error handling scenarios
- Concurrent user sessions
- Token refresh cycles

### Security Tests
- State parameter validation
- CSRF protection
- XSS prevention
- Token tampering detection

## Deployment Considerations

### Production Checklist
- [ ] HTTPS enabled
- [ ] Secure cookie settings
- [ ] Strong secrets configured
- [ ] Rate limiting implemented
- [ ] Monitoring configured
- [ ] Log aggregation setup
- [ ] Backup authentication method
- [ ] Security headers configured

### Scaling Considerations
- Session state manager is in-memory (consider Redis for multi-instance)
- Keycloak clustering for high availability
- Load balancer session affinity (if needed)
- Database connection pooling

### Security Monitoring
- Failed authentication attempts
- Unusual token refresh patterns
- Geographic anomalies
- Concurrent session detection
