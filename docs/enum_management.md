# PostgreSQL Enum Management Guide

This guide covers the management of PostgreSQL enum types in the SecuriNest application, including why we use a separate enum management approach and how to work with it.

## 🎯 Overview

Due to persistent race condition issues with enum creation in Alembic migrations, we've implemented a **two-phase approach**:

1. **Phase 1**: Database schema uses `String` columns instead of `Enum` columns
2. **Phase 2**: Enums are managed separately via a dedicated utility

## 🚨 Why This Approach?

### Problems with Enum Creation in Migrations

- **Race Conditions**: Multiple migration processes creating enums simultaneously
- **Build Failures**: Intermittent "type already exists" errors blocking CI/CD
- **Complex Error Handling**: Difficult to distinguish real errors from race conditions
- **Migration Reliability**: Migrations should always succeed to ensure deployments work

### Benefits of Separate Enum Management

✅ **Reliable Migrations**: Migrations always succeed, never blocked by enum issues  
✅ **No Race Conditions**: Enum utility handles concurrency properly  
✅ **Flexible Deployment**: Can create enums before or after migrations  
✅ **Better Error Handling**: Clear distinction between schema and enum issues  
✅ **Development Friendly**: Developers can work without enum dependencies  

## 🔧 Enum Management Utility

### Basic Usage

```bash
# Create all application enums
python scripts/enum_manager.py create-all

# List existing enums in database
python scripts/enum_manager.py list

# Verify enums match expected values
python scripts/enum_manager.py verify

# Drop all enums (dangerous!)
python scripts/enum_manager.py drop-all
```

### Integration with Development Workflow

#### **After Fresh Database Setup**
```bash
# 1. Run migrations first
python scripts/db_manager.py migrate

# 2. Create enums
python scripts/enum_manager.py create-all

# 3. Seed database
python scripts/db_manager.py seed
```

#### **Daily Development**
```bash
# Check if enums are properly set up
python scripts/enum_manager.py verify

# If verification fails, recreate them
python scripts/enum_manager.py create-all
```

#### **CI/CD Pipeline**
```bash
# In your deployment script:
python scripts/db_manager.py migrate     # Always succeeds
python scripts/enum_manager.py create-all  # Safe to run multiple times
python scripts/db_manager.py seed       # Optional for staging/dev
```

## 📋 Application Enums

The following enums are defined for the application:

| Enum Name | Values | Usage |
|-----------|--------|-------|
| `region` | `US_EAST`, `US_WEST`, `EU_WEST`, `EU_NORTH` | Tenant region preferences |
| `subscriptionlevel` | `FREE`, `BASIC`, `PROFESSIONAL`, `ENTERPRISE` | Tenant subscription tiers |
| `providertype` | `OPENAI`, `AZURE_OPENAI`, `ANTHROPIC`, `GOOGLE_AI`, `MISTRAL`, `SELF_HOSTED` | AI provider types |
| `apikeystatus` | `ACTIVE`, `REVOKED`, `EXPIRED` | API key status |
| `policystatus` | `ACTIVE`, `DRAFT`, `ARCHIVED` | Policy status |
| `outcome` | `ALLOWED`, `BLOCKED`, `REDACTED` | Request processing outcomes |
| `notificationtype` | `VIOLATION`, `SYSTEM_ALERT`, `USAGE_THRESHOLD` | Notification event types |

## 🔄 Migration Strategy

### Current State (String Columns)

All tables use `String(50)` columns where enums would normally be used:

```sql
-- Example: tenant table
CREATE TABLE tenant (
    id INTEGER PRIMARY KEY,
    name VARCHAR NOT NULL,
    region_preference VARCHAR(50) NOT NULL,  -- Was: region enum
    subscription_level VARCHAR(50) NOT NULL  -- Was: subscriptionlevel enum
);
```

### Future Migration to Enums (Optional)

If you want to convert to actual enum columns later:

1. **Create the enums** using the enum manager
2. **Generate a migration** to alter column types
3. **Update application code** to use enum types in SQLAlchemy models

Example migration for converting to enums:
```python
# In a future migration file
def upgrade():
    # Ensure enums exist
    op.execute("-- Enums should be created via enum_manager.py")
    
    # Convert string columns to enum columns
    op.alter_column('tenant', 'region_preference',
                   type_=sa.Enum('US_EAST', 'US_WEST', 'EU_WEST', 'EU_NORTH', name='region'),
                   postgresql_using='region_preference::region')
```

## 🛠️ Development Guidelines

### Application Code

Since we're using string columns, your SQLAlchemy models should use `String` types:

```python
# In your entity models
class Tenant(SQLModel, table=True):
    id: Optional[int] = Field(primary_key=True)
    name: str
    region_preference: str = Field(max_length=50)  # Not Enum type
    subscription_level: str = Field(max_length=50)  # Not Enum type
```

### Validation

Implement enum validation at the application level:

```python
from enum import Enum

class Region(str, Enum):
    US_EAST = "US_EAST"
    US_WEST = "US_WEST"
    EU_WEST = "EU_WEST"
    EU_NORTH = "EU_NORTH"

# In your Pydantic schemas
class TenantCreate(BaseModel):
    name: str
    region_preference: Region  # Validates enum values
    subscription_level: SubscriptionLevel
```

### Database Constraints (Optional)

You can add check constraints to enforce enum values at the database level:

```sql
-- Example check constraint
ALTER TABLE tenant ADD CONSTRAINT check_region_preference 
CHECK (region_preference IN ('US_EAST', 'US_WEST', 'EU_WEST', 'EU_NORTH'));
```

## 🐛 Troubleshooting

### Enum Manager Issues

**Command fails with connection error:**
```bash
# Check DATABASE_URL is set
echo $DATABASE_URL

# Verify database is accessible
python scripts/db_manager.py status
```

**Enum verification fails:**
```bash
# List what enums actually exist
python scripts/enum_manager.py list

# Recreate all enums
python scripts/enum_manager.py create-all
```

**Enum exists with wrong values:**
```bash
# This indicates a schema mismatch
# You may need to drop and recreate:
python scripts/enum_manager.py drop-all  # DANGEROUS!
python scripts/enum_manager.py create-all
```

### Migration Issues

**Migration still references enum types:**
- Check that all `sa.Enum()` columns have been converted to `sa.String(50)`
- Ensure no enum creation code remains in migration files

**Application expects enum types:**
- Update SQLAlchemy models to use `str` instead of enum types
- Add application-level validation using Pydantic enums

## 📁 File Structure

```
scripts/
├── enum_manager.py          # Enum management utility
├── db_manager.py           # Database migration utility
└── enhanced_db_manager.py  # Advanced database operations

src/app/migrations/versions/
└── 001_add_entities.py     # Schema migration (no enums)

docs/
├── enum_management.md      # This guide
└── database_management.md  # General database guide
```

## 🎯 Best Practices

1. **Always run enum manager after migrations** in deployment scripts
2. **Use application-level validation** for enum values
3. **Keep enum definitions centralized** in the enum manager
4. **Test enum operations** in development before deploying
5. **Monitor enum consistency** using the verify command
6. **Document enum changes** when adding new values

This approach ensures reliable database deployments while maintaining the benefits of enum-like validation in your application.
