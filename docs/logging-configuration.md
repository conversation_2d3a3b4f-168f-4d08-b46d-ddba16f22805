# Logging Configuration

This document describes the logging configuration system for the SecuriNest backend application.

## Overview

The logging system is centrally managed through `src/app/core/config/log_config.py` and provides:

- **Configurable log levels** for the entire application and individual components
- **Console logging** (always enabled) and optional **file logging**
- **Component-specific log level control** to reduce noise from verbose libraries
- **Environment-based configuration** for different deployment environments
- **Rotating file logs** with configurable size limits and backup retention

## Environment Variables

### Core Logging Configuration

Add these variables to your `.env` file to configure logging:

```bash
# Main application log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Custom log format (optional)
# LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# File logging configuration
ENABLE_FILE_LOGGING=false
LOG_FILE_PATH=logs/securinest.log
MAX_LOG_FILE_SIZE_MB=10
LOG_FILE_BACKUP_COUNT=5
```

### Component-Specific Log Levels

Fine-tune logging for specific components:

```bash
# Database components (reduce verbosity)
SQLALCHEMY_LOG_LEVEL=WARNING
SQLALCHEMY_POOL_LOG_LEVEL=WARNING
SQLALCHEMY_DIALECTS_LOG_LEVEL=WARNING

# Web server components
UVICORN_ACCESS_LOG_LEVEL=WARNING
UVICORN_ERROR_LOG_LEVEL=INFO

# HTTP client libraries
HTTPX_LOG_LEVEL=WARNING
HTTPCORE_LOG_LEVEL=WARNING

# Application components (keep visible)
APP_SERVICE_LOG_LEVEL=INFO
CORE_SERVICE_LOG_LEVEL=INFO
SECURITY_LOG_LEVEL=INFO
DATABASE_LOG_LEVEL=INFO
```

## Environment-Specific Examples

### Development Environment

For development, use verbose logging with console output only:

```bash
# Development configuration
LOG_LEVEL=DEBUG
ENABLE_FILE_LOGGING=false
SQLALCHEMY_LOG_LEVEL=INFO
APP_SERVICE_LOG_LEVEL=DEBUG
CORE_SERVICE_LOG_LEVEL=DEBUG
```

### Production Environment

For production, use optimized logging with file output:

```bash
# Production configuration
LOG_LEVEL=INFO
ENABLE_FILE_LOGGING=true
LOG_FILE_PATH=/var/log/securinest/app.log
MAX_LOG_FILE_SIZE_MB=50
LOG_FILE_BACKUP_COUNT=10

# Reduce noise from verbose components
SQLALCHEMY_LOG_LEVEL=ERROR
UVICORN_ACCESS_LOG_LEVEL=ERROR
HTTPX_LOG_LEVEL=ERROR
```

### Testing Environment

For testing, minimize logging output:

```bash
# Testing configuration
LOG_LEVEL=WARNING
ENABLE_FILE_LOGGING=false
SQLALCHEMY_LOG_LEVEL=ERROR
UVICORN_ACCESS_LOG_LEVEL=ERROR
```

## Log Levels

| Level | Description | When to Use |
|-------|-------------|-------------|
| `DEBUG` | Detailed diagnostic information | Development debugging |
| `INFO` | General informational messages | Normal operation tracking |
| `WARNING` | Warning messages for unexpected situations | Potential issues |
| `ERROR` | Error messages for serious problems | Application errors |
| `CRITICAL` | Critical errors that may cause shutdown | System failures |

## File Logging

When `ENABLE_FILE_LOGGING=true`, logs are written to rotating files:

- **Log rotation**: Files are rotated when they reach `MAX_LOG_FILE_SIZE_MB`
- **Backup retention**: Keep `LOG_FILE_BACKUP_COUNT` old log files
- **File naming**: Rotated files are named `securinest.log.1`, `securinest.log.2`, etc.
- **Encoding**: All log files use UTF-8 encoding

## Component Loggers

The system configures specific loggers for different components:

### Database Loggers
- `sqlalchemy.engine`: SQL query execution
- `sqlalchemy.pool`: Connection pool management
- `sqlalchemy.dialects`: Database dialect specifics

### Web Server Loggers
- `uvicorn.access`: HTTP access logs
- `uvicorn.error`: Server error logs

### HTTP Client Loggers
- `httpx`: HTTP client requests
- `httpcore`: Low-level HTTP operations

### Application Loggers
- `src.app.api.service`: API service layer
- `src.app.core.service`: Core business services
- `src.app.core.security`: Security and authentication
- `src.app.core.database`: Database operations

## Usage in Code

The logging configuration is automatically applied when the application starts. In your code, simply use:

```python
import logging

logger = logging.getLogger(__name__)

# Use the logger
logger.info("This is an info message")
logger.error("This is an error message")
logger.debug("This is a debug message")
```

## Troubleshooting

### Logs Not Appearing

1. Check that `LOG_LEVEL` is set appropriately
2. Verify the logger name matches the component configuration
3. Ensure the application logging is configured before other imports

### File Logging Issues

1. Check that the log directory exists and is writable
2. Verify `ENABLE_FILE_LOGGING=true`
3. Check disk space for log file creation

### Too Much/Too Little Logging

1. Adjust `LOG_LEVEL` for overall verbosity
2. Use component-specific log levels to fine-tune output
3. Consider different configurations for different environments

## Integration with Monitoring

For production deployments, consider:

- **Log aggregation**: Use tools like ELK stack, Fluentd, or similar
- **Log monitoring**: Set up alerts for ERROR and CRITICAL level logs
- **Performance monitoring**: Monitor log file sizes and rotation frequency
- **Security**: Ensure log files don't contain sensitive information
