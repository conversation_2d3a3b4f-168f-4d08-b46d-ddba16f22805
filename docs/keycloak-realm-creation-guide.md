# Keycloak Realm Creation Guide

## Overview
This guide explains how Keycloak realms are created in the SecuriNest authentication system and provides troubleshooting steps.

## Realm Creation Workflow

### Step 1: Tenant Registration
```bash
POST /tenants
{
  "organization_name": "Test Organization",
  "industry": "Technology",
  "region_preference": "US_EAST",
  "subscription_level": "ENTERPRISE",
  "admin_email": "<EMAIL>",
  "admin_password": "SecurePassword123!",
  "admin_first_name": "<PERSON>",
  "admin_last_name": "<PERSON><PERSON>"
}
```

**What happens:**
- Tenant created with `PENDING` status
- Admin user created with `email_verified=false`, `is_active=false`
- Verification email sent
- **No Keycloak realm created yet**

### Step 2: Email Verification (Realm Creation Trigger)
```bash
GET /tenants/verify-email?token=<verification_token>
```

**What happens:**
- User email verified and activated
- Tenant status changed to `ACTIVE`
- **Keycloak realm is created here**
- Keycloak client configured
- Realm roles created
- Admin user created in Keycloak
- Welcome email sent

## Keycloak Realm Structure

When a realm is created, it includes:

### Realm Configuration
- **Name**: `{organization-name}-tenant-{tenant_id}` (e.g., `acme-corp-tenant-123`)
- **Fallback Name**: `tenant-{tenant_id}` (if organization name not available)
- **Display Name**: `Tenant Realm: {realm-name}`
- **Settings**: Login with email, brute force protection, etc.

### Client Configuration
- **Client ID**: `securinest`
- **Client Type**: Confidential
- **Authentication Flow**: Standard flow enabled

### Realm Roles
- `TENANT_ADMIN`
- `DEVELOPER` 
- `AUDITOR`
- `POLICY_EDITOR`

### Initial Admin User
- Username: Admin email address
- Email: Admin email address
- Roles: `TENANT_ADMIN`
- Password: Temporary (should be reset on first login)

## Troubleshooting

### No Realms Appearing in Keycloak Admin Console

**Check 1: Email Verification Status**
```sql
SELECT 
    t.id, 
    t.organization_name, 
    t.status,
    u.email,
    u.email_verified,
    u.is_active
FROM tenants t 
JOIN users u ON t.id = u.tenant_id 
WHERE u.role = 'TENANT_ADMIN';
```

**Check 2: Application Logs**
Look for these log entries:
```
Starting Keycloak provisioning for tenant {tenant_id}
Created realm: tenant-{tenant_id}
Created client: securinest
Created realm roles: [TENANT_ADMIN, DEVELOPER, AUDITOR, POLICY_EDITOR]
Created admin user: {admin_email}
Successfully provisioned tenant {tenant_id}
```

**Check 3: Keycloak Connection**
```bash
GET /keycloak/health
```

Should return:
```json
{
  "status": "healthy",
  "keycloak_url": "http://localhost:8080",
  "realms_count": 2,
  "message": "Keycloak connection is working"
}
```

### Common Issues

**Issue 1: Email Verification Token Expired**
- Tokens expire after 24 hours
- Need to create new tenant registration

**Issue 2: Keycloak Connection Failed**
- Check environment variables:
  - `KEYCLOAK_URL`
  - `KEYCLOAK_ADMIN_USER`
  - `KEYCLOAK_ADMIN_PASSWORD`
- Verify Keycloak server is running

**Issue 3: Realm Already Exists**
- Keycloak returns 409 Conflict
- Check if realm was created in previous attempt
- Use Keycloak admin console to verify

## Testing Realm Creation

### Manual Test
1. Create tenant via API
2. Check email for verification link
3. Click verification link
4. Check Keycloak admin console for new realm
5. Verify realm structure matches expected configuration

### Automated Test
```python
async def test_realm_creation_workflow():
    # 1. Create tenant
    tenant_response = await client.post("/tenants", json=tenant_data)
    
    # 2. Simulate email verification
    verification_response = await client.get(
        f"/tenants/verify-email?token={verification_token}"
    )
    
    # 3. Verify realm exists in Keycloak
    realm_info = await client.get(f"/keycloak/tenant/{tenant_id}/info")
    assert realm_info.status_code == 200
```

## Realm Naming Convention

### Format
- **Primary Format**: `{organization-name}-tenant-{tenant-id}`
- **Fallback Format**: `tenant-{tenant-id}` (when organization name unavailable)

### Examples
- Organization: "Acme Corp", Tenant ID: 123 → Realm: `acme-corp-tenant-123`
- Organization: "Tech Solutions Inc.", Tenant ID: 456 → Realm: `tech-solutions-inc-tenant-456`
- No organization name, Tenant ID: 789 → Realm: `tenant-789`

### Name Sanitization Rules
- Converted to lowercase
- Spaces and special characters replaced with hyphens
- Multiple consecutive hyphens collapsed to single hyphen
- Leading/trailing hyphens removed
- Limited to 20 characters for organization part

## Environment Variables Required

```bash
KEYCLOAK_URL=http://localhost:8080
KEYCLOAK_ADMIN_USER=admin
KEYCLOAK_ADMIN_PASSWORD=admin
KEYCLOAK_CLIENT_SECRET=your-client-secret
BACKEND_URL=http://localhost:8000
```

## Next Steps After Realm Creation

1. **User Login**: Users can now authenticate via Keycloak
2. **Role Management**: Assign additional roles as needed
3. **Client Configuration**: Configure frontend applications
4. **SSO Setup**: Enable single sign-on if required
