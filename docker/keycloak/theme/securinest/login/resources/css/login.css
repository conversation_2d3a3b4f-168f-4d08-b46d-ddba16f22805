/* Dark Theme Login */
:root {
    --primary-color: #0078d4;
    --primary-hover: #106ebe;
    --text-color: #ffffff;
    --text-secondary: #b3b3b3;
    --border-color: #404040;
    --background-color: #1a1a1a;
    --card-background: #2d2d2d;
    --error-color: #ff6b6b;
    --success-color: #51cf66;
    --input-background: #3a3a3a;
    --input-focus-background: #404040;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, sans-serif;
    background: var(--background-color);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-pf-page {
    background: transparent;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.card-pf {
    background: var(--card-background);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    border: 1px solid var(--border-color);
    max-width: 440px;
    width: 100%;
    padding: 48px 44px 36px;
    margin: 0;
}

/* Logo Styles */
.login-logo {
    text-align: center;
    margin-bottom: 32px;
}

.login-logo img {
    max-width: 200px;
    height: auto;
    display: block;
    margin: 0 auto;
}

.login-pf-header {
    text-align: center;
    margin-bottom: 36px;
}

.login-pf-header h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 8px 0;
    line-height: 1.3;
}

.login-pf-header p {
    font-size: 15px;
    color: var(--text-secondary);
    margin: 0;
}

/* Form Styles */
.form-group {
    margin-bottom: 24px;
}

.form-group:last-child {
    margin-bottom: 0;
}

label {
    display: block;
    font-size: 15px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    font-size: 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--input-background);
    color: var(--text-color);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, background-color 0.15s ease-in-out;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    background: var(--input-focus-background);
    box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.25);
}

.form-control::placeholder {
    color: var(--text-secondary);
}

/* Button Styles */
.btn {
    padding: 12px 24px;
    font-size: 15px;
    font-weight: 600;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s ease-in-out;
    min-height: 44px;
}

.btn-primary {
    background: var(--primary-color);
    color: #ffffff;
    width: 100%;
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-hover);
}

.btn-primary:disabled {
    background: #666666;
    cursor: not-allowed;
}

.btn-link {
    background: transparent;
    color: var(--primary-color);
    padding: 8px 0;
    text-decoration: none;
    border: none;
    font-size: 15px;
}

.btn-link:hover {
    text-decoration: underline;
    color: var(--primary-hover);
}

/* Alert Styles */
.alert {
    padding: 16px;
    border-radius: 4px;
    margin-bottom: 24px;
    border: 1px solid transparent;
}

.alert-error {
    background: rgba(255, 107, 107, 0.1);
    border-color: var(--error-color);
    color: var(--error-color);
}

.alert-success {
    background: rgba(81, 207, 102, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    border-color: #ffc107;
    color: #ffc107;
}

.alert-info {
    background: rgba(0, 120, 212, 0.1);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Back Button */
.back-button {
    margin-bottom: 24px;
}

.back-button .btn-link {
    display: inline-flex;
    align-items: center;
    font-size: 15px;
    color: var(--primary-color);
    text-decoration: none;
    padding: 8px 0;
}

.back-button .btn-link:hover {
    text-decoration: underline;
}

.back-button .btn-link::before {
    content: '← ';
    margin-right: 8px;
}

/* User Info Display */
.user-info {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 12px;
}

.user-details h3 {
    margin: 0 0 4px 0;
    font-size: 15px;
    font-weight: 600;
    color: var(--text-color);
}

.user-details p {
    margin: 0;
    font-size: 13px;
    color: var(--text-secondary);
}

/* Password Strength Indicator */
.password-strength {
    margin-top: 8px;
    font-size: 13px;
}

.password-strength.weak {
    color: var(--error-color);
}

.password-strength.medium {
    color: #ff8c00;
}

.password-strength.strong {
    color: var(--success-color);
}

/* Responsive Design */
@media (max-width: 480px) {
    .card-pf {
        padding: 36px 24px 24px;
        margin: 16px;
    }
    
    .login-pf-header h1 {
        font-size: 22px;
    }
}

/* Loading State */
.btn.loading {
    position: relative;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Checkbox Styling */
.checkbox {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.checkbox input[type="checkbox"] {
    margin-right: 8px;
    accent-color: var(--primary-color);
}

.checkbox label {
    color: var(--text-color);
    font-weight: 400;
    margin-bottom: 0;
    cursor: pointer;
}

/* Registration Link */
#kc-registration-container {
    text-align: center;
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid var(--border-color);
}

#kc-registration span {
    color: var(--text-secondary);
}

#kc-registration a {
    color: var(--primary-color);
    text-decoration: none;
}

#kc-registration a:hover {
    text-decoration: underline;
    color: var(--primary-hover);
}

/* Error Messages */
.kc-feedback-text,
.pf-c-alert__title,
span[id*="input-error"] {
    color: var(--error-color) !important;
}



/* Additional Dark Theme Overrides */
.login-pf-settings {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

#kc-form-options {
    flex: 1;
}

.login-pf-settings .checkbox {
    margin-bottom: 0;
}

/* Ensure all text elements are properly colored */
* {
    color: inherit;
}

h1, h2, h3, h4, h5, h6 {
    color: var(--text-color);
}

p, span, div {
    color: var(--text-color);
}

/* Hide unwanted title text */
#kc-header,
#kc-header-wrapper {
    display: none !important;
}

/* Focus Management */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

@media (min-width: 1200px) {
  .pf-v5-c-login__container {
    grid-template-areas:
      "main main"
      "footer footer" !important;
    grid-template-columns: var(--pf-v5-c-login__container--xl--GridTemplateColumns);
    justify-content: center;
  }
}

.pf-v5-c-login__container {
  max-width: 600px;
}
