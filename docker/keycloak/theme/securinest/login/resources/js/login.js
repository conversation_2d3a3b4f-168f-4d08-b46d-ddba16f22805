// Microsoft-style Login JavaScript
(function() {
    'use strict';

    document.addEventListener('DOMContentLoaded', function() {
        initializeLogin();
    });

    function initializeLogin() {
        const forms = document.querySelectorAll('form');
        forms.forEach(function(form) {
            form.addEventListener('submit', function(e) {
                const submitBtn = form.querySelector('input[type="submit"], button[type="submit"]');
                if (submitBtn) {
                    submitBtn.classList.add('loading');
                    submitBtn.disabled = true;
                }
            });
        });

        const emailInput = document.querySelector('input[name="username"]');
        if (emailInput) {
            emailInput.addEventListener('blur', validateEmail);
            emailInput.addEventListener('input', clearValidation);
        }

        const passwordInput = document.querySelector('input[name="password"]');
        if (passwordInput) {
            const strengthIndicator = createPasswordStrengthIndicator();
            passwordInput.parentNode.appendChild(strengthIndicator);
            passwordInput.addEventListener('input', function() {
                updatePasswordStrength(passwordInput.value, strengthIndicator);
            });
        }

        const firstInput = document.querySelector('.form-control');
        if (firstInput) {
            firstInput.focus();
        }

        const backButton = document.querySelector('.back-button a');
        if (backButton) {
            backButton.addEventListener('click', function(e) {
                e.preventDefault();
                history.back();
            });
        }

        enhanceAccessibility();
    }

    function validateEmail(e) {
        const input = e.target;
        const email = input.value.trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        clearValidation(e);
        
        if (email && !emailRegex.test(email)) {
            showValidationError(input, 'Please enter a valid email address');
        }
    }

    function clearValidation(e) {
        const input = e.target;
        const existingError = input.parentNode.querySelector('.validation-error');
        if (existingError) {
            existingError.remove();
        }
        input.classList.remove('error');
    }

    function showValidationError(input, message) {
        input.classList.add('error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'validation-error';
        errorDiv.style.cssText = 'color: var(--error-color); font-size: 13px; margin-top: 4px;';
        errorDiv.textContent = message;
        input.parentNode.appendChild(errorDiv);
    }

    function createPasswordStrengthIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'password-strength';
        indicator.style.display = 'none';
        return indicator;
    }

    function updatePasswordStrength(password, indicator) {
        if (!password) {
            indicator.style.display = 'none';
            return;
        }

        indicator.style.display = 'block';
        
        let strength = 0;
        let feedback = [];

        if (password.length >= 8) strength++;
        else feedback.push('at least 8 characters');

        if (/[A-Z]/.test(password)) strength++;
        else feedback.push('an uppercase letter');

        if (/[a-z]/.test(password)) strength++;
        else feedback.push('a lowercase letter');

        if (/\d/.test(password)) strength++;
        else feedback.push('a number');

        if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) strength++;
        else feedback.push('a special character');

        if (strength < 3) {
            indicator.className = 'password-strength weak';
            indicator.textContent = 'Weak password. Include: ' + feedback.slice(0, 2).join(', ');
        } else if (strength < 4) {
            indicator.className = 'password-strength medium';
            indicator.textContent = 'Medium strength password';
        } else {
            indicator.className = 'password-strength strong';
            indicator.textContent = 'Strong password';
        }
    }

    function enhanceAccessibility() {
        const inputs = document.querySelectorAll('input');
        inputs.forEach(function(input) {
            const label = document.querySelector('label[for="' + input.id + '"]');
            if (label && !input.getAttribute('aria-label')) {
                input.setAttribute('aria-label', label.textContent);
            }
        });

        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            if (!alert.getAttribute('role')) {
                alert.setAttribute('role', 'alert');
                alert.setAttribute('aria-live', 'polite');
            }
        });

        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && e.target.tagName === 'BUTTON') {
                e.target.click();
            }
        });
    }

    function getUserInitials(email) {
        if (!email) return '?';
        const parts = email.split('@')[0].split('.');
        if (parts.length >= 2) {
            return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();
        }
        return email.charAt(0).toUpperCase();
    }

    window.KeycloakTheme = {
        getUserInitials: getUserInitials,
        validateEmail: function(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
    };
})();