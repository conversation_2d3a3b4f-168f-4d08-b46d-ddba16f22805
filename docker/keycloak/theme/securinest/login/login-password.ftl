<#import "template.ftl" as layout>
<@layout.registrationLayout displayMessage=!messagesPerField.existsError('password') displayInfo=false; section>
    <#if section = "header">
        <div class="login-logo">
            <img src="${url.resourcesPath}/img/logo.svg" alt="SecuriNest Logo" />
        </div>
    <#elseif section = "form">
    <div id="kc-form">
      <div id="kc-form-wrapper">
        
        <!-- User Info Display -->
        <div class="user-info">
            <div class="user-avatar">
                <script>
                    document.write(window.KeycloakTheme ? window.KeycloakTheme.getUserInitials('${login.username!''}') : '${(login.username!'')?substring(0,1)?upper_case}');
                </script>
            </div>
            <div class="user-details">
                <h3>${login.username!''}</h3>
            </div>
        </div>

        <!-- Back Button -->
        <div class="back-button">
            <a href="${url.loginRestartFlowUrl}" class="btn-link">Back</a>
        </div>

        <form id="kc-form-password" onsubmit="login.disabled = true; return true;" action="${url.loginAction}" method="post">
            <input type="hidden" id="username" name="username" value="${login.username!''}"/>
            
            <div class="form-group">
                <label for="password" class="${properties.kcLabelClass!}">${msg("password")}</label>
                <input tabindex="1" 
                       id="password" 
                       class="${properties.kcInputClass!} form-control" 
                       name="password" 
                       type="password" 
                       autofocus 
                       autocomplete="current-password"
                       placeholder="Enter your password"
                       aria-invalid="<#if messagesPerField.existsError('password')>true</#if>"
                />
                <#if messagesPerField.existsError('password')>
                    <span id="input-error-password" class="${properties.kcInputErrorMessageClass!}" aria-live="polite">
                        ${kcSanitize(messagesPerField.get('password'))?no_esc}
                    </span>
                </#if>
            </div>

            <div class="form-group login-pf-settings">
                <div id="kc-form-options">
                    <#if realm.rememberMe>
                        <div class="checkbox">
                            <label>
                                <input tabindex="2" id="rememberMe" name="rememberMe" type="checkbox" <#if login.rememberMe??>checked</#if>> ${msg("rememberMe")}
                            </label>
                        </div>
                    </#if>
                </div>
                <div class="${properties.kcFormOptionsWrapperClass!}">
                    <#if realm.resetPasswordAllowed>
                        <span><a tabindex="4" href="${url.loginResetCredentialsUrl}" class="btn-link">${msg("doForgotPassword")}</a></span>
                    </#if>
                </div>
            </div>

            <div id="kc-form-buttons" class="form-group">
                <input type="hidden" id="id-hidden-input" name="credentialId" <#if auth.selectedCredential?has_content>value="${auth.selectedCredential}"</#if>/>
                <input tabindex="3" 
                       class="${properties.kcButtonClass!} ${properties.kcButtonPrimaryClass!} ${properties.kcButtonBlockClass!} ${properties.kcButtonLargeClass!} btn btn-primary" 
                       name="login" 
                       id="kc-login" 
                       type="submit" 
                       value="${msg("doLogIn")}"/>
            </div>
        </form>
        </div>
    </div>
    </#if>

</@layout.registrationLayout>