services:
  keycloak:
    image: quay.io/keycloak/keycloak:latest
    command: start-dev
    restart: always
    depends_on:
      - postgres
    ports:
      - "8080:8080"
    environment:
      KEYCLOAK_ADMIN : admin
      <PERSON>_ADMIN_PASSWORD: admin@1234
      <PERSON>_DB: postgres
      KC_DB_SCHEMA: public
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: password
      KC_DB_URL: ****************************************
      KC_THEME_LOGIN: securinest

  postgres:
    image: postgres:latest
    restart: always
    volumes:
      - ./postgres_data:/etc/lib/postgresql/data
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: password
 