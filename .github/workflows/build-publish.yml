name: CI
on: [push]
jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write

    # Add PostgreSQL service for testing
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: admin
          POSTGRES_USER: postgres
          POSTGRES_DB: securinest
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    # Set environment variables for CI
    env:
      DATABASE_URL: postgresql+asyncpg://postgres:admin@localhost:5432/securinest
      PYTHONPATH: ${{ github.workspace }}

    steps:
      - uses: actions/checkout@v3
#      - name: Initialize CodeQL
#        uses: github/codeql-action/init@v2
#        with:
#          languages: python
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.13'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Wait for PostgreSQL
        run: |
          until pg_isready -h localhost -p 5432 -U postgres; do
            echo "Waiting for PostgreSQL..."
            sleep 2
          done

      - name: Run database migrations
        run: |
          alembic upgrade head

      - name: Seed test database
        run: |
          python scripts/seed_database.py

      - name: Validate CI setup
        run: |
          python scripts/validate_ci_setup.py

      - name: Run tests
        run: |
          pytest -v --tb=short
#      - name: Autobuild for CodeQL
#        uses: github/codeql-action/autobuild@v2
#      - name: Perform CodeQL analysis
#        uses: github/codeql-action/analyze@v2
