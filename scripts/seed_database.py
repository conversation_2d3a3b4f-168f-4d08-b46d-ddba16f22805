#!/usr/bin/env python3
"""
Simple database seeding script.

This script seeds the database with sample data for development.
It's idempotent - can be run multiple times safely.

Usage:
    python scripts/seed_database.py
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.app.core.database.seeder import seed_database

if __name__ == "__main__":
    print("🌱 Starting database seeding...")
    asyncio.run(seed_database())
    print("✅ Database seeding completed!")
