#!/usr/bin/env python3
"""
Enhanced Database Management Utility Script

This script extends the basic db_manager.py with advanced database operations:
- Schema comparison and drift detection
- Backup and restore capabilities  
- Data export/import utilities
- Migration rollback with confirmation
- Performance monitoring queries
- Database health checks

Usage:
    python scripts/enhanced_db_manager.py backup
    python scripts/enhanced_db_manager.py restore backup_20241201_143022.sql
    python scripts/enhanced_db_manager.py compare-schema
    python scripts/enhanced_db_manager.py export-data users
    python scripts/enhanced_db_manager.py rollback 1
    python scripts/enhanced_db_manager.py health-check
    python scripts/enhanced_db_manager.py performance
"""

import asyncio
import json
import os
import subprocess
import sys
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.app.core.database.seeder import seed_database
from src.app.core.db_connection.db_connector import get_session
from sqlalchemy import text, inspect
from sqlalchemy.ext.asyncio import AsyncSession

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedDatabaseManager:
    """Enhanced database management utility with advanced operations."""
    
    def __init__(self):
        self.project_root = project_root
        self.backup_dir = self.project_root / "backups"
        self.backup_dir.mkdir(exist_ok=True)
        self.exports_dir = self.project_root / "exports"
        self.exports_dir.mkdir(exist_ok=True)
        
    def run_command(self, command: list, description: str, capture_output: bool = True) -> tuple[bool, str]:
        """Run a shell command and return success status and output."""
        logger.info(f"🔄 {description}...")
        try:
            result = subprocess.run(
                command,
                cwd=self.project_root,
                check=True,
                capture_output=capture_output,
                text=True
            )
            logger.info(f"✅ {description} completed successfully")
            return True, result.stdout if capture_output else ""
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ {description} failed")
            if capture_output and e.stderr:
                logger.error(f"Error: {e.stderr}")
            return False, e.stderr if capture_output else ""

    def get_db_connection_params(self) -> Dict[str, str]:
        """Extract database connection parameters from environment."""
        db_url = os.getenv("DATABASE_URL")
        if not db_url:
            raise RuntimeError("DATABASE_URL not set")
            
        # Parse postgresql+asyncpg://user:pass@host:port/dbname
        # Convert to format suitable for pg_dump/pg_restore
        if db_url.startswith("postgresql+asyncpg://"):
            db_url = db_url.replace("postgresql+asyncpg://", "postgresql://")
        
        from urllib.parse import urlparse
        parsed = urlparse(db_url)
        
        return {
            "host": parsed.hostname or "localhost",
            "port": str(parsed.port or 5432),
            "username": parsed.username or "postgres",
            "password": parsed.password or "",
            "database": parsed.path.lstrip("/") if parsed.path else "securinest"
        }

    def backup_database(self, backup_name: Optional[str] = None) -> bool:
        """Create a database backup using pg_dump."""
        if not backup_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{timestamp}.sql"
            
        backup_path = self.backup_dir / backup_name
        
        try:
            db_params = self.get_db_connection_params()
            
            # Set PGPASSWORD environment variable
            env = os.environ.copy()
            env["PGPASSWORD"] = db_params["password"]
            
            command = [
                "pg_dump",
                "-h", db_params["host"],
                "-p", db_params["port"],
                "-U", db_params["username"],
                "-d", db_params["database"],
                "--verbose",
                "--clean",
                "--if-exists",
                "-f", str(backup_path)
            ]
            
            result = subprocess.run(
                command,
                env=env,
                cwd=self.project_root,
                check=True,
                capture_output=True,
                text=True
            )
            
            logger.info(f"✅ Database backup created: {backup_path}")
            logger.info(f"Backup size: {backup_path.stat().st_size / 1024:.1f} KB")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Database backup failed: {e.stderr}")
            return False
        except Exception as e:
            logger.error(f"❌ Database backup failed: {e}")
            return False

    def restore_database(self, backup_file: str) -> bool:
        """Restore database from backup file."""
        backup_path = self.backup_dir / backup_file
        
        if not backup_path.exists():
            logger.error(f"❌ Backup file not found: {backup_path}")
            return False
            
        logger.warning("⚠️  This will replace ALL data in the database!")
        confirmation = input("Type 'YES' to confirm restoration: ")
        if confirmation != "YES":
            logger.info("Restoration cancelled")
            return False
            
        try:
            db_params = self.get_db_connection_params()
            
            # Set PGPASSWORD environment variable
            env = os.environ.copy()
            env["PGPASSWORD"] = db_params["password"]
            
            command = [
                "psql",
                "-h", db_params["host"],
                "-p", db_params["port"],
                "-U", db_params["username"],
                "-d", db_params["database"],
                "-f", str(backup_path)
            ]
            
            result = subprocess.run(
                command,
                env=env,
                cwd=self.project_root,
                check=True,
                capture_output=True,
                text=True
            )
            
            logger.info(f"✅ Database restored from: {backup_path}")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Database restoration failed: {e.stderr}")
            return False
        except Exception as e:
            logger.error(f"❌ Database restoration failed: {e}")
            return False

    def list_backups(self) -> bool:
        """List available backup files."""
        backups = list(self.backup_dir.glob("*.sql"))

        if not backups:
            logger.info("📁 No backup files found")
            return True

        logger.info("📁 Available backup files:")
        for backup in sorted(backups, key=lambda x: x.stat().st_mtime, reverse=True):
            size_kb = backup.stat().st_size / 1024
            mtime = datetime.fromtimestamp(backup.stat().st_mtime)
            logger.info(f"  • {backup.name} ({size_kb:.1f} KB, {mtime.strftime('%Y-%m-%d %H:%M:%S')})")

        return True

    async def compare_schema(self) -> bool:
        """Compare current database schema with SQLModel definitions."""
        logger.info("🔍 Comparing database schema with model definitions...")

        try:
            # Get current database schema using sync engine
            from sqlalchemy import create_engine
            sync_db_url = os.getenv("DATABASE_URL", "").replace("postgresql+asyncpg://", "postgresql://")
            sync_engine = create_engine(sync_db_url)
            inspector = inspect(sync_engine)
            db_tables = inspector.get_table_names()

            # Expected tables from our entities
            expected_tables = {
                'tenant', 'user', 'api_key', 'project', 'policy', 'rule',
                'provider_config', 'notification_event', 'log_entry',
                'alembic_version'
            }

            # Find differences
            missing_tables = expected_tables - set(db_tables)
            extra_tables = set(db_tables) - expected_tables

            if missing_tables:
                logger.warning(f"⚠️  Missing tables: {', '.join(missing_tables)}")

            if extra_tables:
                logger.info(f"ℹ️  Extra tables: {', '.join(extra_tables)}")

            if not missing_tables and not extra_tables:
                logger.info("✅ Schema is in sync with model definitions")

            # Check for column differences in key tables
            for table in ['tenant', 'user', 'policy']:
                if table in db_tables:
                    columns = inspector.get_columns(table)
                    logger.info(f"📋 Table '{table}' has {len(columns)} columns")

            return True

        except Exception as e:
            logger.error(f"❌ Schema comparison failed: {e}")
            return False

    async def export_table_data(self, table_name: str, format: str = "json") -> bool:
        """Export data from a specific table."""
        logger.info(f"📤 Exporting data from table '{table_name}'...")

        try:
            async for session in get_session():
                # Check if table exists
                result = await session.execute(
                    text("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = :table_name)"),
                    {"table_name": table_name}
                )
                table_exists = result.scalar()

                if not table_exists:
                    logger.error(f"❌ Table '{table_name}' does not exist")
                    return False

                # Export data
                result = await session.execute(text(f"SELECT * FROM {table_name}"))
                rows = result.fetchall()
                columns = result.keys()

                if not rows:
                    logger.info(f"ℹ️  Table '{table_name}' is empty")
                    return True

                # Convert to exportable format
                data = []
                for row in rows:
                    row_dict = {}
                    for i, col in enumerate(columns):
                        value = row[i]
                        # Handle datetime and other non-serializable types
                        if hasattr(value, 'isoformat'):
                            value = value.isoformat()
                        elif isinstance(value, bytes):
                            value = value.decode('utf-8', errors='ignore')
                        row_dict[col] = value
                    data.append(row_dict)

                # Save to file
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{table_name}_export_{timestamp}.{format}"
                export_path = self.exports_dir / filename

                if format == "json":
                    with open(export_path, 'w') as f:
                        json.dump(data, f, indent=2, default=str)
                else:
                    logger.error(f"❌ Unsupported export format: {format}")
                    return False

                logger.info(f"✅ Exported {len(data)} rows to: {export_path}")
                break  # Exit the async generator loop

            return True

        except Exception as e:
            logger.error(f"❌ Data export failed: {e}")
            return False

    async def health_check(self) -> bool:
        """Perform comprehensive database health check."""
        logger.info("🏥 Performing database health check...")

        try:
            async for session in get_session():
                # Test basic connectivity
                await session.execute(text("SELECT 1"))
                logger.info("✅ Database connection: OK")

                # Check table counts
                tables = ['tenant', 'user', 'api_key', 'project', 'policy', 'rule']
                for table in tables:
                    try:
                        result = await session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                        count = result.scalar()
                        logger.info(f"📊 Table '{table}': {count} records")
                    except Exception as e:
                        logger.warning(f"⚠️  Could not check table '{table}': {e}")

                # Check for orphaned records (basic foreign key validation)
                orphan_checks = [
                    ("user", "tenant_id", "tenant", "id"),
                    ("api_key", "tenant_id", "tenant", "id"),
                    ("project", "tenant_id", "tenant", "id"),
                    ("policy", "tenant_id", "tenant", "id"),
                ]

                for child_table, fk_col, parent_table, pk_col in orphan_checks:
                    try:
                        query = text(f"""
                            SELECT COUNT(*) FROM {child_table} c
                            LEFT JOIN {parent_table} p ON c.{fk_col} = p.{pk_col}
                            WHERE p.{pk_col} IS NULL
                        """)
                        result = await session.execute(query)
                        orphans = result.scalar()
                        if orphans > 0:
                            logger.warning(f"⚠️  Found {orphans} orphaned records in {child_table}")
                        else:
                            logger.info(f"✅ Foreign key integrity '{child_table}.{fk_col}': OK")
                    except Exception as e:
                        logger.warning(f"⚠️  Could not check FK integrity for {child_table}: {e}")

                logger.info("✅ Database health check completed")
                break  # Exit the async generator loop

            return True

        except Exception as e:
            logger.error(f"❌ Database health check failed: {e}")
            return False

    async def performance_report(self) -> bool:
        """Generate database performance report."""
        logger.info("📊 Generating database performance report...")

        try:
            async for session in get_session():
                # Database size
                result = await session.execute(text("SELECT pg_size_pretty(pg_database_size(current_database()))"))
                db_size = result.scalar()
                logger.info(f"💾 Database size: {db_size}")

                # Table sizes
                query = text("""
                    SELECT
                        schemaname,
                        tablename,
                        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                        pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
                    FROM pg_tables
                    WHERE schemaname = 'public'
                    ORDER BY size_bytes DESC
                """)
                result = await session.execute(query)
                tables = result.fetchall()

                logger.info("📋 Table sizes:")
                for table in tables:
                    logger.info(f"  • {table.tablename}: {table.size}")

                # Connection stats
                query = text("""
                    SELECT
                        state,
                        COUNT(*) as count
                    FROM pg_stat_activity
                    WHERE datname = current_database()
                    GROUP BY state
                """)
                result = await session.execute(query)
                connections = result.fetchall()

                logger.info("🔗 Connection states:")
                for conn in connections:
                    logger.info(f"  • {conn.state or 'unknown'}: {conn.count}")

                # Recent query performance (if available)
                try:
                    query = text("""
                        SELECT
                            query,
                            calls,
                            total_exec_time,
                            mean_exec_time
                        FROM pg_stat_statements
                        ORDER BY total_exec_time DESC
                        LIMIT 5
                    """)
                    result = await session.execute(query)
                    queries = result.fetchall()

                    if queries:
                        logger.info("🚀 Top queries by execution time:")
                        for q in queries:
                            logger.info(f"  • {q.query[:50]}... ({q.calls} calls, {q.mean_exec_time:.2f}ms avg)")
                except:
                    logger.info("ℹ️  Query statistics not available (pg_stat_statements not enabled)")

                break  # Exit the async generator loop

            return True

        except Exception as e:
            logger.error(f"❌ Performance report failed: {e}")
            return False

    def rollback_migration(self, steps: int = 1) -> bool:
        """Rollback database migrations."""
        logger.warning(f"⚠️  This will rollback {steps} migration(s)!")
        confirmation = input("Type 'YES' to confirm rollback: ")
        if confirmation != "YES":
            logger.info("Rollback cancelled")
            return False

        # Get current migration
        success, output = self.run_command(["alembic", "current"], "Getting current migration")
        if not success:
            return False

        logger.info(f"Current migration: {output.strip()}")

        # Perform rollback
        if steps == 1:
            target = "-1"
        else:
            target = f"-{steps}"

        return self.run_command(
            ["alembic", "downgrade", target],
            f"Rolling back {steps} migration(s)"
        )[0]

    def show_help(self) -> None:
        """Show help information."""
        help_text = """
🔧 Enhanced Database Manager - Advanced Operations

Available commands:

📊 backup [name]     - Create database backup (optional custom name)
🔄 restore <file>    - Restore database from backup file
📁 list-backups     - List available backup files
🔍 compare-schema   - Compare DB schema with model definitions
📤 export-data <table> - Export table data to JSON
🏥 health-check     - Comprehensive database health check
📊 performance      - Generate performance report
⏪ rollback [steps] - Rollback migrations (default: 1 step)
❓ help            - Show this help message

Examples:
    python scripts/enhanced_db_manager.py backup
    python scripts/enhanced_db_manager.py backup "before_feature_x"
    python scripts/enhanced_db_manager.py restore backup_20241201_143022.sql
    python scripts/enhanced_db_manager.py export-data users
    python scripts/enhanced_db_manager.py rollback 2
    python scripts/enhanced_db_manager.py health-check

⚠️  Warning: backup/restore operations require PostgreSQL client tools (pg_dump, psql)
"""
        print(help_text)


async def main():
    """Main entry point."""
    if len(sys.argv) < 2:
        print("❌ Error: No command specified")
        EnhancedDatabaseManager().show_help()
        sys.exit(1)

    command = sys.argv[1].lower()
    db_manager = EnhancedDatabaseManager()

    success = True

    if command == "backup":
        backup_name = sys.argv[2] if len(sys.argv) > 2 else None
        success = db_manager.backup_database(backup_name)

    elif command == "restore":
        if len(sys.argv) < 3:
            print("❌ Error: Backup file name required")
            print("Usage: python scripts/enhanced_db_manager.py restore <backup_file>")
            sys.exit(1)
        backup_file = sys.argv[2]
        success = db_manager.restore_database(backup_file)

    elif command == "list-backups":
        success = db_manager.list_backups()

    elif command == "compare-schema":
        success = await db_manager.compare_schema()

    elif command == "export-data":
        if len(sys.argv) < 3:
            print("❌ Error: Table name required")
            print("Usage: python scripts/enhanced_db_manager.py export-data <table_name>")
            sys.exit(1)
        table_name = sys.argv[2]
        success = await db_manager.export_table_data(table_name)

    elif command == "health-check":
        success = await db_manager.health_check()

    elif command == "performance":
        success = await db_manager.performance_report()

    elif command == "rollback":
        steps = int(sys.argv[2]) if len(sys.argv) > 2 else 1
        success = db_manager.rollback_migration(steps)

    elif command == "help":
        db_manager.show_help()

    else:
        print(f"❌ Error: Unknown command '{command}'")
        db_manager.show_help()
        sys.exit(1)

    if not success:
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
