#!/usr/bin/env python3
"""
Database Management Utility Script

This script provides convenient commands for database operations:
- migrate: Run Alembic migrations
- seed: Seed database with sample data
- reset: Drop all tables, run migrations, and seed data
- status: Show migration status

Usage:
    python scripts/db_manager.py migrate
    python scripts/db_manager.py seed
    python scripts/db_manager.py reset
    python scripts/db_manager.py status
"""

import asyncio
import sys
import subprocess
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.app.core.database.seeder import seed_database

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DatabaseManager:
    """Database management utility."""
    
    def __init__(self):
        self.project_root = project_root
        
    def run_command(self, command: list, description: str) -> bool:
        """Run a shell command and return success status."""
        logger.info(f"🔄 {description}...")
        try:
            result = subprocess.run(
                command,
                cwd=self.project_root,
                check=True,
                capture_output=True,
                text=True
            )
            logger.info(f"✅ {description} completed successfully")
            if result.stdout:
                logger.info(f"Output: {result.stdout}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ {description} failed")
            logger.error(f"Error: {e.stderr}")
            return False
            
    def migrate(self) -> bool:
        """Run Alembic migrations."""
        return self.run_command(
            ["alembic", "upgrade", "head"],
            "Running database migrations"
        )
        
    def migration_status(self) -> bool:
        """Show current migration status."""
        return self.run_command(
            ["alembic", "current"],
            "Checking migration status"
        )
        
    def create_migration(self, message: str) -> bool:
        """Create a new migration."""
        return self.run_command(
            ["alembic", "revision", "--autogenerate", "-m", message],
            f"Creating migration: {message}"
        )
        
    async def seed(self) -> bool:
        """Seed database with sample data."""
        logger.info("🌱 Seeding database with sample data...")
        try:
            await seed_database()
            logger.info("✅ Database seeding completed successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Database seeding failed: {e}")
            return False
            
    def drop_all_tables(self) -> bool:
        """Drop all tables (dangerous!)."""
        logger.warning("⚠️  This will drop ALL tables in the database!")
        confirmation = input("Type 'YES' to confirm: ")
        if confirmation != "YES":
            logger.info("Operation cancelled")
            return False
            
        # Use Alembic to downgrade to base (removes all tables)
        return self.run_command(
            ["alembic", "downgrade", "base"],
            "Dropping all tables"
        )
        
    async def reset(self) -> bool:
        """Reset database: drop tables, migrate, and seed."""
        logger.info("🔄 Resetting database...")
        
        # Step 1: Drop all tables
        if not self.drop_all_tables():
            return False
            
        # Step 2: Run migrations
        if not self.migrate():
            return False
            
        # Step 3: Seed data
        if not await self.seed():
            return False
            
        logger.info("✅ Database reset completed successfully!")
        return True
        
    def show_help(self):
        """Show help information."""
        help_text = """
Database Manager - Available Commands:

📊 migrate     - Run Alembic migrations to update schema
🌱 seed       - Seed database with sample data (idempotent)
🔄 reset      - Drop all tables, run migrations, and seed data
📋 status     - Show current migration status
🆕 create     - Create a new migration (requires message)
❓ help       - Show this help message

Examples:
    python scripts/db_manager.py migrate
    python scripts/db_manager.py seed
    python scripts/db_manager.py reset
    python scripts/db_manager.py create "add new field to user table"
    python scripts/db_manager.py status

⚠️  Warning: 'reset' command will delete ALL data in the database!
"""
        print(help_text)


async def main():
    """Main entry point."""
    if len(sys.argv) < 2:
        print("❌ Error: No command specified")
        DatabaseManager().show_help()
        sys.exit(1)
        
    command = sys.argv[1].lower()
    db_manager = DatabaseManager()
    
    success = True
    
    if command == "migrate":
        success = db_manager.migrate()
        
    elif command == "seed":
        success = await db_manager.seed()
        
    elif command == "reset":
        success = await db_manager.reset()
        
    elif command == "status":
        success = db_manager.migration_status()
        
    elif command == "create":
        if len(sys.argv) < 3:
            print("❌ Error: Migration message required")
            print("Usage: python scripts/db_manager.py create \"migration message\"")
            sys.exit(1)
        message = sys.argv[2]
        success = db_manager.create_migration(message)
        
    elif command == "help":
        db_manager.show_help()
        
    else:
        print(f"❌ Error: Unknown command '{command}'")
        db_manager.show_help()
        sys.exit(1)
        
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
