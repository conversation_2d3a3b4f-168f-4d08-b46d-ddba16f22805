#!/usr/bin/env python3
"""
Development Environment Manager

This script provides one-command environment setup and management for the SecuriNest project.

Features:
- Fresh database setup (migrate + seed)
- Service health checks
- Log monitoring and analysis
- Quick data inspection commands
- Environment validation
- Development workflow automation

Usage:
    python scripts/dev_manager.py setup
    python scripts/dev_manager.py health
    python scripts/dev_manager.py inspect users
    python scripts/dev_manager.py logs
    python scripts/dev_manager.py validate-env
    python scripts/dev_manager.py reset-dev
"""

import asyncio
import json
import os
import subprocess
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.app.core.database.seeder import seed_database
from src.app.core.db_connection.db_connector import get_session
from sqlalchemy import text

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DevelopmentEnvironmentManager:
    """Manages development environment setup and operations."""
    
    def __init__(self):
        self.project_root = project_root
        self.logs_dir = self.project_root / "logs"
        self.logs_dir.mkdir(exist_ok=True)
        
        # Required environment variables
        self.required_env_vars = [
            "DATABASE_URL",
        ]
        
        # Optional environment variables with defaults
        self.optional_env_vars = {
            "AUTO_SEED_DATABASE": "false",
            "LOG_LEVEL": "INFO",
        }

    def run_command(self, command: list, description: str, capture_output: bool = True) -> tuple[bool, str]:
        """Run a shell command and return success status and output."""
        logger.info(f"🔄 {description}...")
        try:
            result = subprocess.run(
                command,
                cwd=self.project_root,
                check=True,
                capture_output=capture_output,
                text=True
            )
            logger.info(f"✅ {description} completed successfully")
            return True, result.stdout if capture_output else ""
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ {description} failed")
            if capture_output and e.stderr:
                logger.error(f"Error: {e.stderr}")
            return False, e.stderr if capture_output else ""

    def validate_environment(self) -> bool:
        """Validate development environment setup."""
        logger.info("🔍 Validating development environment...")
        
        all_valid = True
        
        # Check required environment variables
        logger.info("📋 Checking required environment variables:")
        for var in self.required_env_vars:
            value = os.getenv(var)
            if value:
                # Mask sensitive values
                display_value = value if var not in ["DATABASE_URL"] else "***"
                logger.info(f"  ✅ {var}: {display_value}")
            else:
                logger.error(f"  ❌ {var}: Not set")
                all_valid = False
        
        # Check optional environment variables
        logger.info("📋 Checking optional environment variables:")
        for var, default in self.optional_env_vars.items():
            value = os.getenv(var, default)
            logger.info(f"  ℹ️  {var}: {value}")
        
        # Check Python dependencies
        logger.info("📦 Checking Python dependencies:")
        required_packages = ["fastapi", "sqlalchemy", "alembic", "pytest", "asyncpg"]
        for package in required_packages:
            try:
                __import__(package)
                logger.info(f"  ✅ {package}: Available")
            except ImportError:
                logger.error(f"  ❌ {package}: Not installed")
                all_valid = False
        
        # Check database connectivity
        logger.info("🗄️  Checking database connectivity:")
        try:
            # Simple database URL validation
            db_url = os.getenv("DATABASE_URL")
            if db_url and "postgresql" in db_url:
                logger.info("  ✅ Database: URL configured")
            else:
                logger.error("  ❌ Database: Invalid or missing DATABASE_URL")
                all_valid = False
        except Exception as e:
            logger.error(f"  ❌ Database: Configuration error - {e}")
            all_valid = False
        
        # Check file permissions
        logger.info("📁 Checking file permissions:")
        critical_dirs = [self.project_root / "src", self.project_root / "scripts"]
        for dir_path in critical_dirs:
            if dir_path.exists() and os.access(dir_path, os.R_OK | os.W_OK):
                logger.info(f"  ✅ {dir_path.name}: Read/Write access")
            else:
                logger.error(f"  ❌ {dir_path.name}: Access denied")
                all_valid = False
        
        if all_valid:
            logger.info("✅ Environment validation passed")
        else:
            logger.error("❌ Environment validation failed")
        
        return all_valid

    async def _test_db_connection(self):
        """Test database connection."""
        async with get_session() as session:
            await session.execute(text("SELECT 1"))

    async def setup_fresh_environment(self) -> bool:
        """Set up a fresh development environment."""
        logger.info("🚀 Setting up fresh development environment...")
        
        try:
            # Step 1: Validate environment
            if not self.validate_environment():
                logger.error("❌ Environment validation failed. Please fix issues before setup.")
                return False
            
            # Step 2: Run migrations
            logger.info("📊 Running database migrations...")
            success, _ = self.run_command(["alembic", "upgrade", "head"], "Database migrations")
            if not success:
                return False
            
            # Step 3: Seed database
            logger.info("🌱 Seeding database with sample data...")
            await seed_database()
            
            # Step 4: Verify setup
            await self.health_check()
            
            logger.info("✅ Fresh development environment setup completed!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Environment setup failed: {e}")
            return False

    async def health_check(self) -> bool:
        """Perform comprehensive health check."""
        logger.info("🏥 Performing development environment health check...")

        try:
            # Database health
            async for session in get_session():
                # Test basic connectivity
                await session.execute(text("SELECT 1"))
                logger.info("✅ Database: Connection OK")
                
                # Check table counts
                tables = ['tenant', 'user', 'api_key', 'project', 'policy']
                for table in tables:
                    try:
                        result = await session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                        count = result.scalar()
                        status = "✅" if count > 0 else "⚠️ "
                        logger.info(f"{status} Table '{table}': {count} records")
                    except Exception as e:
                        logger.warning(f"⚠️  Could not check table '{table}': {e}")

                break  # Exit the async generator loop

            # System resources (basic check without psutil)
            logger.info("💻 System resources:")
            try:
                # Use basic disk space check
                import shutil
                total, used, free = shutil.disk_usage(str(self.project_root))
                disk_percent = (used / total) * 100
                logger.info(f"  • Disk: {disk_percent:.1f}% used ({free // 1024**3} GB free)")
            except:
                logger.info("  • Disk: Unable to check disk usage")

            # Check if FastAPI server is running (using httpx which is already available)
            logger.info("🌐 Service status:")
            try:
                import httpx
                with httpx.Client() as client:
                    response = client.get("http://localhost:8000/", timeout=5)
                    if response.status_code == 200:
                        logger.info("  ✅ FastAPI server: Running")
                    else:
                        logger.warning("  ⚠️  FastAPI server: Unexpected response")
            except:
                logger.info("  ℹ️  FastAPI server: Not running (use 'uvicorn src.main:app --reload' to start)")

            logger.info("✅ Health check completed")
            return True

        except Exception as e:
            logger.error(f"❌ Health check failed: {e}")
            return False

    async def inspect_data(self, table_name: str, limit: int = 10) -> bool:
        """Quick data inspection for a table."""
        logger.info(f"🔍 Inspecting data in table '{table_name}'...")

        try:
            async for session in get_session():
                # Check if table exists
                result = await session.execute(
                    text("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = :table_name)"),
                    {"table_name": table_name}
                )
                table_exists = result.scalar()
                
                if not table_exists:
                    logger.error(f"❌ Table '{table_name}' does not exist")
                    return False
                
                # Get table info
                result = await session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                total_count = result.scalar()
                logger.info(f"📊 Total records: {total_count}")
                
                if total_count == 0:
                    logger.info("ℹ️  Table is empty")
                    return True
                
                # Get sample data
                result = await session.execute(text(f"SELECT * FROM {table_name} LIMIT {limit}"))
                rows = result.fetchall()
                columns = result.keys()
                
                logger.info(f"📋 Sample data (showing {len(rows)} of {total_count} records):")
                logger.info(f"Columns: {', '.join(columns)}")
                
                for i, row in enumerate(rows, 1):
                    row_data = dict(zip(columns, row))
                    # Truncate long values for display
                    display_data = {}
                    for k, v in row_data.items():
                        if isinstance(v, str) and len(v) > 50:
                            display_data[k] = v[:47] + "..."
                        elif isinstance(v, bytes):
                            display_data[k] = f"<bytes: {len(v)} bytes>"
                        else:
                            display_data[k] = v
                    
                    logger.info(f"  {i}. {display_data}")

                break  # Exit the async generator loop

            return True

        except Exception as e:
            logger.error(f"❌ Data inspection failed: {e}")
            return False

    def monitor_logs(self, duration: int = 30) -> bool:
        """Monitor application logs for a specified duration."""
        logger.info(f"📊 Monitoring logs for {duration} seconds...")

        try:
            # Look for common log files
            log_files = []

            # Check for uvicorn logs
            if (self.logs_dir / "uvicorn.log").exists():
                log_files.append(self.logs_dir / "uvicorn.log")

            # Check for application logs
            if (self.logs_dir / "app.log").exists():
                log_files.append(self.logs_dir / "app.log")

            if not log_files:
                logger.info("ℹ️  No log files found. Start the application to generate logs.")
                return True

            logger.info(f"📁 Monitoring log files: {[f.name for f in log_files]}")

            # Simple log monitoring (in a real implementation, you might use tail -f equivalent)
            start_time = time.time()
            while time.time() - start_time < duration:
                for log_file in log_files:
                    try:
                        # Read last few lines
                        with open(log_file, 'r') as f:
                            lines = f.readlines()
                            recent_lines = lines[-5:] if len(lines) > 5 else lines

                            for line in recent_lines:
                                if any(keyword in line.lower() for keyword in ['error', 'exception', 'failed']):
                                    logger.warning(f"🚨 {log_file.name}: {line.strip()}")
                    except:
                        pass

                time.sleep(5)

            logger.info("✅ Log monitoring completed")
            return True

        except Exception as e:
            logger.error(f"❌ Log monitoring failed: {e}")
            return False

    async def reset_development_environment(self) -> bool:
        """Reset development environment to clean state."""
        logger.warning("⚠️  This will reset ALL development data!")
        confirmation = input("Type 'YES' to confirm reset: ")
        if confirmation != "YES":
            logger.info("Reset cancelled")
            return False

        logger.info("🔄 Resetting development environment...")

        try:
            # Step 1: Drop all tables
            success, _ = self.run_command(["alembic", "downgrade", "base"], "Dropping all tables")
            if not success:
                return False

            # Step 2: Run migrations
            success, _ = self.run_command(["alembic", "upgrade", "head"], "Running migrations")
            if not success:
                return False

            # Step 3: Seed database
            logger.info("🌱 Seeding database...")
            await seed_database()

            logger.info("✅ Development environment reset completed!")
            return True

        except Exception as e:
            logger.error(f"❌ Environment reset failed: {e}")
            return False

    def show_status(self) -> bool:
        """Show current development environment status."""
        logger.info("📊 Development Environment Status")
        logger.info("=" * 50)

        # Environment info
        logger.info(f"🏠 Project root: {self.project_root}")
        logger.info(f"🐍 Python version: {sys.version.split()[0]}")

        # Git info (if available)
        try:
            result = subprocess.run(
                ["git", "branch", "--show-current"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                check=True
            )
            current_branch = result.stdout.strip()
            logger.info(f"🌿 Git branch: {current_branch}")
        except:
            logger.info("🌿 Git branch: Not available")

        # Database status
        try:
            # Simple database URL check
            db_url = os.getenv("DATABASE_URL")
            if db_url and "postgresql" in db_url:
                logger.info("🗄️  Database: URL configured")
            else:
                logger.info("🗄️  Database: Not configured")
        except:
            logger.info("🗄️  Database: Not connected")

        # Recent activity
        logger.info("📅 Recent activity:")

        # Check migration files
        migrations_dir = self.project_root / "src" / "app" / "migrations" / "versions"
        if migrations_dir.exists():
            migration_files = list(migrations_dir.glob("*.py"))
            if migration_files:
                latest_migration = max(migration_files, key=lambda x: x.stat().st_mtime)
                mtime = datetime.fromtimestamp(latest_migration.stat().st_mtime)
                logger.info(f"  • Latest migration: {latest_migration.name} ({mtime.strftime('%Y-%m-%d %H:%M')})")

        return True

    def show_help(self) -> None:
        """Show help information."""
        help_text = """
🚀 Development Environment Manager - One-Command Setup & Management

Available commands:

🔧 setup              - Set up fresh development environment (migrate + seed)
🏥 health             - Comprehensive health check
🔍 inspect <table>    - Quick data inspection for a table
📊 logs [duration]    - Monitor application logs (default: 30 seconds)
🔄 reset-dev          - Reset development environment (⚠️  destructive)
📊 status             - Show current environment status
✅ validate-env       - Validate environment configuration
❓ help              - Show this help message

Examples:
    python scripts/dev_manager.py setup
    python scripts/dev_manager.py health
    python scripts/dev_manager.py inspect users
    python scripts/dev_manager.py logs 60
    python scripts/dev_manager.py validate-env
    python scripts/dev_manager.py status

Features:
  • One-command environment setup
  • Comprehensive health monitoring
  • Quick data inspection
  • Log monitoring and analysis
  • Environment validation
  • Development workflow automation

⚠️  Warning: 'reset-dev' command will delete ALL development data!
"""
        print(help_text)


async def main():
    """Main entry point."""
    if len(sys.argv) < 2:
        print("❌ Error: No command specified")
        DevelopmentEnvironmentManager().show_help()
        sys.exit(1)

    command = sys.argv[1].lower()
    dev_manager = DevelopmentEnvironmentManager()

    success = True

    if command == "setup":
        success = await dev_manager.setup_fresh_environment()

    elif command == "health":
        success = await dev_manager.health_check()

    elif command == "inspect":
        if len(sys.argv) < 3:
            print("❌ Error: Table name required")
            print("Usage: python scripts/dev_manager.py inspect <table_name>")
            sys.exit(1)
        table_name = sys.argv[2]
        success = await dev_manager.inspect_data(table_name)

    elif command == "logs":
        duration = int(sys.argv[2]) if len(sys.argv) > 2 else 30
        success = dev_manager.monitor_logs(duration)

    elif command == "reset-dev":
        success = await dev_manager.reset_development_environment()

    elif command == "status":
        success = dev_manager.show_status()

    elif command == "validate-env":
        success = dev_manager.validate_environment()

    elif command == "help":
        dev_manager.show_help()

    else:
        print(f"❌ Error: Unknown command '{command}'")
        dev_manager.show_help()
        sys.exit(1)

    if not success:
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
