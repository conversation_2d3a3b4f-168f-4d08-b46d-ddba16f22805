#!/usr/bin/env python3
"""
CI Setup Validation Script

This script validates that the CI environment is properly configured
for running tests, including database connectivity, migrations, and seeding.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def validate_database_connection():
    """Validate database connection."""
    logger.info("🔍 Validating database connection...")

    try:
        from src.app.core.db_connection.db_connector import get_session
        from sqlalchemy import text

        async for session in get_session():
            # Simple query to test connection
            result = await session.execute(text("SELECT 1"))
            value = result.scalar()
            if value == 1:
                logger.info("✅ Database connection successful")
                return True
            else:
                logger.error("❌ Database connection failed - unexpected result")
                return False
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False


async def validate_database_schema():
    """Validate that database schema exists."""
    logger.info("🔍 Validating database schema...")

    try:
        from src.app.core.db_connection.db_connector import get_session
        from sqlalchemy import text

        async for session in get_session():
            # Check if tenant table exists
            result = await session.execute(
                text("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'tenant')")
            )
            exists = result.scalar()
            if exists:
                logger.info("✅ Database schema exists")
                return True
            else:
                logger.error("❌ Database schema missing - run migrations first")
                return False
    except Exception as e:
        logger.error(f"❌ Database schema validation failed: {e}")
        return False


async def validate_test_data():
    """Validate that test data exists."""
    logger.info("🔍 Validating test data...")
    
    try:
        from src.app.core.db_connection.db_connector import get_session
        from sqlmodel import select
        from src.app.dao.entity import Tenant
        
        async for session in get_session():
            # Check if any tenants exist
            result = await session.execute(select(Tenant))
            tenants = result.scalars().all()
            if tenants:
                logger.info(f"✅ Test data exists ({len(tenants)} tenants found)")
                return True
            else:
                logger.warning("⚠️  No test data found - run seeding script")
                return False
    except Exception as e:
        logger.error(f"❌ Test data validation failed: {e}")
        return False


async def validate_test_fixtures():
    """Validate that test fixtures work."""
    logger.info("🔍 Validating test fixtures...")

    try:
        from src.tests.test_config import test_db_manager
        from sqlalchemy import text

        # Test database manager
        async with test_db_manager.get_test_session() as session:
            result = await session.execute(text("SELECT 1"))
            value = result.scalar()
            if value == 1:
                logger.info("✅ Test fixtures working")
                return True
            else:
                logger.error("❌ Test fixtures failed")
                return False
    except Exception as e:
        logger.error(f"❌ Test fixtures validation failed: {e}")
        return False


def validate_environment():
    """Validate environment variables."""
    logger.info("🔍 Validating environment variables...")
    
    required_vars = ["DATABASE_URL"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    else:
        logger.info("✅ Environment variables configured")
        return True


async def main():
    """Run all validation checks."""
    logger.info("🚀 Starting CI setup validation...")
    
    checks = [
        ("Environment Variables", validate_environment()),
        ("Database Connection", validate_database_connection()),
        ("Database Schema", validate_database_schema()),
        ("Test Data", validate_test_data()),
        ("Test Fixtures", validate_test_fixtures()),
    ]
    
    results = []
    for name, check in checks:
        if asyncio.iscoroutine(check):
            result = await check
        else:
            result = check
        results.append((name, result))
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("📊 VALIDATION SUMMARY")
    logger.info("="*50)
    
    passed = 0
    total = len(results)
    
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{name}: {status}")
        if result:
            passed += 1
    
    logger.info("="*50)
    logger.info(f"Results: {passed}/{total} checks passed")
    
    if passed == total:
        logger.info("🎉 All validation checks passed! CI environment is ready.")
        return 0
    else:
        logger.error("💥 Some validation checks failed. Please fix the issues above.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
