#!/usr/bin/env python3
"""
PostgreSQL Enum Management Utility

This utility manages PostgreSQL enum types separately from migrations to avoid
race condition issues that can cause build failures.

Usage:
    python scripts/enum_manager.py create-all    # Create all required enums
    python scripts/enum_manager.py list          # List existing enums
    python scripts/enum_manager.py verify        # Verify enums match expected values
    python scripts/enum_manager.py drop-all      # Drop all enums (dangerous!)

Features:
- Race condition safe enum creation
- Validates enum values match expectations
- Can be run independently of migrations
- Idempotent operations (safe to run multiple times)
"""

import asyncio
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from sqlalchemy import create_engine, text
from sqlalchemy.exc import ProgrammingError
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnumManager:
    """Manages PostgreSQL enum types for the application."""
    
    # Define all application enums and their values
    ENUMS = {
        'region': ['US_EAST', 'US_WEST', 'EU_WEST', 'EU_NORTH'],
        'subscriptionlevel': ['FREE', 'BASIC', 'PROFESSIONAL', 'ENTERPRISE'],
        'providertype': ['OPENAI', 'AZURE_OPENAI', 'ANTHROPIC', 'GOOGLE_AI', 'MISTRAL', 'SELF_HOSTED'],
        'apikeystatus': ['ACTIVE', 'REVOKED', 'EXPIRED'],
        'policystatus': ['ACTIVE', 'DRAFT', 'ARCHIVED'],
        'outcome': ['ALLOWED', 'BLOCKED', 'REDACTED'],
        'notificationtype': ['VIOLATION', 'SYSTEM_ALERT', 'USAGE_THRESHOLD'],
    }
    
    def __init__(self):
        """Initialize the enum manager with database connection."""
        db_url = os.getenv("DATABASE_URL")
        if not db_url:
            raise RuntimeError("DATABASE_URL environment variable not set")
        
        # Convert async URL to sync URL for this utility
        if db_url.startswith("postgresql+asyncpg://"):
            db_url = db_url.replace("postgresql+asyncpg://", "postgresql://")
        
        self.engine = create_engine(db_url)
        logger.info(f"Connected to database")
    
    def enum_exists(self, enum_name: str) -> bool:
        """Check if an enum type exists in the database."""
        with self.engine.connect() as conn:
            result = conn.execute(text(
                "SELECT EXISTS (SELECT 1 FROM pg_type WHERE typname = :enum_name "
                "AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public'))"
            ), {"enum_name": enum_name})
            return result.scalar()
    
    def get_enum_values(self, enum_name: str) -> Optional[List[str]]:
        """Get the current values of an enum type."""
        if not self.enum_exists(enum_name):
            return None
        
        with self.engine.connect() as conn:
            result = conn.execute(text(
                "SELECT enumlabel FROM pg_enum WHERE enumtypid = "
                "(SELECT oid FROM pg_type WHERE typname = :enum_name "
                "AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) "
                "ORDER BY enumsortorder"
            ), {"enum_name": enum_name})
            return [row[0] for row in result.fetchall()]
    
    def create_enum_safe(self, enum_name: str, enum_values: List[str]) -> bool:
        """
        Safely create an enum type with race condition handling.
        
        Returns True if enum was created or already exists with correct values.
        Returns False if enum exists with different values.
        """
        try:
            with self.engine.connect() as conn:
                # Use a transaction to ensure atomicity
                with conn.begin():
                    values_str = "', '".join(enum_values)
                    conn.execute(text(f"CREATE TYPE {enum_name} AS ENUM ('{values_str}')"))
                    logger.info(f"✅ Created enum '{enum_name}' with values: {enum_values}")
                    return True
        
        except ProgrammingError as e:
            error_msg = str(e).lower()
            if "already exists" in error_msg or "duplicate" in error_msg:
                # Enum already exists - verify it has correct values
                existing_values = self.get_enum_values(enum_name)
                if existing_values and set(existing_values) == set(enum_values):
                    logger.info(f"✅ Enum '{enum_name}' already exists with correct values")
                    return True
                else:
                    logger.error(f"❌ Enum '{enum_name}' exists but has wrong values. "
                               f"Expected: {enum_values}, Found: {existing_values}")
                    return False
            else:
                # Some other error
                logger.error(f"❌ Failed to create enum '{enum_name}': {e}")
                raise
        
        except Exception as e:
            logger.error(f"❌ Unexpected error creating enum '{enum_name}': {e}")
            raise
    
    def create_all_enums(self) -> bool:
        """Create all application enums. Returns True if all succeeded."""
        logger.info("🔧 Creating all application enums...")
        success = True
        
        for enum_name, enum_values in self.ENUMS.items():
            if not self.create_enum_safe(enum_name, enum_values):
                success = False
        
        if success:
            logger.info("✅ All enums created successfully!")
        else:
            logger.error("❌ Some enums failed to create")
        
        return success
    
    def list_enums(self) -> None:
        """List all existing enums and their values."""
        logger.info("📋 Listing existing enums...")
        
        with self.engine.connect() as conn:
            result = conn.execute(text(
                "SELECT typname FROM pg_type WHERE typtype = 'e' "
                "AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public') "
                "ORDER BY typname"
            ))
            existing_enums = [row[0] for row in result.fetchall()]
        
        if not existing_enums:
            logger.info("No enums found in database")
            return
        
        for enum_name in existing_enums:
            values = self.get_enum_values(enum_name)
            status = "✅" if enum_name in self.ENUMS else "⚠️ "
            logger.info(f"{status} {enum_name}: {values}")
    
    def verify_enums(self) -> bool:
        """Verify all application enums exist with correct values."""
        logger.info("🔍 Verifying application enums...")
        success = True
        
        for enum_name, expected_values in self.ENUMS.items():
            if not self.enum_exists(enum_name):
                logger.error(f"❌ Missing enum: {enum_name}")
                success = False
                continue
            
            actual_values = self.get_enum_values(enum_name)
            if set(actual_values) != set(expected_values):
                logger.error(f"❌ Enum '{enum_name}' has wrong values. "
                           f"Expected: {expected_values}, Found: {actual_values}")
                success = False
            else:
                logger.info(f"✅ Enum '{enum_name}' is correct")
        
        if success:
            logger.info("✅ All enums verified successfully!")
        else:
            logger.error("❌ Some enums have issues")
        
        return success
    
    def drop_all_enums(self) -> bool:
        """Drop all application enums. DANGEROUS - use with caution!"""
        logger.warning("⚠️  DROPPING ALL APPLICATION ENUMS - THIS IS DANGEROUS!")
        
        confirmation = input("Type 'YES' to confirm dropping all enums: ")
        if confirmation != "YES":
            logger.info("Operation cancelled")
            return False
        
        success = True
        with self.engine.connect() as conn:
            with conn.begin():
                for enum_name in reversed(list(self.ENUMS.keys())):
                    try:
                        conn.execute(text(f"DROP TYPE IF EXISTS {enum_name} CASCADE"))
                        logger.info(f"🗑️  Dropped enum: {enum_name}")
                    except Exception as e:
                        logger.error(f"❌ Failed to drop enum '{enum_name}': {e}")
                        success = False
        
        return success

def main():
    """Main entry point for the enum manager."""
    if len(sys.argv) != 2:
        print(__doc__)
        sys.exit(1)
    
    command = sys.argv[1]
    manager = EnumManager()
    
    try:
        if command == "create-all":
            success = manager.create_all_enums()
            sys.exit(0 if success else 1)
        
        elif command == "list":
            manager.list_enums()
        
        elif command == "verify":
            success = manager.verify_enums()
            sys.exit(0 if success else 1)
        
        elif command == "drop-all":
            success = manager.drop_all_enums()
            sys.exit(0 if success else 1)
        
        else:
            print(f"Unknown command: {command}")
            print(__doc__)
            sys.exit(1)
    
    except Exception as e:
        logger.error(f"❌ Command failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
